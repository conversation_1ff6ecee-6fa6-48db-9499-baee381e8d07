!function(c){c.add("plugin","specialchars",{translations:{en:{specialchars:"Special Characters"}},init:function(a){this.app=a,this.lang=a.lang,this.toolbar=a.toolbar,this.insertion=a.insertion,this.chars=["&lsquo;","&rsquo;","&ldquo;","&rdquo;","&ndash;","&mdash;","&divide;","&hellip;","&trade;","&bull;","&rarr;","&asymp;","$","&euro;","&cent;","&pound;","&yen;","&iexcl;","&curren;","&brvbar;","&sect;","&uml;","&copy;","&ordf;","&laquo;","&raquo;","&not;","&reg;","&macr;","&deg;","&sup1;","&sup2;","&sup3;","&acute;","&micro;","&para;","&middot;","&cedil;","&ordm;","&frac14;","&frac12;","&frac34;","&iquest;","&Agrave;","&Aacute;","&Acirc;","&Atilde;","&Auml;","&Aring;","&AElig;","&Ccedil;","&Egrave;","&Eacute;","&Ecirc;","&Euml;","&Igrave;","&Iacute;","&Icirc;","&Iuml;","&ETH;","&Ntilde;","&Ograve;","&Oacute;","&Ocirc;","&Otilde;","&Ouml;","&times;","&Oslash;","&Ugrave;","&Uacute;","&Ucirc;","&Uuml;","&Yacute;","&THORN;","&szlig;","&agrave;","&aacute;","&acirc;","&atilde;","&auml;","&aring;","&aelig;","&ccedil;","&egrave;","&eacute;","&ecirc;","&euml;","&igrave;","&iacute;","&icirc;","&iuml;","&eth;","&ntilde;","&ograve;","&oacute;","&ocirc;","&otilde;","&ouml;","&oslash;","&ugrave;","&uacute;","&ucirc;","&uuml;","&yacute;","&thorn;","&yuml;","&OElig;","&oelig;","&#372;","&#374","&#373","&#375;"]},start:function(){var a={title:this.lang.get("specialchars")},t=this._buildDropdown();this.$button=this.toolbar.addButton("specialchars",a),this.$button.setIcon('<i class="re-icon-specialcharacters"></i>'),this.$button.setDropdown(t)},_set:function(a){this.insertion.insertChar(a)},_buildDropdown:function(){function a(a){a.preventDefault();var t=c.dom(a.target);i._set(t.data("char"))}for(var i=this,t=c.dom('<div class="redactor-dropdown-cells">'),r=0;r<this.chars.length;r++){var e=c.dom("<a>");e.attr({href:"#","data-char":this.chars[r]}),e.css({"line-height":"32px",width:"32px",height:"32px"}),e.html(this.chars[r]),e.on("click",a),t.append(e)}return t}})}(Redactor);