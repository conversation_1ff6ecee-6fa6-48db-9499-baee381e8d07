{"version": 3, "file": "bootstrap.esm.js", "sources": ["../../js/src/util/index.js", "../../js/src/dom/data.js", "../../js/src/dom/event-handler.js", "../../js/src/base-component.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/dom/manipulator.js", "../../js/src/dom/selector-engine.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../js/src/dropdown.js", "../../js/src/modal.js", "../../js/src/util/sanitizer.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    const hrefAttr = element.getAttribute('href')\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => (obj[0] || obj).nodeType\n\nconst emulateTransitionEnd = (element, duration) => {\n  let called = false\n  const durationPadding = 5\n  const emulatedDuration = duration + durationPadding\n\n  function listener() {\n    called = true\n    element.removeEventListener(TRANSITION_END, listener)\n  }\n\n  element.addEventListener(TRANSITION_END, listener)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(element)\n    }\n  }, emulatedDuration)\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ?\n      'element' :\n      toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new Error(\n        `${componentName.toUpperCase()}: ` +\n        `Option \"${property}\" provided type \"${valueType}\" ` +\n        `but expected type \"${expectedTypes}\".`)\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!element) {\n    return false\n  }\n\n  if (element.style && element.parentNode && element.parentNode.style) {\n    const elementStyle = getComputedStyle(element)\n    const parentNodeStyle = getComputedStyle(element.parentNode)\n\n    return elementStyle.display !== 'none' &&\n      parentNodeStyle.display !== 'none' &&\n      elementStyle.visibility !== 'hidden'\n  }\n\n  return false\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => function () {}\n\nconst reflow = element => element.offsetHeight\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    document.addEventListener('DOMContentLoaded', callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = document.documentElement.dir === 'rtl'\n\nexport {\n  TRANSITION_END,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  emulateTransitionEnd,\n  typeCheckConfig,\n  isVisible,\n  findShadowRoot,\n  noop,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded,\n  isRTL\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst mapData = (() => {\n  const storeData = {}\n  let id = 1\n  return {\n    set(element, key, data) {\n      if (typeof element.bsKey === 'undefined') {\n        element.bsKey = {\n          key,\n          id\n        }\n        id++\n      }\n\n      storeData[element.bsKey.id] = data\n    },\n    get(element, key) {\n      if (!element || typeof element.bsKey === 'undefined') {\n        return null\n      }\n\n      const keyProperties = element.bsKey\n      if (keyProperties.key === key) {\n        return storeData[keyProperties.id]\n      }\n\n      return null\n    },\n    delete(element, key) {\n      if (typeof element.bsKey === 'undefined') {\n        return\n      }\n\n      const keyProperties = element.bsKey\n      if (keyProperties.key === key) {\n        delete storeData[keyProperties.id]\n        delete element.bsKey\n      }\n    }\n  }\n})()\n\nconst Data = {\n  setData(instance, key, data) {\n    mapData.set(instance, key, data)\n  },\n  getData(instance, key) {\n    return mapData.get(instance, key)\n  },\n  removeData(instance, key) {\n    mapData.delete(instance, key)\n  }\n}\n\nexport default Data\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\nconst nativeEvents = new Set([\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n])\n\n/**\n * ------------------------------------------------------------------------\n * Private methods\n * ------------------------------------------------------------------------\n */\n\nfunction getUidEvent(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getEvent(element) {\n  const uid = getUidEvent(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    event.delegateTarget = element\n\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (let i = domElements.length; i--;) {\n        if (domElements[i] === target) {\n          event.delegateTarget = target\n\n          if (handler.oneOff) {\n            EventHandler.off(element, event.type, fn)\n          }\n\n          return fn.apply(target, [event])\n        }\n      }\n    }\n\n    // To please ESLint\n    return null\n  }\n}\n\nfunction findHandler(events, handler, delegationSelector = null) {\n  const uidEventList = Object.keys(events)\n\n  for (let i = 0, len = uidEventList.length; i < len; i++) {\n    const event = events[uidEventList[i]]\n\n    if (event.originalHandler === handler && event.delegationSelector === delegationSelector) {\n      return event\n    }\n  }\n\n  return null\n}\n\nfunction normalizeParams(originalTypeEvent, handler, delegationFn) {\n  const delegation = typeof handler === 'string'\n  const originalHandler = delegation ? delegationFn : handler\n\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  let typeEvent = originalTypeEvent.replace(stripNameRegex, '')\n  const custom = customEvents[typeEvent]\n\n  if (custom) {\n    typeEvent = custom\n  }\n\n  const isNative = nativeEvents.has(typeEvent)\n\n  if (!isNative) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [delegation, originalHandler, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFn, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  if (!handler) {\n    handler = delegationFn\n    delegationFn = null\n  }\n\n  const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n  const events = getEvent(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFn = findHandler(handlers, originalHandler, delegation ? handler : null)\n\n  if (previousFn) {\n    previousFn.oneOff = previousFn.oneOff && oneOff\n\n    return\n  }\n\n  const uid = getUidEvent(originalHandler, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = delegation ?\n    bootstrapDelegationHandler(element, handler, delegationFn) :\n    bootstrapHandler(element, handler)\n\n  fn.delegationSelector = delegation ? handler : null\n  fn.originalHandler = originalHandler\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, delegation)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  Object.keys(storeElementEvent).forEach(handlerKey => {\n    if (handlerKey.includes(namespace)) {\n      const event = storeElementEvent[handlerKey]\n\n      removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n    }\n  })\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, false)\n  },\n\n  one(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFn) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getEvent(element)\n    const isNamespace = originalTypeEvent.startsWith('.')\n\n    if (typeof originalHandler !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!events || !events[typeEvent]) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, originalHandler, delegation ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      Object.keys(events).forEach(elementEvent => {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n      })\n    }\n\n    const storeElementEvent = events[typeEvent] || {}\n    Object.keys(storeElementEvent).forEach(keyHandlers => {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n      if (!inNamespace || originalTypeEvent.includes(handlerKey)) {\n        const event = storeElementEvent[keyHandlers]\n\n        removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n      }\n    })\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const $ = getjQuery()\n    const typeEvent = event.replace(stripNameRegex, '')\n    const inNamespace = event !== typeEvent\n    const isNative = nativeEvents.has(typeEvent)\n\n    let jQueryEvent\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n    let evt = null\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    if (isNative) {\n      evt = document.createEvent('HTMLEvents')\n      evt.initEvent(typeEvent, bubbles, true)\n    } else {\n      evt = new CustomEvent(event, {\n        bubbles,\n        cancelable: true\n      })\n    }\n\n    // merge custom information in our event\n    if (typeof args !== 'undefined') {\n      Object.keys(args).forEach(key => {\n        Object.defineProperty(evt, key, {\n          get() {\n            return args[key]\n          }\n        })\n      })\n    }\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && typeof jQueryEvent !== 'undefined') {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): base-component.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Data from './dom/data'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst VERSION = '5.0.0-beta1'\n\nclass BaseComponent {\n  constructor(element) {\n    if (!element) {\n      return\n    }\n\n    this._element = element\n    Data.setData(element, this.constructor.DATA_KEY, this)\n  }\n\n  dispose() {\n    Data.removeData(this._element, this.constructor.DATA_KEY)\n    this._element = null\n  }\n\n  /** Static */\n\n  static getInstance(element) {\n    return Data.getData(element, this.DATA_KEY)\n  }\n\n  static get VERSION() {\n    return VERSION\n  }\n}\n\nexport default BaseComponent\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'alert'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst SELECTOR_DISMISS = '[data-bs-dismiss=\"alert\"]'\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASSNAME_ALERT = 'alert'\nconst CLASSNAME_FADE = 'fade'\nconst CLASSNAME_SHOW = 'show'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Alert extends BaseComponent {\n  // Getters\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  close(element) {\n    const rootElement = element ? this._getRootElement(element) : this._element\n    const customEvent = this._triggerCloseEvent(rootElement)\n\n    if (customEvent === null || customEvent.defaultPrevented) {\n      return\n    }\n\n    this._removeElement(rootElement)\n  }\n\n  // Private\n\n  _getRootElement(element) {\n    return getElementFromSelector(element) || element.closest(`.${CLASSNAME_ALERT}`)\n  }\n\n  _triggerCloseEvent(element) {\n    return EventHandler.trigger(element, EVENT_CLOSE)\n  }\n\n  _removeElement(element) {\n    element.classList.remove(CLASSNAME_SHOW)\n\n    if (!element.classList.contains(CLASSNAME_FADE)) {\n      this._destroyElement(element)\n      return\n    }\n\n    const transitionDuration = getTransitionDurationFromElement(element)\n\n    EventHandler.one(element, TRANSITION_END, () => this._destroyElement(element))\n    emulateTransitionEnd(element, transitionDuration)\n  }\n\n  _destroyElement(element) {\n    if (element.parentNode) {\n      element.parentNode.removeChild(element)\n    }\n\n    EventHandler.trigger(element, EVENT_CLOSED)\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n\n      if (!data) {\n        data = new Alert(this)\n      }\n\n      if (config === 'close') {\n        data[config](this)\n      }\n    })\n  }\n\n  static handleDismiss(alertInstance) {\n    return function (event) {\n      if (event) {\n        event.preventDefault()\n      }\n\n      alertInstance.close(this)\n    }\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DISMISS, Alert.handleDismiss(new Alert()))\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Alert to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Alert.jQueryInterface\n    $.fn[NAME].Constructor = Alert\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Alert.jQueryInterface\n    }\n  }\n})\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery, onDOMContentLoaded } from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'button'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"button\"]'\n\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Button extends BaseComponent {\n  // Getters\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n\n      if (!data) {\n        data = new Button(this)\n      }\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n\n  let data = Data.getData(button, DATA_KEY)\n  if (!data) {\n    data = new Button(button)\n  }\n\n  data.toggle()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Button to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Button.jQueryInterface\n    $.fn[NAME].Constructor = Button\n\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Button.jQueryInterface\n    }\n  }\n})\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(val) {\n  if (val === 'true') {\n    return true\n  }\n\n  if (val === 'false') {\n    return false\n  }\n\n  if (val === Number(val).toString()) {\n    return Number(val)\n  }\n\n  if (val === '' || val === 'null') {\n    return null\n  }\n\n  return val\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.to<PERSON><PERSON><PERSON><PERSON>ase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-bs-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-bs-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {}\n\n    Object.keys(element.dataset)\n      .filter(key => key.startsWith('bs'))\n      .forEach(key => {\n        let pureKey = key.replace(/^bs/, '')\n        pureKey = pureKey.charAt(0).toLowerCase() + pureKey.slice(1, pureKey.length)\n        attributes[pureKey] = normalizeData(element.dataset[key])\n      })\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-bs-${normalizeDataKey(key)}`))\n  },\n\n  offset(element) {\n    const rect = element.getBoundingClientRect()\n\n    return {\n      top: rect.top + document.body.scrollTop,\n      left: rect.left + document.body.scrollLeft\n    }\n  },\n\n  position(element) {\n    return {\n      top: element.offsetTop,\n      left: element.offsetLeft\n    }\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NODE_TEXT = 3\n\nconst SelectorEngine = {\n  matches(element, selector) {\n    return element.matches(selector)\n  },\n\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector)\n  },\n\n  children(element, selector) {\n    const children = [].concat(...element.children)\n\n    return children.filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n\n    let ancestor = element.parentNode\n\n    while (ancestor && ancestor.nodeType === Node.ELEMENT_NODE && ancestor.nodeType !== NODE_TEXT) {\n      if (this.matches(ancestor, selector)) {\n        parents.push(ancestor)\n      }\n\n      ancestor = ancestor.parentNode\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (this.matches(next, selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  }\n}\n\nexport default SelectorEngine\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isVisible,\n  reflow,\n  triggerTransitionEnd,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'carousel'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  slide: false,\n  pause: 'hover',\n  wrap: true,\n  touch: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)',\n  keyboard: 'boolean',\n  slide: '(boolean|string)',\n  pause: '(string|boolean)',\n  wrap: 'boolean',\n  touch: 'boolean'\n}\n\nconst DIRECTION_NEXT = 'next'\nconst DIRECTION_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_END = 'carousel-item-end'\nconst CLASS_NAME_START = 'carousel-item-start'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_ITEM = '.active.carousel-item'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_NEXT_PREV = '.carousel-item-next, .carousel-item-prev'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_DATA_SLIDE = '[data-bs-slide], [data-bs-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-bs-ride=\"carousel\"]'\n\nconst PointerType = {\n  TOUCH: 'touch',\n  PEN: 'pen'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\nclass Carousel extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._items = null\n    this._interval = null\n    this._activeElement = null\n    this._isPaused = false\n    this._isSliding = false\n    this.touchTimeout = null\n    this.touchStartX = 0\n    this.touchDeltaX = 0\n\n    this._config = this._getConfig(config)\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._touchSupported = 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n    this._pointerEvent = Boolean(window.PointerEvent)\n\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  next() {\n    if (!this._isSliding) {\n      this._slide(DIRECTION_NEXT)\n    }\n  }\n\n  nextWhenVisible() {\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    if (!this._isSliding) {\n      this._slide(DIRECTION_PREV)\n    }\n  }\n\n  pause(event) {\n    if (!event) {\n      this._isPaused = true\n    }\n\n    if (SelectorEngine.findOne(SELECTOR_NEXT_PREV, this._element)) {\n      triggerTransitionEnd(this._element)\n      this.cycle(true)\n    }\n\n    clearInterval(this._interval)\n    this._interval = null\n  }\n\n  cycle(event) {\n    if (!event) {\n      this._isPaused = false\n    }\n\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n\n    if (this._config && this._config.interval && !this._isPaused) {\n      this._updateInterval()\n\n      this._interval = setInterval(\n        (document.visibilityState ? this.nextWhenVisible : this.next).bind(this),\n        this._config.interval\n      )\n    }\n  }\n\n  to(index) {\n    this._activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeIndex = this._getItemIndex(this._activeElement)\n\n    if (index > this._items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    if (activeIndex === index) {\n      this.pause()\n      this.cycle()\n      return\n    }\n\n    const direction = index > activeIndex ?\n      DIRECTION_NEXT :\n      DIRECTION_PREV\n\n    this._slide(direction, this._items[index])\n  }\n\n  dispose() {\n    super.dispose()\n    EventHandler.off(this._element, EVENT_KEY)\n\n    this._items = null\n    this._config = null\n    this._interval = null\n    this._isPaused = null\n    this._isSliding = null\n    this._activeElement = null\n    this._indicatorsElement = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _handleSwipe() {\n    const absDeltax = Math.abs(this.touchDeltaX)\n\n    if (absDeltax <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltax / this.touchDeltaX\n\n    this.touchDeltaX = 0\n\n    // swipe left\n    if (direction > 0) {\n      this.prev()\n    }\n\n    // swipe right\n    if (direction < 0) {\n      this.next()\n    }\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER, event => this.pause(event))\n      EventHandler.on(this._element, EVENT_MOUSELEAVE, event => this.cycle(event))\n    }\n\n    if (this._config.touch && this._touchSupported) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    const start = event => {\n      if (this._pointerEvent && PointerType[event.pointerType.toUpperCase()]) {\n        this.touchStartX = event.clientX\n      } else if (!this._pointerEvent) {\n        this.touchStartX = event.touches[0].clientX\n      }\n    }\n\n    const move = event => {\n      // ensure swiping with one touch and not pinching\n      if (event.touches && event.touches.length > 1) {\n        this.touchDeltaX = 0\n      } else {\n        this.touchDeltaX = event.touches[0].clientX - this.touchStartX\n      }\n    }\n\n    const end = event => {\n      if (this._pointerEvent && PointerType[event.pointerType.toUpperCase()]) {\n        this.touchDeltaX = event.clientX - this.touchStartX\n      }\n\n      this._handleSwipe()\n      if (this._config.pause === 'hover') {\n        // If it's a touch-enabled device, mouseenter/leave are fired as\n        // part of the mouse compatibility events on first tap - the carousel\n        // would stop cycling until user tapped out of it;\n        // here, we listen for touchend, explicitly pause the carousel\n        // (as if it's the second time we tap on it, mouseenter compat event\n        // is NOT fired) and after a timeout (to allow for mouse compatibility\n        // events to fire) we explicitly restart cycling\n\n        this.pause()\n        if (this.touchTimeout) {\n          clearTimeout(this.touchTimeout)\n        }\n\n        this.touchTimeout = setTimeout(event => this.cycle(event), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n      }\n    }\n\n    SelectorEngine.find(SELECTOR_ITEM_IMG, this._element).forEach(itemImg => {\n      EventHandler.on(itemImg, EVENT_DRAG_START, e => e.preventDefault())\n    })\n\n    if (this._pointerEvent) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => end(event))\n    }\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    switch (event.key) {\n      case ARROW_LEFT_KEY:\n        event.preventDefault()\n        this.prev()\n        break\n      case ARROW_RIGHT_KEY:\n        event.preventDefault()\n        this.next()\n        break\n      default:\n    }\n  }\n\n  _getItemIndex(element) {\n    this._items = element && element.parentNode ?\n      SelectorEngine.find(SELECTOR_ITEM, element.parentNode) :\n      []\n\n    return this._items.indexOf(element)\n  }\n\n  _getItemByDirection(direction, activeElement) {\n    const isNextDirection = direction === DIRECTION_NEXT\n    const isPrevDirection = direction === DIRECTION_PREV\n    const activeIndex = this._getItemIndex(activeElement)\n    const lastItemIndex = this._items.length - 1\n    const isGoingToWrap = (isPrevDirection && activeIndex === 0) ||\n                            (isNextDirection && activeIndex === lastItemIndex)\n\n    if (isGoingToWrap && !this._config.wrap) {\n      return activeElement\n    }\n\n    const delta = direction === DIRECTION_PREV ? -1 : 1\n    const itemIndex = (activeIndex + delta) % this._items.length\n\n    return itemIndex === -1 ?\n      this._items[this._items.length - 1] :\n      this._items[itemIndex]\n  }\n\n  _triggerSlideEvent(relatedTarget, eventDirectionName) {\n    const targetIndex = this._getItemIndex(relatedTarget)\n    const fromIndex = this._getItemIndex(SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element))\n\n    return EventHandler.trigger(this._element, EVENT_SLIDE, {\n      relatedTarget,\n      direction: eventDirectionName,\n      from: fromIndex,\n      to: targetIndex\n    })\n  }\n\n  _setActiveIndicatorElement(element) {\n    if (this._indicatorsElement) {\n      const indicators = SelectorEngine.find(SELECTOR_ACTIVE, this._indicatorsElement)\n\n      for (let i = 0; i < indicators.length; i++) {\n        indicators[i].classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      const nextIndicator = this._indicatorsElement.children[\n        this._getItemIndex(element)\n      ]\n\n      if (nextIndicator) {\n        nextIndicator.classList.add(CLASS_NAME_ACTIVE)\n      }\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = Number.parseInt(element.getAttribute('data-bs-interval'), 10)\n\n    if (elementInterval) {\n      this._config.defaultInterval = this._config.defaultInterval || this._config.interval\n      this._config.interval = elementInterval\n    } else {\n      this._config.interval = this._config.defaultInterval || this._config.interval\n    }\n  }\n\n  _slide(direction, element) {\n    const activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeElementIndex = this._getItemIndex(activeElement)\n    const nextElement = element || (activeElement && this._getItemByDirection(direction, activeElement))\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n    const isCycling = Boolean(this._interval)\n\n    let directionalClassName\n    let orderClassName\n    let eventDirectionName\n\n    if (direction === DIRECTION_NEXT) {\n      directionalClassName = CLASS_NAME_START\n      orderClassName = CLASS_NAME_NEXT\n      eventDirectionName = DIRECTION_LEFT\n    } else {\n      directionalClassName = CLASS_NAME_END\n      orderClassName = CLASS_NAME_PREV\n      eventDirectionName = DIRECTION_RIGHT\n    }\n\n    if (nextElement && nextElement.classList.contains(CLASS_NAME_ACTIVE)) {\n      this._isSliding = false\n      return\n    }\n\n    const slideEvent = this._triggerSlideEvent(nextElement, eventDirectionName)\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      return\n    }\n\n    this._isSliding = true\n\n    if (isCycling) {\n      this.pause()\n    }\n\n    this._setActiveIndicatorElement(nextElement)\n    this._activeElement = nextElement\n\n    if (this._element.classList.contains(CLASS_NAME_SLIDE)) {\n      nextElement.classList.add(orderClassName)\n\n      reflow(nextElement)\n\n      activeElement.classList.add(directionalClassName)\n      nextElement.classList.add(directionalClassName)\n\n      const transitionDuration = getTransitionDurationFromElement(activeElement)\n\n      EventHandler.one(activeElement, TRANSITION_END, () => {\n        nextElement.classList.remove(directionalClassName, orderClassName)\n        nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n        activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n        this._isSliding = false\n\n        setTimeout(() => {\n          EventHandler.trigger(this._element, EVENT_SLID, {\n            relatedTarget: nextElement,\n            direction: eventDirectionName,\n            from: activeElementIndex,\n            to: nextElementIndex\n          })\n        }, 0)\n      })\n\n      emulateTransitionEnd(activeElement, transitionDuration)\n    } else {\n      activeElement.classList.remove(CLASS_NAME_ACTIVE)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      this._isSliding = false\n      EventHandler.trigger(this._element, EVENT_SLID, {\n        relatedTarget: nextElement,\n        direction: eventDirectionName,\n        from: activeElementIndex,\n        to: nextElementIndex\n      })\n    }\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  // Static\n\n  static carouselInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    let _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element)\n    }\n\n    if (typeof config === 'object') {\n      _config = {\n        ..._config,\n        ...config\n      }\n    }\n\n    const action = typeof config === 'string' ? config : _config.slide\n\n    if (!data) {\n      data = new Carousel(element, _config)\n    }\n\n    if (typeof config === 'number') {\n      data.to(config)\n    } else if (typeof action === 'string') {\n      if (typeof data[action] === 'undefined') {\n        throw new TypeError(`No method named \"${action}\"`)\n      }\n\n      data[action]()\n    } else if (_config.interval && _config.ride) {\n      data.pause()\n      data.cycle()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Carousel.carouselInterface(this, config)\n    })\n  }\n\n  static dataApiClickHandler(event) {\n    const target = getElementFromSelector(this)\n\n    if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n      return\n    }\n\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n    const slideIndex = this.getAttribute('data-bs-slide-to')\n\n    if (slideIndex) {\n      config.interval = false\n    }\n\n    Carousel.carouselInterface(target, config)\n\n    if (slideIndex) {\n      Data.getData(target, DATA_KEY).to(slideIndex)\n    }\n\n    event.preventDefault()\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, Carousel.dataApiClickHandler)\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (let i = 0, len = carousels.length; i < len; i++) {\n    Carousel.carouselInterface(carousels[i], Data.getData(carousels[i], DATA_KEY))\n  }\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Carousel to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Carousel.jQueryInterface\n    $.fn[NAME].Constructor = Carousel\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Carousel.jQueryInterface\n    }\n  }\n})\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'collapse'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  toggle: true,\n  parent: ''\n}\n\nconst DefaultType = {\n  toggle: 'boolean',\n  parent: '(string|element)'\n}\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.show, .collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"collapse\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Collapse extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._isTransitioning = false\n    this._config = this._getConfig(config)\n    this._triggerArray = SelectorEngine.find(\n      `${SELECTOR_DATA_TOGGLE}[href=\"#${element.id}\"],` +\n      `${SELECTOR_DATA_TOGGLE}[data-bs-target=\"#${element.id}\"]`\n    )\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggleList.length; i < len; i++) {\n      const elem = toggleList[i]\n      const selector = getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElem => foundElem === element)\n\n      if (selector !== null && filterElement.length) {\n        this._selector = selector\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._parent = this._config.parent ? this._getParent() : null\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._element, this._triggerArray)\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning || this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    let actives\n    let activesData\n\n    if (this._parent) {\n      actives = SelectorEngine.find(SELECTOR_ACTIVES, this._parent)\n        .filter(elem => {\n          if (typeof this._config.parent === 'string') {\n            return elem.getAttribute('data-bs-parent') === this._config.parent\n          }\n\n          return elem.classList.contains(CLASS_NAME_COLLAPSE)\n        })\n\n      if (actives.length === 0) {\n        actives = null\n      }\n    }\n\n    const container = SelectorEngine.findOne(this._selector)\n    if (actives) {\n      const tempActiveData = actives.find(elem => container !== elem)\n      activesData = tempActiveData ? Data.getData(tempActiveData, DATA_KEY) : null\n\n      if (activesData && activesData._isTransitioning) {\n        return\n      }\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    if (actives) {\n      actives.forEach(elemActive => {\n        if (container !== elemActive) {\n          Collapse.collapseInterface(elemActive, 'hide')\n        }\n\n        if (!activesData) {\n          Data.setData(elemActive, DATA_KEY, null)\n        }\n      })\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    if (this._triggerArray.length) {\n      this._triggerArray.forEach(element => {\n        element.classList.remove(CLASS_NAME_COLLAPSED)\n        element.setAttribute('aria-expanded', true)\n      })\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      this.setTransitioning(false)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, TRANSITION_END, complete)\n\n    emulateTransitionEnd(this._element, transitionDuration)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning || !this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    const triggerArrayLength = this._triggerArray.length\n    if (triggerArrayLength > 0) {\n      for (let i = 0; i < triggerArrayLength; i++) {\n        const trigger = this._triggerArray[i]\n        const elem = getElementFromSelector(trigger)\n\n        if (elem && !elem.classList.contains(CLASS_NAME_SHOW)) {\n          trigger.classList.add(CLASS_NAME_COLLAPSED)\n          trigger.setAttribute('aria-expanded', false)\n        }\n      }\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this.setTransitioning(false)\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, TRANSITION_END, complete)\n    emulateTransitionEnd(this._element, transitionDuration)\n  }\n\n  setTransitioning(isTransitioning) {\n    this._isTransitioning = isTransitioning\n  }\n\n  dispose() {\n    super.dispose()\n    this._config = null\n    this._parent = null\n    this._triggerArray = null\n    this._isTransitioning = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(WIDTH) ? WIDTH : HEIGHT\n  }\n\n  _getParent() {\n    let { parent } = this._config\n\n    if (isElement(parent)) {\n      // it's a jQuery object\n      if (typeof parent.jquery !== 'undefined' || typeof parent[0] !== 'undefined') {\n        parent = parent[0]\n      }\n    } else {\n      parent = SelectorEngine.findOne(parent)\n    }\n\n    const selector = `${SELECTOR_DATA_TOGGLE}[data-bs-parent=\"${parent}\"]`\n\n    SelectorEngine.find(selector, parent)\n      .forEach(element => {\n        const selected = getElementFromSelector(element)\n\n        this._addAriaAndCollapsedClass(\n          selected,\n          [element]\n        )\n      })\n\n    return parent\n  }\n\n  _addAriaAndCollapsedClass(element, triggerArray) {\n    if (!element || !triggerArray.length) {\n      return\n    }\n\n    const isOpen = element.classList.contains(CLASS_NAME_SHOW)\n\n    triggerArray.forEach(elem => {\n      if (isOpen) {\n        elem.classList.remove(CLASS_NAME_COLLAPSED)\n      } else {\n        elem.classList.add(CLASS_NAME_COLLAPSED)\n      }\n\n      elem.setAttribute('aria-expanded', isOpen)\n    })\n  }\n\n  // Static\n\n  static collapseInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    const _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (!data && _config.toggle && typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    if (!data) {\n      data = new Collapse(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Collapse.collapseInterface(this, config)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A') {\n    event.preventDefault()\n  }\n\n  const triggerData = Manipulator.getDataAttributes(this)\n  const selector = getSelectorFromElement(this)\n  const selectorElements = SelectorEngine.find(selector)\n\n  selectorElements.forEach(element => {\n    const data = Data.getData(element, DATA_KEY)\n    let config\n    if (data) {\n      // update parent attribute\n      if (data._parent === null && typeof triggerData.parent === 'string') {\n        data._config.parent = triggerData.parent\n        data._parent = data._getParent()\n      }\n\n      config = 'toggle'\n    } else {\n      config = triggerData\n    }\n\n    Collapse.collapseInterface(element, config)\n  })\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Collapse to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Collapse.jQueryInterface\n    $.fn[NAME].Constructor = Collapse\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Collapse.jQueryInterface\n    }\n  }\n})\n\nexport default Collapse\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  getElementFromSelector,\n  isElement,\n  isVisible,\n  isRTL,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'dropdown'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst SPACE_KEY = 'Space'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst REGEXP_KEYDOWN = new RegExp(`${ARROW_UP_KEY}|${ARROW_DOWN_KEY}|${ESCAPE_KEY}`)\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPEND = 'dropend'\nconst CLASS_NAME_DROPSTART = 'dropstart'\nconst CLASS_NAME_NAVBAR = 'navbar'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"dropdown\"]'\nconst SELECTOR_FORM_CHILD = '.dropdown form'\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = isRTL ? 'top-end' : 'top-start'\nconst PLACEMENT_TOPEND = isRTL ? 'top-start' : 'top-end'\nconst PLACEMENT_BOTTOM = isRTL ? 'bottom-end' : 'bottom-start'\nconst PLACEMENT_BOTTOMEND = isRTL ? 'bottom-start' : 'bottom-end'\nconst PLACEMENT_RIGHT = isRTL ? 'left-start' : 'right-start'\nconst PLACEMENT_LEFT = isRTL ? 'right-start' : 'left-start'\n\nconst Default = {\n  offset: 0,\n  flip: true,\n  boundary: 'clippingParents',\n  reference: 'toggle',\n  display: 'dynamic',\n  popperConfig: null\n}\n\nconst DefaultType = {\n  offset: '(number|string|function)',\n  flip: 'boolean',\n  boundary: '(string|element)',\n  reference: '(string|element)',\n  display: 'string',\n  popperConfig: '(null|object)'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Dropdown extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._popper = null\n    this._config = this._getConfig(config)\n    this._menu = this._getMenuElement()\n    this._inNavbar = this._detectNavbar()\n\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const isActive = this._element.classList.contains(CLASS_NAME_SHOW)\n\n    Dropdown.clearMenus()\n\n    if (isActive) {\n      return\n    }\n\n    this.show()\n  }\n\n  show() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    // Totally disable Popper for Dropdowns in Navbar\n    if (!this._inNavbar) {\n      if (typeof Popper === 'undefined') {\n        throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org)')\n      }\n\n      let referenceElement = this._element\n\n      if (this._config.reference === 'parent') {\n        referenceElement = parent\n      } else if (isElement(this._config.reference)) {\n        referenceElement = this._config.reference\n\n        // Check if it's jQuery element\n        if (typeof this._config.reference.jquery !== 'undefined') {\n          referenceElement = this._config.reference[0]\n        }\n      }\n\n      this._popper = Popper.createPopper(referenceElement, this._menu, this._getPopperConfig())\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement &&\n      !parent.closest(SELECTOR_NAVBAR_NAV)) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.on(elem, 'mouseover', null, noop()))\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.toggle(CLASS_NAME_SHOW)\n    this._element.classList.toggle(CLASS_NAME_SHOW)\n    EventHandler.trigger(parent, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || !this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const hideEvent = EventHandler.trigger(parent, EVENT_HIDE, relatedTarget)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.toggle(CLASS_NAME_SHOW)\n    this._element.classList.toggle(CLASS_NAME_SHOW)\n    EventHandler.trigger(parent, EVENT_HIDDEN, relatedTarget)\n  }\n\n  dispose() {\n    super.dispose()\n    EventHandler.off(this._element, EVENT_KEY)\n    this._menu = null\n\n    if (this._popper) {\n      this._popper.destroy()\n      this._popper = null\n    }\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Private\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_CLICK, event => {\n      event.preventDefault()\n      event.stopPropagation()\n      this.toggle()\n    })\n  }\n\n  _getConfig(config) {\n    config = {\n      ...this.constructor.Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...config\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    return config\n  }\n\n  _getMenuElement() {\n    return SelectorEngine.next(this._element, SELECTOR_MENU)[0]\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._element.parentNode\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPEND)) {\n      return PLACEMENT_RIGHT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPSTART)) {\n      return PLACEMENT_LEFT\n    }\n\n    // We need to trim the value because custom properties can also include spaces\n    const isEnd = getComputedStyle(this._menu).getPropertyValue('--bs-position').trim() === 'end'\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      return isEnd ? PLACEMENT_TOPEND : PLACEMENT_TOP\n    }\n\n    return isEnd ? PLACEMENT_BOTTOMEND : PLACEMENT_BOTTOM\n  }\n\n  _detectNavbar() {\n    return this._element.closest(`.${CLASS_NAME_NAVBAR}`) !== null\n  }\n\n  _getPopperConfig() {\n    const popperConfig = {\n      placement: this._getPlacement(),\n      modifiers: [{\n        name: 'preventOverflow',\n        options: {\n          altBoundary: this._config.flip,\n          rootBoundary: this._config.boundary\n        }\n      }]\n    }\n\n    // Disable Popper if we have a static display\n    if (this._config.display === 'static') {\n      popperConfig.modifiers = [{\n        name: 'applyStyles',\n        enabled: false\n      }]\n    }\n\n    return {\n      ...popperConfig,\n      ...this._config.popperConfig\n    }\n  }\n\n  // Static\n\n  static dropdownInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    const _config = typeof config === 'object' ? config : null\n\n    if (!data) {\n      data = new Dropdown(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Dropdown.dropdownInterface(this, config)\n    })\n  }\n\n  static clearMenus(event) {\n    if (event && (event.button === RIGHT_MOUSE_BUTTON || (event.type === 'keyup' && event.key !== TAB_KEY))) {\n      return\n    }\n\n    const toggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggles.length; i < len; i++) {\n      const parent = Dropdown.getParentFromElement(toggles[i])\n      const context = Data.getData(toggles[i], DATA_KEY)\n      const relatedTarget = {\n        relatedTarget: toggles[i]\n      }\n\n      if (event && event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      if (!context) {\n        continue\n      }\n\n      const dropdownMenu = context._menu\n      if (!toggles[i].classList.contains(CLASS_NAME_SHOW)) {\n        continue\n      }\n\n      if (event && ((event.type === 'click' &&\n          /input|textarea/i.test(event.target.tagName)) ||\n          (event.type === 'keyup' && event.key === TAB_KEY)) &&\n          dropdownMenu.contains(event.target)) {\n        continue\n      }\n\n      const hideEvent = EventHandler.trigger(parent, EVENT_HIDE, relatedTarget)\n      if (hideEvent.defaultPrevented) {\n        continue\n      }\n\n      // If this is a touch-enabled device we remove the extra\n      // empty mouseover listeners we added for iOS support\n      if ('ontouchstart' in document.documentElement) {\n        [].concat(...document.body.children)\n          .forEach(elem => EventHandler.off(elem, 'mouseover', null, noop()))\n      }\n\n      toggles[i].setAttribute('aria-expanded', 'false')\n\n      if (context._popper) {\n        context._popper.destroy()\n      }\n\n      dropdownMenu.classList.remove(CLASS_NAME_SHOW)\n      toggles[i].classList.remove(CLASS_NAME_SHOW)\n      EventHandler.trigger(parent, EVENT_HIDDEN, relatedTarget)\n    }\n  }\n\n  static getParentFromElement(element) {\n    return getElementFromSelector(element) || element.parentNode\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not input/textarea:\n    //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n    // If input/textarea:\n    //  - If space key => not a dropdown command\n    //  - If key is other than escape\n    //    - If key is not up or down => not a dropdown command\n    //    - If trigger inside the menu => not a dropdown command\n    if (/input|textarea/i.test(event.target.tagName) ?\n      event.key === SPACE_KEY || (event.key !== ESCAPE_KEY &&\n      ((event.key !== ARROW_DOWN_KEY && event.key !== ARROW_UP_KEY) ||\n        event.target.closest(SELECTOR_MENU))) :\n      !REGEXP_KEYDOWN.test(event.key)) {\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    if (this.disabled || this.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this)\n    const isActive = this.classList.contains(CLASS_NAME_SHOW)\n\n    if (event.key === ESCAPE_KEY) {\n      const button = this.matches(SELECTOR_DATA_TOGGLE) ? this : SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0]\n      button.focus()\n      Dropdown.clearMenus()\n      return\n    }\n\n    if (!isActive || event.key === SPACE_KEY) {\n      Dropdown.clearMenus()\n      return\n    }\n\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, parent).filter(isVisible)\n\n    if (!items.length) {\n      return\n    }\n\n    let index = items.indexOf(event.target)\n\n    // Up\n    if (event.key === ARROW_UP_KEY && index > 0) {\n      index--\n    }\n\n    // Down\n    if (event.key === ARROW_DOWN_KEY && index < items.length - 1) {\n      index++\n    }\n\n    // index is -1 if the first keydown is an ArrowUp\n    index = index === -1 ? 0 : index\n\n    items[index].focus()\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  event.stopPropagation()\n  Dropdown.dropdownInterface(this, 'toggle')\n})\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_FORM_CHILD, e => e.stopPropagation())\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Dropdown to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Dropdown.jQueryInterface\n    $.fn[NAME].Constructor = Dropdown\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Dropdown.jQueryInterface\n    }\n  }\n})\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isVisible,\n  isRTL,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'modal'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  focus: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  focus: 'boolean'\n}\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEUP_DISMISS = `mouseup.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SCROLLBAR_MEASURER = 'modal-scrollbar-measure'\nconst CLASS_NAME_BACKDROP = 'modal-backdrop'\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"modal\"]'\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"modal\"]'\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Modal extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, element)\n    this._backdrop = null\n    this._isShown = false\n    this._isBodyOverflowing = false\n    this._ignoreBackdropClick = false\n    this._isTransitioning = false\n    this._scrollbarWidth = 0\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    if (this._element.classList.contains(CLASS_NAME_FADE)) {\n      this._isTransitioning = true\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (this._isShown || showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n\n    this._checkScrollbar()\n    this._setScrollbar()\n\n    this._adjustDialog()\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, event => this.hide(event))\n\n    EventHandler.on(this._dialog, EVENT_MOUSEDOWN_DISMISS, () => {\n      EventHandler.one(this._element, EVENT_MOUSEUP_DISMISS, event => {\n        if (event.target === this._element) {\n          this._ignoreBackdropClick = true\n        }\n      })\n    })\n\n    this._showBackdrop(() => this._showElement(relatedTarget))\n  }\n\n  hide(event) {\n    if (event) {\n      event.preventDefault()\n    }\n\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    const transition = this._element.classList.contains(CLASS_NAME_FADE)\n\n    if (transition) {\n      this._isTransitioning = true\n    }\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    EventHandler.off(this._dialog, EVENT_MOUSEDOWN_DISMISS)\n\n    if (transition) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, event => this._hideModal(event))\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      this._hideModal()\n    }\n  }\n\n  dispose() {\n    [window, this._element, this._dialog]\n      .forEach(htmlElement => EventHandler.off(htmlElement, EVENT_KEY))\n\n    super.dispose()\n\n    /**\n     * `document` has 2 events `EVENT_FOCUSIN` and `EVENT_CLICK_DATA_API`\n     * Do not move `document` in `htmlElements` array\n     * It will remove `EVENT_CLICK_DATA_API` event that should remain\n     */\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    this._config = null\n    this._dialog = null\n    this._backdrop = null\n    this._isShown = null\n    this._isBodyOverflowing = null\n    this._ignoreBackdropClick = null\n    this._isTransitioning = null\n    this._scrollbarWidth = null\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _showElement(relatedTarget) {\n    const transition = this._element.classList.contains(CLASS_NAME_FADE)\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n\n    if (!this._element.parentNode || this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n      // Don't move modal's DOM position\n      document.body.appendChild(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    if (transition) {\n      reflow(this._element)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    if (this._config.focus) {\n      this._enforceFocus()\n    }\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._element.focus()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    if (transition) {\n      const transitionDuration = getTransitionDurationFromElement(this._dialog)\n\n      EventHandler.one(this._dialog, TRANSITION_END, transitionComplete)\n      emulateTransitionEnd(this._dialog, transitionDuration)\n    } else {\n      transitionComplete()\n    }\n  }\n\n  _enforceFocus() {\n    EventHandler.off(document, EVENT_FOCUSIN) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => {\n      if (document !== event.target &&\n          this._element !== event.target &&\n          !this._element.contains(event.target)) {\n        this._element.focus()\n      }\n    })\n  }\n\n  _setEscapeEvent() {\n    if (this._isShown) {\n      EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n        if (this._config.keyboard && event.key === ESCAPE_KEY) {\n          event.preventDefault()\n          this.hide()\n        } else if (!this._config.keyboard && event.key === ESCAPE_KEY) {\n          this._triggerBackdropTransition()\n        }\n      })\n    } else {\n      EventHandler.off(this._element, EVENT_KEYDOWN_DISMISS)\n    }\n  }\n\n  _setResizeEvent() {\n    if (this._isShown) {\n      EventHandler.on(window, EVENT_RESIZE, () => this._adjustDialog())\n    } else {\n      EventHandler.off(window, EVENT_RESIZE)\n    }\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n    this._showBackdrop(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._resetScrollbar()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _removeBackdrop() {\n    this._backdrop.parentNode.removeChild(this._backdrop)\n    this._backdrop = null\n  }\n\n  _showBackdrop(callback) {\n    const animate = this._element.classList.contains(CLASS_NAME_FADE) ?\n      CLASS_NAME_FADE :\n      ''\n\n    if (this._isShown && this._config.backdrop) {\n      this._backdrop = document.createElement('div')\n      this._backdrop.className = CLASS_NAME_BACKDROP\n\n      if (animate) {\n        this._backdrop.classList.add(animate)\n      }\n\n      document.body.appendChild(this._backdrop)\n\n      EventHandler.on(this._element, EVENT_CLICK_DISMISS, event => {\n        if (this._ignoreBackdropClick) {\n          this._ignoreBackdropClick = false\n          return\n        }\n\n        if (event.target !== event.currentTarget) {\n          return\n        }\n\n        if (this._config.backdrop === 'static') {\n          this._triggerBackdropTransition()\n        } else {\n          this.hide()\n        }\n      })\n\n      if (animate) {\n        reflow(this._backdrop)\n      }\n\n      this._backdrop.classList.add(CLASS_NAME_SHOW)\n\n      if (!animate) {\n        callback()\n        return\n      }\n\n      const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n\n      EventHandler.one(this._backdrop, TRANSITION_END, callback)\n      emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n    } else if (!this._isShown && this._backdrop) {\n      this._backdrop.classList.remove(CLASS_NAME_SHOW)\n\n      const callbackRemove = () => {\n        this._removeBackdrop()\n        callback()\n      }\n\n      if (this._element.classList.contains(CLASS_NAME_FADE)) {\n        const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n        EventHandler.one(this._backdrop, TRANSITION_END, callbackRemove)\n        emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n      } else {\n        callbackRemove()\n      }\n    } else {\n      callback()\n    }\n  }\n\n  _triggerBackdropTransition() {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n\n    if (!isModalOverflowing) {\n      this._element.style.overflowY = 'hidden'\n    }\n\n    this._element.classList.add(CLASS_NAME_STATIC)\n    const modalTransitionDuration = getTransitionDurationFromElement(this._dialog)\n    EventHandler.off(this._element, TRANSITION_END)\n    EventHandler.one(this._element, TRANSITION_END, () => {\n      this._element.classList.remove(CLASS_NAME_STATIC)\n      if (!isModalOverflowing) {\n        EventHandler.one(this._element, TRANSITION_END, () => {\n          this._element.style.overflowY = ''\n        })\n        emulateTransitionEnd(this._element, modalTransitionDuration)\n      }\n    })\n    emulateTransitionEnd(this._element, modalTransitionDuration)\n    this._element.focus()\n  }\n\n  // ----------------------------------------------------------------------\n  // the following methods are used to handle overflowing modals\n  // ----------------------------------------------------------------------\n\n  _adjustDialog() {\n    const isModalOverflowing =\n      this._element.scrollHeight > document.documentElement.clientHeight\n\n    if ((!this._isBodyOverflowing && isModalOverflowing && !isRTL) || (this._isBodyOverflowing && !isModalOverflowing && isRTL)) {\n      this._element.style.paddingLeft = `${this._scrollbarWidth}px`\n    }\n\n    if ((this._isBodyOverflowing && !isModalOverflowing && !isRTL) || (!this._isBodyOverflowing && isModalOverflowing && isRTL)) {\n      this._element.style.paddingRight = `${this._scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  _checkScrollbar() {\n    const rect = document.body.getBoundingClientRect()\n    this._isBodyOverflowing = Math.round(rect.left + rect.right) < window.innerWidth\n    this._scrollbarWidth = this._getScrollbarWidth()\n  }\n\n  _setScrollbar() {\n    if (this._isBodyOverflowing) {\n      // Note: DOMNode.style.paddingRight returns the actual value or '' if not set\n      //   while $(DOMNode).css('padding-right') returns the calculated value or 0 if not set\n\n      // Adjust fixed content padding\n      SelectorEngine.find(SELECTOR_FIXED_CONTENT)\n        .forEach(element => {\n          const actualPadding = element.style.paddingRight\n          const calculatedPadding = window.getComputedStyle(element)['padding-right']\n          Manipulator.setDataAttribute(element, 'padding-right', actualPadding)\n          element.style.paddingRight = `${Number.parseFloat(calculatedPadding) + this._scrollbarWidth}px`\n        })\n\n      // Adjust sticky content margin\n      SelectorEngine.find(SELECTOR_STICKY_CONTENT)\n        .forEach(element => {\n          const actualMargin = element.style.marginRight\n          const calculatedMargin = window.getComputedStyle(element)['margin-right']\n          Manipulator.setDataAttribute(element, 'margin-right', actualMargin)\n          element.style.marginRight = `${Number.parseFloat(calculatedMargin) - this._scrollbarWidth}px`\n        })\n\n      // Adjust body padding\n      const actualPadding = document.body.style.paddingRight\n      const calculatedPadding = window.getComputedStyle(document.body)['padding-right']\n\n      Manipulator.setDataAttribute(document.body, 'padding-right', actualPadding)\n      document.body.style.paddingRight = `${Number.parseFloat(calculatedPadding) + this._scrollbarWidth}px`\n    }\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n  }\n\n  _resetScrollbar() {\n    // Restore fixed content padding\n    SelectorEngine.find(SELECTOR_FIXED_CONTENT)\n      .forEach(element => {\n        const padding = Manipulator.getDataAttribute(element, 'padding-right')\n        if (typeof padding !== 'undefined') {\n          Manipulator.removeDataAttribute(element, 'padding-right')\n          element.style.paddingRight = padding\n        }\n      })\n\n    // Restore sticky content and navbar-toggler margin\n    SelectorEngine.find(`${SELECTOR_STICKY_CONTENT}`)\n      .forEach(element => {\n        const margin = Manipulator.getDataAttribute(element, 'margin-right')\n        if (typeof margin !== 'undefined') {\n          Manipulator.removeDataAttribute(element, 'margin-right')\n          element.style.marginRight = margin\n        }\n      })\n\n    // Restore body padding\n    const padding = Manipulator.getDataAttribute(document.body, 'padding-right')\n    if (typeof padding === 'undefined') {\n      document.body.style.paddingRight = ''\n    } else {\n      Manipulator.removeDataAttribute(document.body, 'padding-right')\n      document.body.style.paddingRight = padding\n    }\n  }\n\n  _getScrollbarWidth() { // thx d.walsh\n    const scrollDiv = document.createElement('div')\n    scrollDiv.className = CLASS_NAME_SCROLLBAR_MEASURER\n    document.body.appendChild(scrollDiv)\n    const scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth\n    document.body.removeChild(scrollDiv)\n    return scrollbarWidth\n  }\n\n  // Static\n\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = {\n        ...Default,\n        ...Manipulator.getDataAttributes(this),\n        ...(typeof config === 'object' && config ? config : {})\n      }\n\n      if (!data) {\n        data = new Modal(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](relatedTarget)\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (this.tagName === 'A' || this.tagName === 'AREA') {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  let data = Data.getData(target, DATA_KEY)\n  if (!data) {\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n\n    data = new Modal(target, config)\n  }\n\n  data.show(this)\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Modal to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Modal.jQueryInterface\n    $.fn[NAME].Constructor = Modal\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Modal.jQueryInterface\n    }\n  }\n})\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttrs = new Set([\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n])\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/gi\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nconst allowedAttribute = (attr, allowedAttributeList) => {\n  const attrName = attr.nodeName.toLowerCase()\n\n  if (allowedAttributeList.includes(attrName)) {\n    if (uriAttrs.has(attrName)) {\n      return Boolean(attr.nodeValue.match(SAFE_URL_PATTERN) || attr.nodeValue.match(DATA_URL_PATTERN))\n    }\n\n    return true\n  }\n\n  const regExp = allowedAttributeList.filter(attrRegex => attrRegex instanceof RegExp)\n\n  // Check if a regular expression validates the attribute.\n  for (let i = 0, len = regExp.length; i < len; i++) {\n    if (attrName.match(regExp[i])) {\n      return true\n    }\n  }\n\n  return false\n}\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFn) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFn && typeof sanitizeFn === 'function') {\n    return sanitizeFn(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const allowlistKeys = Object.keys(allowList)\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (let i = 0, len = elements.length; i < len; i++) {\n    const el = elements[i]\n    const elName = el.nodeName.toLowerCase()\n\n    if (!allowlistKeys.includes(elName)) {\n      el.parentNode.removeChild(el)\n\n      continue\n    }\n\n    const attributeList = [].concat(...el.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elName] || [])\n\n    attributeList.forEach(attr => {\n      if (!allowedAttribute(attr, allowedAttributes)) {\n        el.removeAttribute(attr.nodeName)\n      }\n    })\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  findShadowRoot,\n  getTransitionDurationFromElement,\n  getUID,\n  isElement,\n  isRTL,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport {\n  DefaultAllowlist,\n  sanitizeHtml\n} from './util/sanitizer'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tooltip'\nconst DATA_KEY = 'bs.tooltip'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-tooltip'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\nconst DISALLOWED_ATTRIBUTES = new Set(['sanitize', 'allowList', 'sanitizeFn'])\n\nconst DefaultType = {\n  animation: 'boolean',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string',\n  delay: '(number|object)',\n  html: 'boolean',\n  selector: '(string|boolean)',\n  placement: '(string|function)',\n  container: '(string|element|boolean)',\n  fallbackPlacements: '(null|array)',\n  boundary: '(string|element)',\n  customClass: '(string|function)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  allowList: 'object',\n  popperConfig: '(null|object)'\n}\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: isRTL ? 'left' : 'right',\n  BOTTOM: 'bottom',\n  LEFT: isRTL ? 'right' : 'left'\n}\n\nconst Default = {\n  animation: true,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n              '<div class=\"tooltip-arrow\"></div>' +\n              '<div class=\"tooltip-inner\"></div>' +\n            '</div>',\n  trigger: 'hover focus',\n  title: '',\n  delay: 0,\n  html: false,\n  selector: false,\n  placement: 'top',\n  container: false,\n  fallbackPlacements: null,\n  boundary: 'clippingParents',\n  customClass: '',\n  sanitize: true,\n  sanitizeFn: null,\n  allowList: DefaultAllowlist,\n  popperConfig: null\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst HOVER_STATE_SHOW = 'show'\nconst HOVER_STATE_OUT = 'out'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tooltip extends BaseComponent {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper (https://popper.js.org)')\n    }\n\n    super(element)\n\n    // private\n    this._isEnabled = true\n    this._timeout = 0\n    this._hoverState = ''\n    this._activeTrigger = {}\n    this._popper = null\n\n    // Protected\n    this.config = this._getConfig(config)\n    this.tip = null\n\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle(event) {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (event) {\n      const dataKey = this.constructor.DATA_KEY\n      let context = Data.getData(event.delegateTarget, dataKey)\n\n      if (!context) {\n        context = new this.constructor(event.delegateTarget, this._getDelegateConfig())\n        Data.setData(event.delegateTarget, dataKey, context)\n      }\n\n      context._activeTrigger.click = !context._activeTrigger.click\n\n      if (context._isWithActiveTrigger()) {\n        context._enter(null, context)\n      } else {\n        context._leave(null, context)\n      }\n    } else {\n      if (this.getTipElement().classList.contains(CLASS_NAME_SHOW)) {\n        this._leave(null, this)\n        return\n      }\n\n      this._enter(null, this)\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    EventHandler.off(this._element, this.constructor.EVENT_KEY)\n    EventHandler.off(this._element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this.tip) {\n      this.tip.parentNode.removeChild(this.tip)\n    }\n\n    this._isEnabled = null\n    this._timeout = null\n    this._hoverState = null\n    this._activeTrigger = null\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._popper = null\n    this.config = null\n    this.tip = null\n    super.dispose()\n  }\n\n  show() {\n    if (this._element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (this.isWithContent() && this._isEnabled) {\n      const showEvent = EventHandler.trigger(this._element, this.constructor.Event.SHOW)\n      const shadowRoot = findShadowRoot(this._element)\n      const isInTheDom = shadowRoot === null ?\n        this._element.ownerDocument.documentElement.contains(this._element) :\n        shadowRoot.contains(this._element)\n\n      if (showEvent.defaultPrevented || !isInTheDom) {\n        return\n      }\n\n      const tip = this.getTipElement()\n      const tipId = getUID(this.constructor.NAME)\n\n      tip.setAttribute('id', tipId)\n      this._element.setAttribute('aria-describedby', tipId)\n\n      this.setContent()\n\n      if (this.config.animation) {\n        tip.classList.add(CLASS_NAME_FADE)\n      }\n\n      const placement = typeof this.config.placement === 'function' ?\n        this.config.placement.call(this, tip, this._element) :\n        this.config.placement\n\n      const attachment = this._getAttachment(placement)\n      this._addAttachmentClass(attachment)\n\n      const container = this._getContainer()\n      Data.setData(tip, this.constructor.DATA_KEY, this)\n\n      if (!this._element.ownerDocument.documentElement.contains(this.tip)) {\n        container.appendChild(tip)\n      }\n\n      EventHandler.trigger(this._element, this.constructor.Event.INSERTED)\n\n      this._popper = Popper.createPopper(this._element, tip, this._getPopperConfig(attachment))\n\n      tip.classList.add(CLASS_NAME_SHOW)\n\n      const customClass = typeof this.config.customClass === 'function' ? this.config.customClass() : this.config.customClass\n      if (customClass) {\n        tip.classList.add(...customClass.split(' '))\n      }\n\n      // If this is a touch-enabled device we add extra\n      // empty mouseover listeners to the body's immediate children;\n      // only needed because of broken event delegation on iOS\n      // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n      if ('ontouchstart' in document.documentElement) {\n        [].concat(...document.body.children).forEach(element => {\n          EventHandler.on(element, 'mouseover', noop())\n        })\n      }\n\n      const complete = () => {\n        const prevHoverState = this._hoverState\n\n        this._hoverState = null\n        EventHandler.trigger(this._element, this.constructor.Event.SHOWN)\n\n        if (prevHoverState === HOVER_STATE_OUT) {\n          this._leave(null, this)\n        }\n      }\n\n      if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n        const transitionDuration = getTransitionDurationFromElement(this.tip)\n        EventHandler.one(this.tip, TRANSITION_END, complete)\n        emulateTransitionEnd(this.tip, transitionDuration)\n      } else {\n        complete()\n      }\n    }\n  }\n\n  hide() {\n    if (!this._popper) {\n      return\n    }\n\n    const tip = this.getTipElement()\n    const complete = () => {\n      if (this._hoverState !== HOVER_STATE_SHOW && tip.parentNode) {\n        tip.parentNode.removeChild(tip)\n      }\n\n      this._cleanTipClass()\n      this._element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this._element, this.constructor.Event.HIDDEN)\n\n      if (this._popper) {\n        this._popper.destroy()\n        this._popper = null\n      }\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, this.constructor.Event.HIDE)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(element => EventHandler.off(element, 'mouseover', noop))\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n\n    if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n      const transitionDuration = getTransitionDurationFromElement(tip)\n\n      EventHandler.one(tip, TRANSITION_END, complete)\n      emulateTransitionEnd(tip, transitionDuration)\n    } else {\n      complete()\n    }\n\n    this._hoverState = ''\n  }\n\n  update() {\n    if (this._popper !== null) {\n      this._popper.update()\n    }\n  }\n\n  // Protected\n\n  isWithContent() {\n    return Boolean(this.getTitle())\n  }\n\n  getTipElement() {\n    if (this.tip) {\n      return this.tip\n    }\n\n    const element = document.createElement('div')\n    element.innerHTML = this.config.template\n\n    this.tip = element.children[0]\n    return this.tip\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TOOLTIP_INNER, tip), this.getTitle())\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  setElementContent(element, content) {\n    if (element === null) {\n      return\n    }\n\n    if (typeof content === 'object' && isElement(content)) {\n      if (content.jquery) {\n        content = content[0]\n      }\n\n      // content is a DOM node or a jQuery\n      if (this.config.html) {\n        if (content.parentNode !== element) {\n          element.innerHTML = ''\n          element.appendChild(content)\n        }\n      } else {\n        element.textContent = content.textContent\n      }\n\n      return\n    }\n\n    if (this.config.html) {\n      if (this.config.sanitize) {\n        content = sanitizeHtml(content, this.config.allowList, this.config.sanitizeFn)\n      }\n\n      element.innerHTML = content\n    } else {\n      element.textContent = content\n    }\n  }\n\n  getTitle() {\n    let title = this._element.getAttribute('data-bs-original-title')\n\n    if (!title) {\n      title = typeof this.config.title === 'function' ?\n        this.config.title.call(this._element) :\n        this.config.title\n    }\n\n    return title\n  }\n\n  updateAttachment(attachment) {\n    if (attachment === 'right') {\n      return 'end'\n    }\n\n    if (attachment === 'left') {\n      return 'start'\n    }\n\n    return attachment\n  }\n\n  // Private\n\n  _getPopperConfig(attachment) {\n    const flipModifier = {\n      name: 'flip',\n      options: {\n        altBoundary: true\n      }\n    }\n\n    if (this.config.fallbackPlacements) {\n      flipModifier.options.fallbackPlacements = this.config.fallbackPlacements\n    }\n\n    const defaultBsConfig = {\n      placement: attachment,\n      modifiers: [\n        flipModifier,\n        {\n          name: 'preventOverflow',\n          options: {\n            rootBoundary: this.config.boundary\n          }\n        },\n        {\n          name: 'arrow',\n          options: {\n            element: `.${this.constructor.NAME}-arrow`\n          }\n        },\n        {\n          name: 'onChange',\n          enabled: true,\n          phase: 'afterWrite',\n          fn: data => this._handlePopperPlacementChange(data)\n        }\n      ],\n      onFirstUpdate: data => {\n        if (data.options.placement !== data.placement) {\n          this._handlePopperPlacementChange(data)\n        }\n      }\n    }\n\n    return {\n      ...defaultBsConfig,\n      ...this.config.popperConfig\n    }\n  }\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${this.updateAttachment(attachment)}`)\n  }\n\n  _getContainer() {\n    if (this.config.container === false) {\n      return document.body\n    }\n\n    if (isElement(this.config.container)) {\n      return this.config.container\n    }\n\n    return SelectorEngine.findOne(this.config.container)\n  }\n\n  _getAttachment(placement) {\n    return AttachmentMap[placement.toUpperCase()]\n  }\n\n  _setListeners() {\n    const triggers = this.config.trigger.split(' ')\n\n    triggers.forEach(trigger => {\n      if (trigger === 'click') {\n        EventHandler.on(this._element, this.constructor.Event.CLICK, this.config.selector, event => this.toggle(event)\n        )\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSEENTER :\n          this.constructor.Event.FOCUSIN\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSELEAVE :\n          this.constructor.Event.FOCUSOUT\n\n        EventHandler.on(this._element, eventIn, this.config.selector, event => this._enter(event))\n        EventHandler.on(this._element, eventOut, this.config.selector, event => this._leave(event))\n      }\n    })\n\n    this._hideModalHandler = () => {\n      if (this._element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this._element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this.config.selector) {\n      this.config = {\n        ...this.config,\n        trigger: 'manual',\n        selector: ''\n      }\n    } else {\n      this._fixTitle()\n    }\n  }\n\n  _fixTitle() {\n    const title = this._element.getAttribute('title')\n    const originalTitleType = typeof this._element.getAttribute('data-bs-original-title')\n\n    if (title || originalTitleType !== 'string') {\n      this._element.setAttribute('data-bs-original-title', title || '')\n      if (title && !this._element.getAttribute('aria-label') && !this._element.textContent) {\n        this._element.setAttribute('aria-label', title)\n      }\n\n      this._element.setAttribute('title', '')\n    }\n  }\n\n  _enter(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.getData(event.delegateTarget, dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.delegateTarget,\n        this._getDelegateConfig()\n      )\n      Data.setData(event.delegateTarget, dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = true\n    }\n\n    if (context.getTipElement().classList.contains(CLASS_NAME_SHOW) || context._hoverState === HOVER_STATE_SHOW) {\n      context._hoverState = HOVER_STATE_SHOW\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_SHOW\n\n    if (!context.config.delay || !context.config.delay.show) {\n      context.show()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_SHOW) {\n        context.show()\n      }\n    }, context.config.delay.show)\n  }\n\n  _leave(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.getData(event.delegateTarget, dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.delegateTarget,\n        this._getDelegateConfig()\n      )\n      Data.setData(event.delegateTarget, dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = false\n    }\n\n    if (context._isWithActiveTrigger()) {\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_OUT\n\n    if (!context.config.delay || !context.config.delay.hide) {\n      context.hide()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_OUT) {\n        context.hide()\n      }\n    }, context.config.delay.hide)\n  }\n\n  _isWithActiveTrigger() {\n    for (const trigger in this._activeTrigger) {\n      if (this._activeTrigger[trigger]) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this._element)\n\n    Object.keys(dataAttributes).forEach(dataAttr => {\n      if (DISALLOWED_ATTRIBUTES.has(dataAttr)) {\n        delete dataAttributes[dataAttr]\n      }\n    })\n\n    if (config && typeof config.container === 'object' && config.container.jquery) {\n      config.container = config.container[0]\n    }\n\n    config = {\n      ...this.constructor.Default,\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (config.sanitize) {\n      config.template = sanitizeHtml(config.template, config.allowList, config.sanitizeFn)\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    if (this.config) {\n      for (const key in this.config) {\n        if (this.constructor.Default[key] !== this.config[key]) {\n          config[key] = this.config[key]\n        }\n      }\n    }\n\n    return config\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  _handlePopperPlacementChange(popperData) {\n    const { state } = popperData\n\n    if (!state) {\n      return\n    }\n\n    this.tip = state.elements.popper\n    this._cleanTipClass()\n    this._addAttachmentClass(this._getAttachment(state.placement))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Tooltip(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tooltip to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Tooltip.jQueryInterface\n    $.fn[NAME].Constructor = Tooltip\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Tooltip.jQueryInterface\n    }\n  }\n})\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery, onDOMContentLoaded } from './util/index'\nimport Data from './dom/data'\nimport SelectorEngine from './dom/selector-engine'\nimport Tooltip from './tooltip'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'popover'\nconst DATA_KEY = 'bs.popover'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-popover'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\nconst Default = {\n  ...Tooltip.Default,\n  placement: 'right',\n  trigger: 'click',\n  content: '',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"popover-arrow\"></div>' +\n                '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div>' +\n            '</div>'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(string|element|function)'\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Popover extends Tooltip {\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Overrides\n\n  isWithContent() {\n    return this.getTitle() || this._getContent()\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n\n    // we use append for html objects to maintain js events\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TITLE, tip), this.getTitle())\n    let content = this._getContent()\n    if (typeof content === 'function') {\n      content = content.call(this._element)\n    }\n\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_CONTENT, tip), content)\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  // Private\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${this.updateAttachment(attachment)}`)\n  }\n\n  _getContent() {\n    return this._element.getAttribute('data-bs-content') || this.config.content\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' ? config : null\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Popover(this, _config)\n        Data.setData(this, DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Popover to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Popover.jQueryInterface\n    $.fn[NAME].Constructor = Popover\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Popover.jQueryInterface\n    }\n  }\n})\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  getSelectorFromElement,\n  getUID,\n  isElement,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'scrollspy'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  offset: 10,\n  method: 'auto',\n  target: ''\n}\n\nconst DefaultType = {\n  offset: 'number',\n  method: 'string',\n  target: '(string|element)'\n}\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_SCROLL = `scroll${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-bs-spy=\"scroll\"]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst METHOD_OFFSET = 'offset'\nconst METHOD_POSITION = 'position'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass ScrollSpy extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n    this._scrollElement = element.tagName === 'BODY' ? window : element\n    this._config = this._getConfig(config)\n    this._selector = `${this._config.target} ${SELECTOR_NAV_LINKS}, ${this._config.target} ${SELECTOR_LIST_ITEMS}, ${this._config.target} .${CLASS_NAME_DROPDOWN_ITEM}`\n    this._offsets = []\n    this._targets = []\n    this._activeTarget = null\n    this._scrollHeight = 0\n\n    EventHandler.on(this._scrollElement, EVENT_SCROLL, event => this._process(event))\n\n    this.refresh()\n    this._process()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  refresh() {\n    const autoMethod = this._scrollElement === this._scrollElement.window ?\n      METHOD_OFFSET :\n      METHOD_POSITION\n\n    const offsetMethod = this._config.method === 'auto' ?\n      autoMethod :\n      this._config.method\n\n    const offsetBase = offsetMethod === METHOD_POSITION ?\n      this._getScrollTop() :\n      0\n\n    this._offsets = []\n    this._targets = []\n    this._scrollHeight = this._getScrollHeight()\n\n    const targets = SelectorEngine.find(this._selector)\n\n    targets.map(element => {\n      const targetSelector = getSelectorFromElement(element)\n      const target = targetSelector ? SelectorEngine.findOne(targetSelector) : null\n\n      if (target) {\n        const targetBCR = target.getBoundingClientRect()\n        if (targetBCR.width || targetBCR.height) {\n          return [\n            Manipulator[offsetMethod](target).top + offsetBase,\n            targetSelector\n          ]\n        }\n      }\n\n      return null\n    })\n      .filter(item => item)\n      .sort((a, b) => a[0] - b[0])\n      .forEach(item => {\n        this._offsets.push(item[0])\n        this._targets.push(item[1])\n      })\n  }\n\n  dispose() {\n    super.dispose()\n    EventHandler.off(this._scrollElement, EVENT_KEY)\n\n    this._scrollElement = null\n    this._config = null\n    this._selector = null\n    this._offsets = null\n    this._targets = null\n    this._activeTarget = null\n    this._scrollHeight = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.target !== 'string' && isElement(config.target)) {\n      let { id } = config.target\n      if (!id) {\n        id = getUID(NAME)\n        config.target.id = id\n      }\n\n      config.target = `#${id}`\n    }\n\n    typeCheckConfig(NAME, config, DefaultType)\n\n    return config\n  }\n\n  _getScrollTop() {\n    return this._scrollElement === window ?\n      this._scrollElement.pageYOffset :\n      this._scrollElement.scrollTop\n  }\n\n  _getScrollHeight() {\n    return this._scrollElement.scrollHeight || Math.max(\n      document.body.scrollHeight,\n      document.documentElement.scrollHeight\n    )\n  }\n\n  _getOffsetHeight() {\n    return this._scrollElement === window ?\n      window.innerHeight :\n      this._scrollElement.getBoundingClientRect().height\n  }\n\n  _process() {\n    const scrollTop = this._getScrollTop() + this._config.offset\n    const scrollHeight = this._getScrollHeight()\n    const maxScroll = this._config.offset + scrollHeight - this._getOffsetHeight()\n\n    if (this._scrollHeight !== scrollHeight) {\n      this.refresh()\n    }\n\n    if (scrollTop >= maxScroll) {\n      const target = this._targets[this._targets.length - 1]\n\n      if (this._activeTarget !== target) {\n        this._activate(target)\n      }\n\n      return\n    }\n\n    if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n      this._activeTarget = null\n      this._clear()\n      return\n    }\n\n    for (let i = this._offsets.length; i--;) {\n      const isActiveTarget = this._activeTarget !== this._targets[i] &&\n          scrollTop >= this._offsets[i] &&\n          (typeof this._offsets[i + 1] === 'undefined' || scrollTop < this._offsets[i + 1])\n\n      if (isActiveTarget) {\n        this._activate(this._targets[i])\n      }\n    }\n  }\n\n  _activate(target) {\n    this._activeTarget = target\n\n    this._clear()\n\n    const queries = this._selector.split(',')\n      .map(selector => `${selector}[data-bs-target=\"${target}\"],${selector}[href=\"${target}\"]`)\n\n    const link = SelectorEngine.findOne(queries.join(','))\n\n    if (link.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE, link.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n\n      link.classList.add(CLASS_NAME_ACTIVE)\n    } else {\n      // Set triggered link as active\n      link.classList.add(CLASS_NAME_ACTIVE)\n\n      SelectorEngine.parents(link, SELECTOR_NAV_LIST_GROUP)\n        .forEach(listGroup => {\n          // Set triggered links parents as active\n          // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n          SelectorEngine.prev(listGroup, `${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`)\n            .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n\n          // Handle special case when .nav-link is inside .nav-item\n          SelectorEngine.prev(listGroup, SELECTOR_NAV_ITEMS)\n            .forEach(navItem => {\n              SelectorEngine.children(navItem, SELECTOR_NAV_LINKS)\n                .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n            })\n        })\n    }\n\n    EventHandler.trigger(this._scrollElement, EVENT_ACTIVATE, {\n      relatedTarget: target\n    })\n  }\n\n  _clear() {\n    SelectorEngine.find(this._selector)\n      .filter(node => node.classList.contains(CLASS_NAME_ACTIVE))\n      .forEach(node => node.classList.remove(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new ScrollSpy(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  SelectorEngine.find(SELECTOR_DATA_SPY)\n    .forEach(spy => new ScrollSpy(spy, Manipulator.getDataAttributes(spy)))\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .ScrollSpy to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = ScrollSpy.jQueryInterface\n    $.fn[NAME].Constructor = ScrollSpy\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return ScrollSpy.jQueryInterface\n    }\n  }\n})\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  reflow\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tab'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_MENU = 'dropdown-menu'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_UL = ':scope > li > .active'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"tab\"], [data-bs-toggle=\"pill\"], [data-bs-toggle=\"list\"]'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_ACTIVE_CHILD = ':scope > .dropdown-menu .active'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tab extends BaseComponent {\n  // Getters\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  show() {\n    if ((this._element.parentNode &&\n      this._element.parentNode.nodeType === Node.ELEMENT_NODE &&\n      this._element.classList.contains(CLASS_NAME_ACTIVE)) ||\n      this._element.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    let previous\n    const target = getElementFromSelector(this._element)\n    const listElement = this._element.closest(SELECTOR_NAV_LIST_GROUP)\n\n    if (listElement) {\n      const itemSelector = listElement.nodeName === 'UL' || listElement.nodeName === 'OL' ? SELECTOR_ACTIVE_UL : SELECTOR_ACTIVE\n      previous = SelectorEngine.find(itemSelector, listElement)\n      previous = previous[previous.length - 1]\n    }\n\n    let hideEvent = null\n\n    if (previous) {\n      hideEvent = EventHandler.trigger(previous, EVENT_HIDE, {\n        relatedTarget: this._element\n      })\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget: previous\n    })\n\n    if (showEvent.defaultPrevented || (hideEvent !== null && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._activate(this._element, listElement)\n\n    const complete = () => {\n      EventHandler.trigger(previous, EVENT_HIDDEN, {\n        relatedTarget: this._element\n      })\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget: previous\n      })\n    }\n\n    if (target) {\n      this._activate(target, target.parentNode, complete)\n    } else {\n      complete()\n    }\n  }\n\n  // Private\n\n  _activate(element, container, callback) {\n    const activeElements = container && (container.nodeName === 'UL' || container.nodeName === 'OL') ?\n      SelectorEngine.find(SELECTOR_ACTIVE_UL, container) :\n      SelectorEngine.children(container, SELECTOR_ACTIVE)\n\n    const active = activeElements[0]\n    const isTransitioning = callback && (active && active.classList.contains(CLASS_NAME_FADE))\n\n    const complete = () => this._transitionComplete(element, active, callback)\n\n    if (active && isTransitioning) {\n      const transitionDuration = getTransitionDurationFromElement(active)\n      active.classList.remove(CLASS_NAME_SHOW)\n\n      EventHandler.one(active, TRANSITION_END, complete)\n      emulateTransitionEnd(active, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  _transitionComplete(element, active, callback) {\n    if (active) {\n      active.classList.remove(CLASS_NAME_ACTIVE)\n\n      const dropdownChild = SelectorEngine.findOne(SELECTOR_DROPDOWN_ACTIVE_CHILD, active.parentNode)\n\n      if (dropdownChild) {\n        dropdownChild.classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      if (active.getAttribute('role') === 'tab') {\n        active.setAttribute('aria-selected', false)\n      }\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n    if (element.getAttribute('role') === 'tab') {\n      element.setAttribute('aria-selected', true)\n    }\n\n    reflow(element)\n\n    if (element.classList.contains(CLASS_NAME_FADE)) {\n      element.classList.add(CLASS_NAME_SHOW)\n    }\n\n    if (element.parentNode && element.parentNode.classList.contains(CLASS_NAME_DROPDOWN_MENU)) {\n      const dropdownElement = element.closest(SELECTOR_DROPDOWN)\n\n      if (dropdownElement) {\n        SelectorEngine.find(SELECTOR_DROPDOWN_TOGGLE)\n          .forEach(dropdown => dropdown.classList.add(CLASS_NAME_ACTIVE))\n      }\n\n      element.setAttribute('aria-expanded', true)\n    }\n\n    if (callback) {\n      callback()\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Data.getData(this, DATA_KEY) || new Tab(this)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n\n  const data = Data.getData(this, DATA_KEY) || new Tab(this)\n  data.show()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tab to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Tab.jQueryInterface\n    $.fn[NAME].Constructor = Tab\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Tab.jQueryInterface\n    }\n  }\n})\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getTransitionDurationFromElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'toast'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"toast\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Toast extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._timeout = null\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      this._element.classList.add(CLASS_NAME_SHOW)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      if (this._config.autohide) {\n        this._timeout = setTimeout(() => {\n          this.hide()\n        }, this._config.delay)\n      }\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE)\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  hide() {\n    if (!this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n\n    super.dispose()\n    this._config = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    return config\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, () => this.hide())\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new Toast(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Toast to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Toast.jQueryInterface\n    $.fn[NAME].Constructor = Toast\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Toast.jQueryInterface\n    }\n  }\n})\n\nexport default Toast\n"], "names": ["MAX_UID", "MILLISECONDS_MULTIPLIER", "TRANSITION_END", "toType", "obj", "undefined", "toString", "call", "match", "toLowerCase", "getUID", "prefix", "Math", "floor", "random", "document", "getElementById", "getSelector", "element", "selector", "getAttribute", "hrefAttr", "trim", "getSelectorFromElement", "querySelector", "getElementFromSelector", "getTransitionDurationFromElement", "window", "getComputedStyle", "transitionDuration", "transitionDelay", "floatTransitionDuration", "Number", "parseFloat", "floatTransitionDelay", "split", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "nodeType", "emulateTransitionEnd", "duration", "called", "durationPadding", "emulatedDuration", "listener", "removeEventListener", "addEventListener", "setTimeout", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "RegExp", "test", "Error", "toUpperCase", "isVisible", "style", "parentNode", "elementStyle", "parentNodeStyle", "display", "visibility", "findShadowRoot", "documentElement", "attachShadow", "getRootNode", "root", "ShadowRoot", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "body", "hasAttribute", "onDOMContentLoaded", "callback", "readyState", "isRTL", "dir", "mapData", "storeData", "id", "set", "key", "data", "bs<PERSON><PERSON>", "get", "keyProperties", "delete", "Data", "setData", "instance", "getData", "removeData", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "nativeEvents", "Set", "getUidEvent", "uid", "getEvent", "bootstrapHandler", "fn", "handler", "event", "<PERSON><PERSON><PERSON><PERSON>", "oneOff", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "dom<PERSON><PERSON>s", "querySelectorAll", "target", "i", "length", "<PERSON><PERSON><PERSON><PERSON>", "events", "delegationSelector", "uidEventList", "len", "<PERSON><PERSON><PERSON><PERSON>", "normalizeParams", "originalTypeEvent", "delegationFn", "delegation", "typeEvent", "replace", "custom", "isNative", "has", "add<PERSON><PERSON><PERSON>", "handlers", "previousFn", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "removeNamespacedHandlers", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "includes", "on", "one", "inNamespace", "isNamespace", "startsWith", "elementEvent", "slice", "keyHandlers", "trigger", "args", "$", "jQueryEvent", "bubbles", "nativeDispatch", "defaultPrevented", "evt", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "createEvent", "initEvent", "CustomEvent", "cancelable", "defineProperty", "preventDefault", "VERSION", "BaseComponent", "_element", "constructor", "DATA_KEY", "dispose", "getInstance", "NAME", "EVENT_KEY", "DATA_API_KEY", "SELECTOR_DISMISS", "EVENT_CLOSE", "EVENT_CLOSED", "EVENT_CLICK_DATA_API", "CLASSNAME_ALERT", "CLASSNAME_FADE", "CLASSNAME_SHOW", "<PERSON><PERSON>", "close", "rootElement", "_getRootElement", "customEvent", "_triggerCloseEvent", "_removeElement", "closest", "classList", "remove", "contains", "_destroyElement", "<PERSON><PERSON><PERSON><PERSON>", "jQueryInterface", "each", "handle<PERSON><PERSON><PERSON>", "alertInstance", "JQUERY_NO_CONFLICT", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "CLASS_NAME_ACTIVE", "SELECTOR_DATA_TOGGLE", "<PERSON><PERSON>", "toggle", "setAttribute", "button", "normalizeData", "val", "normalizeDataKey", "chr", "Manipulator", "setDataAttribute", "removeDataAttribute", "removeAttribute", "getDataAttributes", "attributes", "dataset", "filter", "pureKey", "char<PERSON>t", "getDataAttribute", "offset", "rect", "getBoundingClientRect", "top", "scrollTop", "left", "scrollLeft", "position", "offsetTop", "offsetLeft", "NODE_TEXT", "SelectorEngine", "matches", "find", "concat", "Element", "prototype", "findOne", "children", "child", "parents", "ancestor", "Node", "ELEMENT_NODE", "push", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "ARROW_LEFT_KEY", "ARROW_RIGHT_KEY", "TOUCHEVENT_COMPAT_WAIT", "SWIPE_THRESHOLD", "<PERSON><PERSON><PERSON>", "interval", "keyboard", "slide", "pause", "wrap", "touch", "DefaultType", "DIRECTION_NEXT", "DIRECTION_PREV", "DIRECTION_LEFT", "DIRECTION_RIGHT", "EVENT_SLIDE", "EVENT_SLID", "EVENT_KEYDOWN", "EVENT_MOUSEENTER", "EVENT_MOUSELEAVE", "EVENT_TOUCHSTART", "EVENT_TOUCHMOVE", "EVENT_TOUCHEND", "EVENT_POINTERDOWN", "EVENT_POINTERUP", "EVENT_DRAG_START", "EVENT_LOAD_DATA_API", "CLASS_NAME_CAROUSEL", "CLASS_NAME_SLIDE", "CLASS_NAME_END", "CLASS_NAME_START", "CLASS_NAME_NEXT", "CLASS_NAME_PREV", "CLASS_NAME_POINTER_EVENT", "SELECTOR_ACTIVE", "SELECTOR_ACTIVE_ITEM", "SELECTOR_ITEM", "SELECTOR_ITEM_IMG", "SELECTOR_NEXT_PREV", "SELECTOR_INDICATORS", "SELECTOR_DATA_SLIDE", "SELECTOR_DATA_RIDE", "PointerType", "TOUCH", "PEN", "Carousel", "_items", "_interval", "_activeElement", "_isPaused", "_isSliding", "touchTimeout", "touchStartX", "touchDeltaX", "_config", "_getConfig", "_indicatorsElement", "_touchSupported", "navigator", "maxTouchPoints", "_pointerEvent", "PointerEvent", "_addEventListeners", "_slide", "nextWhenVisible", "hidden", "cycle", "clearInterval", "_updateInterval", "setInterval", "visibilityState", "bind", "to", "index", "activeIndex", "_getItemIndex", "direction", "_handleSwipe", "absDeltax", "abs", "_keydown", "_addTouchEventListeners", "start", "pointerType", "clientX", "touches", "move", "end", "clearTimeout", "itemImg", "e", "add", "tagName", "indexOf", "_getItemByDirection", "activeElement", "isNextDirection", "isPrevDirection", "lastItemIndex", "isGoingToWrap", "delta", "itemIndex", "_triggerSlideEvent", "relatedTarget", "eventDirectionName", "targetIndex", "fromIndex", "from", "_setActiveIndicatorElement", "indicators", "nextIndicator", "elementInterval", "parseInt", "defaultInterval", "activeElementIndex", "nextElement", "nextElementIndex", "isCycling", "directionalClassName", "orderClassName", "slideEvent", "carouselInterface", "action", "TypeError", "ride", "dataApiClickHandler", "slideIndex", "carousels", "parent", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_HIDE", "EVENT_HIDDEN", "CLASS_NAME_SHOW", "CLASS_NAME_COLLAPSE", "CLASS_NAME_COLLAPSING", "CLASS_NAME_COLLAPSED", "WIDTH", "HEIGHT", "SELECTOR_ACTIVES", "Collapse", "_isTransitioning", "_triggerArray", "toggleList", "elem", "filterElement", "foundElem", "_selector", "_parent", "_getParent", "_addAriaAndCollapsedClass", "hide", "show", "actives", "activesData", "container", "tempActiveData", "startEvent", "elemActive", "collapseInterface", "dimension", "_getDimension", "setTransitioning", "complete", "capitalizedDimension", "scrollSize", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isTransitioning", "j<PERSON>y", "selected", "trigger<PERSON><PERSON>y", "isOpen", "triggerData", "selectorElements", "ESCAPE_KEY", "SPACE_KEY", "TAB_KEY", "ARROW_UP_KEY", "ARROW_DOWN_KEY", "RIGHT_MOUSE_BUTTON", "REGEXP_KEYDOWN", "EVENT_CLICK", "EVENT_KEYDOWN_DATA_API", "EVENT_KEYUP_DATA_API", "CLASS_NAME_DISABLED", "CLASS_NAME_DROPUP", "CLASS_NAME_DROPEND", "CLASS_NAME_DROPSTART", "CLASS_NAME_NAVBAR", "SELECTOR_FORM_CHILD", "SELECTOR_MENU", "SELECTOR_NAVBAR_NAV", "SELECTOR_VISIBLE_ITEMS", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "flip", "boundary", "reference", "popperConfig", "Dropdown", "_popper", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "disabled", "isActive", "clearMenus", "getParentFromElement", "showEvent", "<PERSON><PERSON>", "referenceElement", "_getPopperConfig", "focus", "hideEvent", "destroy", "update", "stopPropagation", "_getPlacement", "parentDropdown", "isEnd", "getPropertyValue", "placement", "modifiers", "name", "options", "altBoundary", "rootBoundary", "enabled", "dropdownInterface", "toggles", "context", "clickEvent", "dropdownMenu", "dataApiKeydownHandler", "items", "backdrop", "EVENT_HIDE_PREVENTED", "EVENT_FOCUSIN", "EVENT_RESIZE", "EVENT_CLICK_DISMISS", "EVENT_KEYDOWN_DISMISS", "EVENT_MOUSEUP_DISMISS", "EVENT_MOUSEDOWN_DISMISS", "CLASS_NAME_SCROLLBAR_MEASURER", "CLASS_NAME_BACKDROP", "CLASS_NAME_OPEN", "CLASS_NAME_FADE", "CLASS_NAME_STATIC", "SELECTOR_DIALOG", "SELECTOR_MODAL_BODY", "SELECTOR_DATA_DISMISS", "SELECTOR_FIXED_CONTENT", "SELECTOR_STICKY_CONTENT", "Modal", "_dialog", "_backdrop", "_isShown", "_isBodyOverflowing", "_ignoreBackdropClick", "_scrollbarWidth", "_checkScrollbar", "_setScrollbar", "_adjustDialog", "_setEscapeEvent", "_setResizeEvent", "_showBackdrop", "_showElement", "transition", "_hideModal", "htmlElement", "handleUpdate", "modalBody", "append<PERSON><PERSON><PERSON>", "_enforceFocus", "transitionComplete", "_triggerBackdropTransition", "_resetAdjustments", "_resetScrollbar", "_removeBackdrop", "animate", "createElement", "className", "currentTarget", "backdropTransitionDuration", "callback<PERSON><PERSON><PERSON>", "isModalOverflowing", "scrollHeight", "clientHeight", "overflowY", "modalTransitionDuration", "paddingLeft", "paddingRight", "round", "right", "innerWidth", "_getScrollbarWidth", "actualPadding", "calculatedPadding", "<PERSON><PERSON><PERSON><PERSON>", "marginRight", "<PERSON><PERSON><PERSON><PERSON>", "padding", "margin", "scrollDiv", "scrollbarWidth", "width", "clientWidth", "uriAttrs", "ARIA_ATTRIBUTE_PATTERN", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "allowedAttribute", "attr", "allowedAttributeList", "attrName", "nodeName", "nodeValue", "regExp", "attrRegex", "DefaultAllowlist", "a", "area", "b", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "img", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "sanitizeHtml", "unsafeHtml", "allowList", "sanitizeFn", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createdDocument", "parseFromString", "allow<PERSON><PERSON><PERSON><PERSON>", "elements", "el", "el<PERSON>ame", "attributeList", "allowedAttributes", "innerHTML", "CLASS_PREFIX", "BSCLS_PREFIX_REGEX", "DISALLOWED_ATTRIBUTES", "animation", "template", "title", "delay", "html", "fallbackPlacements", "customClass", "sanitize", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "HIDE", "HIDDEN", "SHOW", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "CLASS_NAME_MODAL", "HOVER_STATE_SHOW", "HOVER_STATE_OUT", "SELECTOR_TOOLTIP_INNER", "TRIGGER_HOVER", "TRIGGER_FOCUS", "TRIGGER_CLICK", "TRIGGER_MANUAL", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "tip", "_setListeners", "enable", "disable", "toggle<PERSON>nabled", "dataKey", "_getDelegateConfig", "click", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "_hideModalHandler", "isWithContent", "shadowRoot", "isInTheDom", "ownerDocument", "tipId", "<PERSON><PERSON><PERSON><PERSON>", "attachment", "_getAttachment", "_addAttachmentClass", "_get<PERSON><PERSON><PERSON>", "prevHoverState", "_cleanTipClass", "getTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "textContent", "updateAttachment", "flipModifier", "defaultBsConfig", "phase", "_handlePopperPlacementChange", "onFirstUpdate", "triggers", "eventIn", "eventOut", "_fixTitle", "originalTitleType", "dataAttributes", "dataAttr", "tabClass", "map", "token", "tClass", "popperData", "state", "popper", "SELECTOR_TITLE", "SELECTOR_CONTENT", "Popover", "_getContent", "method", "EVENT_ACTIVATE", "EVENT_SCROLL", "CLASS_NAME_DROPDOWN_ITEM", "SELECTOR_DATA_SPY", "SELECTOR_NAV_LIST_GROUP", "SELECTOR_NAV_LINKS", "SELECTOR_NAV_ITEMS", "SELECTOR_LIST_ITEMS", "SELECTOR_DROPDOWN", "SELECTOR_DROPDOWN_TOGGLE", "METHOD_OFFSET", "METHOD_POSITION", "ScrollSpy", "_scrollElement", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "_process", "refresh", "autoMethod", "offsetMethod", "offsetBase", "_getScrollTop", "_getScrollHeight", "targets", "targetSelector", "targetBCR", "height", "item", "sort", "pageYOffset", "max", "_getOffsetHeight", "innerHeight", "maxScroll", "_activate", "_clear", "isActiveTarget", "queries", "link", "join", "listGroup", "navItem", "node", "spy", "CLASS_NAME_DROPDOWN_MENU", "SELECTOR_ACTIVE_UL", "SELECTOR_DROPDOWN_ACTIVE_CHILD", "Tab", "listElement", "itemSelector", "activeElements", "active", "_transitionComplete", "dropdown<PERSON><PERSON>d", "dropdownElement", "dropdown", "CLASS_NAME_HIDE", "CLASS_NAME_SHOWING", "autohide", "Toast", "_clearTimeout"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA,IAAMA,OAAO,GAAG,OAAhB;AACA,IAAMC,uBAAuB,GAAG,IAAhC;AACA,IAAMC,cAAc,GAAG,eAAvB;;AAGA,IAAMC,MAAM,GAAG,SAATA,MAAS,CAAAC,GAAG,EAAI;AACpB,MAAIA,GAAG,KAAK,IAAR,IAAgBA,GAAG,KAAKC,SAA5B,EAAuC;AACrC,gBAAUD,GAAV;AACD;;AAED,SAAO,GAAGE,QAAH,CAAYC,IAAZ,CAAiBH,GAAjB,EAAsBI,KAAtB,CAA4B,aAA5B,EAA2C,CAA3C,EAA8CC,WAA9C,EAAP;AACD,CAND;AAQA;AACA;AACA;AACA;AACA;;;AAEA,IAAMC,MAAM,GAAG,SAATA,MAAS,CAAAC,MAAM,EAAI;AACvB,KAAG;AACDA,IAAAA,MAAM,IAAIC,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,KAAgBd,OAA3B,CAAV;AACD,GAFD,QAESe,QAAQ,CAACC,cAAT,CAAwBL,MAAxB,CAFT;;AAIA,SAAOA,MAAP;AACD,CAND;;AAQA,IAAMM,WAAW,GAAG,SAAdA,WAAc,CAAAC,OAAO,EAAI;AAC7B,MAAIC,QAAQ,GAAGD,OAAO,CAACE,YAAR,CAAqB,gBAArB,CAAf;;AAEA,MAAI,CAACD,QAAD,IAAaA,QAAQ,KAAK,GAA9B,EAAmC;AACjC,QAAME,QAAQ,GAAGH,OAAO,CAACE,YAAR,CAAqB,MAArB,CAAjB;AAEAD,IAAAA,QAAQ,GAAGE,QAAQ,IAAIA,QAAQ,KAAK,GAAzB,GAA+BA,QAAQ,CAACC,IAAT,EAA/B,GAAiD,IAA5D;AACD;;AAED,SAAOH,QAAP;AACD,CAVD;;AAYA,IAAMI,sBAAsB,GAAG,SAAzBA,sBAAyB,CAAAL,OAAO,EAAI;AACxC,MAAMC,QAAQ,GAAGF,WAAW,CAACC,OAAD,CAA5B;;AAEA,MAAIC,QAAJ,EAAc;AACZ,WAAOJ,QAAQ,CAACS,aAAT,CAAuBL,QAAvB,IAAmCA,QAAnC,GAA8C,IAArD;AACD;;AAED,SAAO,IAAP;AACD,CARD;;AAUA,IAAMM,sBAAsB,GAAG,SAAzBA,sBAAyB,CAAAP,OAAO,EAAI;AACxC,MAAMC,QAAQ,GAAGF,WAAW,CAACC,OAAD,CAA5B;AAEA,SAAOC,QAAQ,GAAGJ,QAAQ,CAACS,aAAT,CAAuBL,QAAvB,CAAH,GAAsC,IAArD;AACD,CAJD;;AAMA,IAAMO,gCAAgC,GAAG,SAAnCA,gCAAmC,CAAAR,OAAO,EAAI;AAClD,MAAI,CAACA,OAAL,EAAc;AACZ,WAAO,CAAP;AACD,GAHiD;;;AAAA,8BAMJS,MAAM,CAACC,gBAAP,CAAwBV,OAAxB,CANI;AAAA,MAM5CW,kBAN4C,yBAM5CA,kBAN4C;AAAA,MAMxBC,eANwB,yBAMxBA,eANwB;;AAQlD,MAAMC,uBAAuB,GAAGC,MAAM,CAACC,UAAP,CAAkBJ,kBAAlB,CAAhC;AACA,MAAMK,oBAAoB,GAAGF,MAAM,CAACC,UAAP,CAAkBH,eAAlB,CAA7B,CATkD;;AAYlD,MAAI,CAACC,uBAAD,IAA4B,CAACG,oBAAjC,EAAuD;AACrD,WAAO,CAAP;AACD,GAdiD;;;AAiBlDL,EAAAA,kBAAkB,GAAGA,kBAAkB,CAACM,KAAnB,CAAyB,GAAzB,EAA8B,CAA9B,CAArB;AACAL,EAAAA,eAAe,GAAGA,eAAe,CAACK,KAAhB,CAAsB,GAAtB,EAA2B,CAA3B,CAAlB;AAEA,SAAO,CAACH,MAAM,CAACC,UAAP,CAAkBJ,kBAAlB,IAAwCG,MAAM,CAACC,UAAP,CAAkBH,eAAlB,CAAzC,IAA+E7B,uBAAtF;AACD,CArBD;;AAuBA,IAAMmC,oBAAoB,GAAG,SAAvBA,oBAAuB,CAAAlB,OAAO,EAAI;AACtCA,EAAAA,OAAO,CAACmB,aAAR,CAAsB,IAAIC,KAAJ,CAAUpC,cAAV,CAAtB;AACD,CAFD;;AAIA,IAAMqC,SAAS,GAAG,SAAZA,SAAY,CAAAnC,GAAG;AAAA,SAAI,CAACA,GAAG,CAAC,CAAD,CAAH,IAAUA,GAAX,EAAgBoC,QAApB;AAAA,CAArB;;AAEA,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAuB,CAACvB,OAAD,EAAUwB,QAAV,EAAuB;AAClD,MAAIC,MAAM,GAAG,KAAb;AACA,MAAMC,eAAe,GAAG,CAAxB;AACA,MAAMC,gBAAgB,GAAGH,QAAQ,GAAGE,eAApC;;AAEA,WAASE,QAAT,GAAoB;AAClBH,IAAAA,MAAM,GAAG,IAAT;AACAzB,IAAAA,OAAO,CAAC6B,mBAAR,CAA4B7C,cAA5B,EAA4C4C,QAA5C;AACD;;AAED5B,EAAAA,OAAO,CAAC8B,gBAAR,CAAyB9C,cAAzB,EAAyC4C,QAAzC;AACAG,EAAAA,UAAU,CAAC,YAAM;AACf,QAAI,CAACN,MAAL,EAAa;AACXP,MAAAA,oBAAoB,CAAClB,OAAD,CAApB;AACD;AACF,GAJS,EAIP2B,gBAJO,CAAV;AAKD,CAhBD;;AAkBA,IAAMK,eAAe,GAAG,SAAlBA,eAAkB,CAACC,aAAD,EAAgBC,MAAhB,EAAwBC,WAAxB,EAAwC;AAC9DC,EAAAA,MAAM,CAACC,IAAP,CAAYF,WAAZ,EAAyBG,OAAzB,CAAiC,UAAAC,QAAQ,EAAI;AAC3C,QAAMC,aAAa,GAAGL,WAAW,CAACI,QAAD,CAAjC;AACA,QAAME,KAAK,GAAGP,MAAM,CAACK,QAAD,CAApB;AACA,QAAMG,SAAS,GAAGD,KAAK,IAAIpB,SAAS,CAACoB,KAAD,CAAlB,GAChB,SADgB,GAEhBxD,MAAM,CAACwD,KAAD,CAFR;;AAIA,QAAI,CAAC,IAAIE,MAAJ,CAAWH,aAAX,EAA0BI,IAA1B,CAA+BF,SAA/B,CAAL,EAAgD;AAC9C,YAAM,IAAIG,KAAJ,CACDZ,aAAa,CAACa,WAAd,EAAH,yBACWP,QADX,2BACuCG,SADvC,sCAEsBF,aAFtB,SADI,CAAN;AAID;AACF,GAbD;AAcD,CAfD;;AAiBA,IAAMO,SAAS,GAAG,SAAZA,SAAY,CAAA/C,OAAO,EAAI;AAC3B,MAAI,CAACA,OAAL,EAAc;AACZ,WAAO,KAAP;AACD;;AAED,MAAIA,OAAO,CAACgD,KAAR,IAAiBhD,OAAO,CAACiD,UAAzB,IAAuCjD,OAAO,CAACiD,UAAR,CAAmBD,KAA9D,EAAqE;AACnE,QAAME,YAAY,GAAGxC,gBAAgB,CAACV,OAAD,CAArC;AACA,QAAMmD,eAAe,GAAGzC,gBAAgB,CAACV,OAAO,CAACiD,UAAT,CAAxC;AAEA,WAAOC,YAAY,CAACE,OAAb,KAAyB,MAAzB,IACLD,eAAe,CAACC,OAAhB,KAA4B,MADvB,IAELF,YAAY,CAACG,UAAb,KAA4B,QAF9B;AAGD;;AAED,SAAO,KAAP;AACD,CAfD;;AAiBA,IAAMC,cAAc,GAAG,SAAjBA,cAAiB,CAAAtD,OAAO,EAAI;AAChC,MAAI,CAACH,QAAQ,CAAC0D,eAAT,CAAyBC,YAA9B,EAA4C;AAC1C,WAAO,IAAP;AACD,GAH+B;;;AAMhC,MAAI,OAAOxD,OAAO,CAACyD,WAAf,KAA+B,UAAnC,EAA+C;AAC7C,QAAMC,IAAI,GAAG1D,OAAO,CAACyD,WAAR,EAAb;AACA,WAAOC,IAAI,YAAYC,UAAhB,GAA6BD,IAA7B,GAAoC,IAA3C;AACD;;AAED,MAAI1D,OAAO,YAAY2D,UAAvB,EAAmC;AACjC,WAAO3D,OAAP;AACD,GAb+B;;;AAgBhC,MAAI,CAACA,OAAO,CAACiD,UAAb,EAAyB;AACvB,WAAO,IAAP;AACD;;AAED,SAAOK,cAAc,CAACtD,OAAO,CAACiD,UAAT,CAArB;AACD,CArBD;;AAuBA,IAAMW,IAAI,GAAG,SAAPA,IAAO;AAAA,SAAM,YAAY,EAAlB;AAAA,CAAb;;AAEA,IAAMC,MAAM,GAAG,SAATA,MAAS,CAAA7D,OAAO;AAAA,SAAIA,OAAO,CAAC8D,YAAZ;AAAA,CAAtB;;AAEA,IAAMC,SAAS,GAAG,SAAZA,SAAY,GAAM;AAAA,gBACHtD,MADG;AAAA,MACduD,MADc,WACdA,MADc;;AAGtB,MAAIA,MAAM,IAAI,CAACnE,QAAQ,CAACoE,IAAT,CAAcC,YAAd,CAA2B,mBAA3B,CAAf,EAAgE;AAC9D,WAAOF,MAAP;AACD;;AAED,SAAO,IAAP;AACD,CARD;;AAUA,IAAMG,kBAAkB,GAAG,SAArBA,kBAAqB,CAAAC,QAAQ,EAAI;AACrC,MAAIvE,QAAQ,CAACwE,UAAT,KAAwB,SAA5B,EAAuC;AACrCxE,IAAAA,QAAQ,CAACiC,gBAAT,CAA0B,kBAA1B,EAA8CsC,QAA9C;AACD,GAFD,MAEO;AACLA,IAAAA,QAAQ;AACT;AACF,CAND;;AAQA,IAAME,KAAK,GAAGzE,QAAQ,CAAC0D,eAAT,CAAyBgB,GAAzB,KAAiC,KAA/C;;AC5LA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AAEA,IAAMC,OAAO,GAAI,YAAM;AACrB,MAAMC,SAAS,GAAG,EAAlB;AACA,MAAIC,EAAE,GAAG,CAAT;AACA,SAAO;AACLC,IAAAA,GADK,eACD3E,OADC,EACQ4E,GADR,EACaC,IADb,EACmB;AACtB,UAAI,OAAO7E,OAAO,CAAC8E,KAAf,KAAyB,WAA7B,EAA0C;AACxC9E,QAAAA,OAAO,CAAC8E,KAAR,GAAgB;AACdF,UAAAA,GAAG,EAAHA,GADc;AAEdF,UAAAA,EAAE,EAAFA;AAFc,SAAhB;AAIAA,QAAAA,EAAE;AACH;;AAEDD,MAAAA,SAAS,CAACzE,OAAO,CAAC8E,KAAR,CAAcJ,EAAf,CAAT,GAA8BG,IAA9B;AACD,KAXI;AAYLE,IAAAA,GAZK,eAYD/E,OAZC,EAYQ4E,GAZR,EAYa;AAChB,UAAI,CAAC5E,OAAD,IAAY,OAAOA,OAAO,CAAC8E,KAAf,KAAyB,WAAzC,EAAsD;AACpD,eAAO,IAAP;AACD;;AAED,UAAME,aAAa,GAAGhF,OAAO,CAAC8E,KAA9B;;AACA,UAAIE,aAAa,CAACJ,GAAd,KAAsBA,GAA1B,EAA+B;AAC7B,eAAOH,SAAS,CAACO,aAAa,CAACN,EAAf,CAAhB;AACD;;AAED,aAAO,IAAP;AACD,KAvBI;AAwBLO,IAAAA,MAxBK,mBAwBEjF,OAxBF,EAwBW4E,GAxBX,EAwBgB;AACnB,UAAI,OAAO5E,OAAO,CAAC8E,KAAf,KAAyB,WAA7B,EAA0C;AACxC;AACD;;AAED,UAAME,aAAa,GAAGhF,OAAO,CAAC8E,KAA9B;;AACA,UAAIE,aAAa,CAACJ,GAAd,KAAsBA,GAA1B,EAA+B;AAC7B,eAAOH,SAAS,CAACO,aAAa,CAACN,EAAf,CAAhB;AACA,eAAO1E,OAAO,CAAC8E,KAAf;AACD;AACF;AAlCI,GAAP;AAoCD,CAvCe,EAAhB;;AAyCA,IAAMI,IAAI,GAAG;AACXC,EAAAA,OADW,mBACHC,QADG,EACOR,GADP,EACYC,IADZ,EACkB;AAC3BL,IAAAA,OAAO,CAACG,GAAR,CAAYS,QAAZ,EAAsBR,GAAtB,EAA2BC,IAA3B;AACD,GAHU;AAIXQ,EAAAA,OAJW,mBAIHD,QAJG,EAIOR,GAJP,EAIY;AACrB,WAAOJ,OAAO,CAACO,GAAR,CAAYK,QAAZ,EAAsBR,GAAtB,CAAP;AACD,GANU;AAOXU,EAAAA,UAPW,sBAOAF,QAPA,EAOUR,GAPV,EAOe;AACxBJ,IAAAA,OAAO,CAACS,MAAR,CAAeG,QAAf,EAAyBR,GAAzB;AACD;AATU,CAAb;;ACtDA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;;AAEA,IAAMW,cAAc,GAAG,oBAAvB;AACA,IAAMC,cAAc,GAAG,MAAvB;AACA,IAAMC,aAAa,GAAG,QAAtB;AACA,IAAMC,aAAa,GAAG,EAAtB;;AACA,IAAIC,QAAQ,GAAG,CAAf;AACA,IAAMC,YAAY,GAAG;AACnBC,EAAAA,UAAU,EAAE,WADO;AAEnBC,EAAAA,UAAU,EAAE;AAFO,CAArB;AAIA,IAAMC,YAAY,GAAG,IAAIC,GAAJ,CAAQ,CAC3B,OAD2B,EAE3B,UAF2B,EAG3B,SAH2B,EAI3B,WAJ2B,EAK3B,aAL2B,EAM3B,YAN2B,EAO3B,gBAP2B,EAQ3B,WAR2B,EAS3B,UAT2B,EAU3B,WAV2B,EAW3B,aAX2B,EAY3B,WAZ2B,EAa3B,SAb2B,EAc3B,UAd2B,EAe3B,OAf2B,EAgB3B,mBAhB2B,EAiB3B,YAjB2B,EAkB3B,WAlB2B,EAmB3B,UAnB2B,EAoB3B,aApB2B,EAqB3B,aArB2B,EAsB3B,aAtB2B,EAuB3B,WAvB2B,EAwB3B,cAxB2B,EAyB3B,eAzB2B,EA0B3B,cA1B2B,EA2B3B,eA3B2B,EA4B3B,YA5B2B,EA6B3B,OA7B2B,EA8B3B,MA9B2B,EA+B3B,QA/B2B,EAgC3B,OAhC2B,EAiC3B,QAjC2B,EAkC3B,QAlC2B,EAmC3B,SAnC2B,EAoC3B,UApC2B,EAqC3B,MArC2B,EAsC3B,QAtC2B,EAuC3B,cAvC2B,EAwC3B,QAxC2B,EAyC3B,MAzC2B,EA0C3B,kBA1C2B,EA2C3B,kBA3C2B,EA4C3B,OA5C2B,EA6C3B,OA7C2B,EA8C3B,QA9C2B,CAAR,CAArB;AAiDA;AACA;AACA;AACA;AACA;;AAEA,SAASC,WAAT,CAAqBjG,OAArB,EAA8BkG,GAA9B,EAAmC;AACjC,SAAQA,GAAG,IAAOA,GAAP,UAAeP,QAAQ,EAA3B,IAAoC3F,OAAO,CAAC2F,QAA5C,IAAwDA,QAAQ,EAAvE;AACD;;AAED,SAASQ,QAAT,CAAkBnG,OAAlB,EAA2B;AACzB,MAAMkG,GAAG,GAAGD,WAAW,CAACjG,OAAD,CAAvB;AAEAA,EAAAA,OAAO,CAAC2F,QAAR,GAAmBO,GAAnB;AACAR,EAAAA,aAAa,CAACQ,GAAD,CAAb,GAAqBR,aAAa,CAACQ,GAAD,CAAb,IAAsB,EAA3C;AAEA,SAAOR,aAAa,CAACQ,GAAD,CAApB;AACD;;AAED,SAASE,gBAAT,CAA0BpG,OAA1B,EAAmCqG,EAAnC,EAAuC;AACrC,SAAO,SAASC,OAAT,CAAiBC,KAAjB,EAAwB;AAC7BA,IAAAA,KAAK,CAACC,cAAN,GAAuBxG,OAAvB;;AAEA,QAAIsG,OAAO,CAACG,MAAZ,EAAoB;AAClBC,MAAAA,YAAY,CAACC,GAAb,CAAiB3G,OAAjB,EAA0BuG,KAAK,CAACK,IAAhC,EAAsCP,EAAtC;AACD;;AAED,WAAOA,EAAE,CAACQ,KAAH,CAAS7G,OAAT,EAAkB,CAACuG,KAAD,CAAlB,CAAP;AACD,GARD;AASD;;AAED,SAASO,0BAAT,CAAoC9G,OAApC,EAA6CC,QAA7C,EAAuDoG,EAAvD,EAA2D;AACzD,SAAO,SAASC,OAAT,CAAiBC,KAAjB,EAAwB;AAC7B,QAAMQ,WAAW,GAAG/G,OAAO,CAACgH,gBAAR,CAAyB/G,QAAzB,CAApB;;AAEA,aAAWgH,MAAX,GAAsBV,KAAtB,CAAWU,MAAX,EAA6BA,MAAM,IAAIA,MAAM,KAAK,IAAlD,EAAwDA,MAAM,GAAGA,MAAM,CAAChE,UAAxE,EAAoF;AAClF,WAAK,IAAIiE,CAAC,GAAGH,WAAW,CAACI,MAAzB,EAAiCD,CAAC,EAAlC,GAAuC;AACrC,YAAIH,WAAW,CAACG,CAAD,CAAX,KAAmBD,MAAvB,EAA+B;AAC7BV,UAAAA,KAAK,CAACC,cAAN,GAAuBS,MAAvB;;AAEA,cAAIX,OAAO,CAACG,MAAZ,EAAoB;AAClBC,YAAAA,YAAY,CAACC,GAAb,CAAiB3G,OAAjB,EAA0BuG,KAAK,CAACK,IAAhC,EAAsCP,EAAtC;AACD;;AAED,iBAAOA,EAAE,CAACQ,KAAH,CAASI,MAAT,EAAiB,CAACV,KAAD,CAAjB,CAAP;AACD;AACF;AACF,KAf4B;;;AAkB7B,WAAO,IAAP;AACD,GAnBD;AAoBD;;AAED,SAASa,WAAT,CAAqBC,MAArB,EAA6Bf,OAA7B,EAAsCgB,kBAAtC,EAAiE;AAAA,MAA3BA,kBAA2B;AAA3BA,IAAAA,kBAA2B,GAAN,IAAM;AAAA;;AAC/D,MAAMC,YAAY,GAAGnF,MAAM,CAACC,IAAP,CAAYgF,MAAZ,CAArB;;AAEA,OAAK,IAAIH,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAGD,YAAY,CAACJ,MAAnC,EAA2CD,CAAC,GAAGM,GAA/C,EAAoDN,CAAC,EAArD,EAAyD;AACvD,QAAMX,KAAK,GAAGc,MAAM,CAACE,YAAY,CAACL,CAAD,CAAb,CAApB;;AAEA,QAAIX,KAAK,CAACkB,eAAN,KAA0BnB,OAA1B,IAAqCC,KAAK,CAACe,kBAAN,KAA6BA,kBAAtE,EAA0F;AACxF,aAAOf,KAAP;AACD;AACF;;AAED,SAAO,IAAP;AACD;;AAED,SAASmB,eAAT,CAAyBC,iBAAzB,EAA4CrB,OAA5C,EAAqDsB,YAArD,EAAmE;AACjE,MAAMC,UAAU,GAAG,OAAOvB,OAAP,KAAmB,QAAtC;AACA,MAAMmB,eAAe,GAAGI,UAAU,GAAGD,YAAH,GAAkBtB,OAApD,CAFiE;;AAKjE,MAAIwB,SAAS,GAAGH,iBAAiB,CAACI,OAAlB,CAA0BvC,cAA1B,EAA0C,EAA1C,CAAhB;AACA,MAAMwC,MAAM,GAAGpC,YAAY,CAACkC,SAAD,CAA3B;;AAEA,MAAIE,MAAJ,EAAY;AACVF,IAAAA,SAAS,GAAGE,MAAZ;AACD;;AAED,MAAMC,QAAQ,GAAGlC,YAAY,CAACmC,GAAb,CAAiBJ,SAAjB,CAAjB;;AAEA,MAAI,CAACG,QAAL,EAAe;AACbH,IAAAA,SAAS,GAAGH,iBAAZ;AACD;;AAED,SAAO,CAACE,UAAD,EAAaJ,eAAb,EAA8BK,SAA9B,CAAP;AACD;;AAED,SAASK,UAAT,CAAoBnI,OAApB,EAA6B2H,iBAA7B,EAAgDrB,OAAhD,EAAyDsB,YAAzD,EAAuEnB,MAAvE,EAA+E;AAC7E,MAAI,OAAOkB,iBAAP,KAA6B,QAA7B,IAAyC,CAAC3H,OAA9C,EAAuD;AACrD;AACD;;AAED,MAAI,CAACsG,OAAL,EAAc;AACZA,IAAAA,OAAO,GAAGsB,YAAV;AACAA,IAAAA,YAAY,GAAG,IAAf;AACD;;AAR4E,yBAU5BF,eAAe,CAACC,iBAAD,EAAoBrB,OAApB,EAA6BsB,YAA7B,CAVa;AAAA,MAUtEC,UAVsE;AAAA,MAU1DJ,eAV0D;AAAA,MAUzCK,SAVyC;;AAW7E,MAAMT,MAAM,GAAGlB,QAAQ,CAACnG,OAAD,CAAvB;AACA,MAAMoI,QAAQ,GAAGf,MAAM,CAACS,SAAD,CAAN,KAAsBT,MAAM,CAACS,SAAD,CAAN,GAAoB,EAA1C,CAAjB;AACA,MAAMO,UAAU,GAAGjB,WAAW,CAACgB,QAAD,EAAWX,eAAX,EAA4BI,UAAU,GAAGvB,OAAH,GAAa,IAAnD,CAA9B;;AAEA,MAAI+B,UAAJ,EAAgB;AACdA,IAAAA,UAAU,CAAC5B,MAAX,GAAoB4B,UAAU,CAAC5B,MAAX,IAAqBA,MAAzC;AAEA;AACD;;AAED,MAAMP,GAAG,GAAGD,WAAW,CAACwB,eAAD,EAAkBE,iBAAiB,CAACI,OAAlB,CAA0BxC,cAA1B,EAA0C,EAA1C,CAAlB,CAAvB;AACA,MAAMc,EAAE,GAAGwB,UAAU,GACnBf,0BAA0B,CAAC9G,OAAD,EAAUsG,OAAV,EAAmBsB,YAAnB,CADP,GAEnBxB,gBAAgB,CAACpG,OAAD,EAAUsG,OAAV,CAFlB;AAIAD,EAAAA,EAAE,CAACiB,kBAAH,GAAwBO,UAAU,GAAGvB,OAAH,GAAa,IAA/C;AACAD,EAAAA,EAAE,CAACoB,eAAH,GAAqBA,eAArB;AACApB,EAAAA,EAAE,CAACI,MAAH,GAAYA,MAAZ;AACAJ,EAAAA,EAAE,CAACV,QAAH,GAAcO,GAAd;AACAkC,EAAAA,QAAQ,CAAClC,GAAD,CAAR,GAAgBG,EAAhB;AAEArG,EAAAA,OAAO,CAAC8B,gBAAR,CAAyBgG,SAAzB,EAAoCzB,EAApC,EAAwCwB,UAAxC;AACD;;AAED,SAASS,aAAT,CAAuBtI,OAAvB,EAAgCqH,MAAhC,EAAwCS,SAAxC,EAAmDxB,OAAnD,EAA4DgB,kBAA5D,EAAgF;AAC9E,MAAMjB,EAAE,GAAGe,WAAW,CAACC,MAAM,CAACS,SAAD,CAAP,EAAoBxB,OAApB,EAA6BgB,kBAA7B,CAAtB;;AAEA,MAAI,CAACjB,EAAL,EAAS;AACP;AACD;;AAEDrG,EAAAA,OAAO,CAAC6B,mBAAR,CAA4BiG,SAA5B,EAAuCzB,EAAvC,EAA2CkC,OAAO,CAACjB,kBAAD,CAAlD;AACA,SAAOD,MAAM,CAACS,SAAD,CAAN,CAAkBzB,EAAE,CAACV,QAArB,CAAP;AACD;;AAED,SAAS6C,wBAAT,CAAkCxI,OAAlC,EAA2CqH,MAA3C,EAAmDS,SAAnD,EAA8DW,SAA9D,EAAyE;AACvE,MAAMC,iBAAiB,GAAGrB,MAAM,CAACS,SAAD,CAAN,IAAqB,EAA/C;AAEA1F,EAAAA,MAAM,CAACC,IAAP,CAAYqG,iBAAZ,EAA+BpG,OAA/B,CAAuC,UAAAqG,UAAU,EAAI;AACnD,QAAIA,UAAU,CAACC,QAAX,CAAoBH,SAApB,CAAJ,EAAoC;AAClC,UAAMlC,KAAK,GAAGmC,iBAAiB,CAACC,UAAD,CAA/B;AAEAL,MAAAA,aAAa,CAACtI,OAAD,EAAUqH,MAAV,EAAkBS,SAAlB,EAA6BvB,KAAK,CAACkB,eAAnC,EAAoDlB,KAAK,CAACe,kBAA1D,CAAb;AACD;AACF,GAND;AAOD;;AAED,IAAMZ,YAAY,GAAG;AACnBmC,EAAAA,EADmB,cAChB7I,OADgB,EACPuG,KADO,EACAD,OADA,EACSsB,YADT,EACuB;AACxCO,IAAAA,UAAU,CAACnI,OAAD,EAAUuG,KAAV,EAAiBD,OAAjB,EAA0BsB,YAA1B,EAAwC,KAAxC,CAAV;AACD,GAHkB;AAKnBkB,EAAAA,GALmB,eAKf9I,OALe,EAKNuG,KALM,EAKCD,OALD,EAKUsB,YALV,EAKwB;AACzCO,IAAAA,UAAU,CAACnI,OAAD,EAAUuG,KAAV,EAAiBD,OAAjB,EAA0BsB,YAA1B,EAAwC,IAAxC,CAAV;AACD,GAPkB;AASnBjB,EAAAA,GATmB,eASf3G,OATe,EASN2H,iBATM,EASarB,OATb,EASsBsB,YATtB,EASoC;AACrD,QAAI,OAAOD,iBAAP,KAA6B,QAA7B,IAAyC,CAAC3H,OAA9C,EAAuD;AACrD;AACD;;AAHoD,4BAKJ0H,eAAe,CAACC,iBAAD,EAAoBrB,OAApB,EAA6BsB,YAA7B,CALX;AAAA,QAK9CC,UAL8C;AAAA,QAKlCJ,eALkC;AAAA,QAKjBK,SALiB;;AAMrD,QAAMiB,WAAW,GAAGjB,SAAS,KAAKH,iBAAlC;AACA,QAAMN,MAAM,GAAGlB,QAAQ,CAACnG,OAAD,CAAvB;AACA,QAAMgJ,WAAW,GAAGrB,iBAAiB,CAACsB,UAAlB,CAA6B,GAA7B,CAApB;;AAEA,QAAI,OAAOxB,eAAP,KAA2B,WAA/B,EAA4C;AAC1C;AACA,UAAI,CAACJ,MAAD,IAAW,CAACA,MAAM,CAACS,SAAD,CAAtB,EAAmC;AACjC;AACD;;AAEDQ,MAAAA,aAAa,CAACtI,OAAD,EAAUqH,MAAV,EAAkBS,SAAlB,EAA6BL,eAA7B,EAA8CI,UAAU,GAAGvB,OAAH,GAAa,IAArE,CAAb;AACA;AACD;;AAED,QAAI0C,WAAJ,EAAiB;AACf5G,MAAAA,MAAM,CAACC,IAAP,CAAYgF,MAAZ,EAAoB/E,OAApB,CAA4B,UAAA4G,YAAY,EAAI;AAC1CV,QAAAA,wBAAwB,CAACxI,OAAD,EAAUqH,MAAV,EAAkB6B,YAAlB,EAAgCvB,iBAAiB,CAACwB,KAAlB,CAAwB,CAAxB,CAAhC,CAAxB;AACD,OAFD;AAGD;;AAED,QAAMT,iBAAiB,GAAGrB,MAAM,CAACS,SAAD,CAAN,IAAqB,EAA/C;AACA1F,IAAAA,MAAM,CAACC,IAAP,CAAYqG,iBAAZ,EAA+BpG,OAA/B,CAAuC,UAAA8G,WAAW,EAAI;AACpD,UAAMT,UAAU,GAAGS,WAAW,CAACrB,OAAZ,CAAoBtC,aAApB,EAAmC,EAAnC,CAAnB;;AAEA,UAAI,CAACsD,WAAD,IAAgBpB,iBAAiB,CAACiB,QAAlB,CAA2BD,UAA3B,CAApB,EAA4D;AAC1D,YAAMpC,KAAK,GAAGmC,iBAAiB,CAACU,WAAD,CAA/B;AAEAd,QAAAA,aAAa,CAACtI,OAAD,EAAUqH,MAAV,EAAkBS,SAAlB,EAA6BvB,KAAK,CAACkB,eAAnC,EAAoDlB,KAAK,CAACe,kBAA1D,CAAb;AACD;AACF,KARD;AASD,GA7CkB;AA+CnB+B,EAAAA,OA/CmB,mBA+CXrJ,OA/CW,EA+CFuG,KA/CE,EA+CK+C,IA/CL,EA+CW;AAC5B,QAAI,OAAO/C,KAAP,KAAiB,QAAjB,IAA6B,CAACvG,OAAlC,EAA2C;AACzC,aAAO,IAAP;AACD;;AAED,QAAMuJ,CAAC,GAAGxF,SAAS,EAAnB;AACA,QAAM+D,SAAS,GAAGvB,KAAK,CAACwB,OAAN,CAAcvC,cAAd,EAA8B,EAA9B,CAAlB;AACA,QAAMuD,WAAW,GAAGxC,KAAK,KAAKuB,SAA9B;AACA,QAAMG,QAAQ,GAAGlC,YAAY,CAACmC,GAAb,CAAiBJ,SAAjB,CAAjB;AAEA,QAAI0B,WAAJ;AACA,QAAIC,OAAO,GAAG,IAAd;AACA,QAAIC,cAAc,GAAG,IAArB;AACA,QAAIC,gBAAgB,GAAG,KAAvB;AACA,QAAIC,GAAG,GAAG,IAAV;;AAEA,QAAIb,WAAW,IAAIQ,CAAnB,EAAsB;AACpBC,MAAAA,WAAW,GAAGD,CAAC,CAACnI,KAAF,CAAQmF,KAAR,EAAe+C,IAAf,CAAd;AAEAC,MAAAA,CAAC,CAACvJ,OAAD,CAAD,CAAWqJ,OAAX,CAAmBG,WAAnB;AACAC,MAAAA,OAAO,GAAG,CAACD,WAAW,CAACK,oBAAZ,EAAX;AACAH,MAAAA,cAAc,GAAG,CAACF,WAAW,CAACM,6BAAZ,EAAlB;AACAH,MAAAA,gBAAgB,GAAGH,WAAW,CAACO,kBAAZ,EAAnB;AACD;;AAED,QAAI9B,QAAJ,EAAc;AACZ2B,MAAAA,GAAG,GAAG/J,QAAQ,CAACmK,WAAT,CAAqB,YAArB,CAAN;AACAJ,MAAAA,GAAG,CAACK,SAAJ,CAAcnC,SAAd,EAAyB2B,OAAzB,EAAkC,IAAlC;AACD,KAHD,MAGO;AACLG,MAAAA,GAAG,GAAG,IAAIM,WAAJ,CAAgB3D,KAAhB,EAAuB;AAC3BkD,QAAAA,OAAO,EAAPA,OAD2B;AAE3BU,QAAAA,UAAU,EAAE;AAFe,OAAvB,CAAN;AAID,KAjC2B;;;AAoC5B,QAAI,OAAOb,IAAP,KAAgB,WAApB,EAAiC;AAC/BlH,MAAAA,MAAM,CAACC,IAAP,CAAYiH,IAAZ,EAAkBhH,OAAlB,CAA0B,UAAAsC,GAAG,EAAI;AAC/BxC,QAAAA,MAAM,CAACgI,cAAP,CAAsBR,GAAtB,EAA2BhF,GAA3B,EAAgC;AAC9BG,UAAAA,GAD8B,iBACxB;AACJ,mBAAOuE,IAAI,CAAC1E,GAAD,CAAX;AACD;AAH6B,SAAhC;AAKD,OAND;AAOD;;AAED,QAAI+E,gBAAJ,EAAsB;AACpBC,MAAAA,GAAG,CAACS,cAAJ;AACD;;AAED,QAAIX,cAAJ,EAAoB;AAClB1J,MAAAA,OAAO,CAACmB,aAAR,CAAsByI,GAAtB;AACD;;AAED,QAAIA,GAAG,CAACD,gBAAJ,IAAwB,OAAOH,WAAP,KAAuB,WAAnD,EAAgE;AAC9DA,MAAAA,WAAW,CAACa,cAAZ;AACD;;AAED,WAAOT,GAAP;AACD;AA1GkB,CAArB;;ACnNA;AACA;AACA;AACA;AACA;;AAEA,IAAMU,OAAO,GAAG,aAAhB;;IAEMC;AACJ,yBAAYvK,OAAZ,EAAqB;AACnB,QAAI,CAACA,OAAL,EAAc;AACZ;AACD;;AAED,SAAKwK,QAAL,GAAgBxK,OAAhB;AACAkF,IAAAA,IAAI,CAACC,OAAL,CAAanF,OAAb,EAAsB,KAAKyK,WAAL,CAAiBC,QAAvC,EAAiD,IAAjD;AACD;;;;SAEDC,UAAA,mBAAU;AACRzF,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAKkF,QAArB,EAA+B,KAAKC,WAAL,CAAiBC,QAAhD;AACA,SAAKF,QAAL,GAAgB,IAAhB;AACD;AAED;;;gBAEOI,cAAP,qBAAmB5K,OAAnB,EAA4B;AAC1B,WAAOkF,IAAI,CAACG,OAAL,CAAarF,OAAb,EAAsB,KAAK0K,QAA3B,CAAP;AACD;;;;wBAEoB;AACnB,aAAOJ,OAAP;AACD;;;;;;ACrBH;AACA;AACA;AACA;AACA;;AAEA,IAAMO,IAAI,GAAG,OAAb;AACA,IAAMH,QAAQ,GAAG,UAAjB;AACA,IAAMI,SAAS,SAAOJ,QAAtB;AACA,IAAMK,YAAY,GAAG,WAArB;AAEA,IAAMC,gBAAgB,GAAG,2BAAzB;AAEA,IAAMC,WAAW,aAAWH,SAA5B;AACA,IAAMI,YAAY,cAAYJ,SAA9B;AACA,IAAMK,oBAAoB,aAAWL,SAAX,GAAuBC,YAAjD;AAEA,IAAMK,eAAe,GAAG,OAAxB;AACA,IAAMC,cAAc,GAAG,MAAvB;AACA,IAAMC,cAAc,GAAG,MAAvB;AAEA;AACA;AACA;AACA;AACA;;IAEMC;;;;;;;;;AAOJ;SAEAC,QAAA,eAAMxL,OAAN,EAAe;AACb,QAAMyL,WAAW,GAAGzL,OAAO,GAAG,KAAK0L,eAAL,CAAqB1L,OAArB,CAAH,GAAmC,KAAKwK,QAAnE;;AACA,QAAMmB,WAAW,GAAG,KAAKC,kBAAL,CAAwBH,WAAxB,CAApB;;AAEA,QAAIE,WAAW,KAAK,IAAhB,IAAwBA,WAAW,CAAChC,gBAAxC,EAA0D;AACxD;AACD;;AAED,SAAKkC,cAAL,CAAoBJ,WAApB;AACD;;;SAIDC,kBAAA,yBAAgB1L,OAAhB,EAAyB;AACvB,WAAOO,sBAAsB,CAACP,OAAD,CAAtB,IAAmCA,OAAO,CAAC8L,OAAR,OAAoBV,eAApB,CAA1C;AACD;;SAEDQ,qBAAA,4BAAmB5L,OAAnB,EAA4B;AAC1B,WAAO0G,YAAY,CAAC2C,OAAb,CAAqBrJ,OAArB,EAA8BiL,WAA9B,CAAP;AACD;;SAEDY,iBAAA,wBAAe7L,OAAf,EAAwB;AAAA;;AACtBA,IAAAA,OAAO,CAAC+L,SAAR,CAAkBC,MAAlB,CAAyBV,cAAzB;;AAEA,QAAI,CAACtL,OAAO,CAAC+L,SAAR,CAAkBE,QAAlB,CAA2BZ,cAA3B,CAAL,EAAiD;AAC/C,WAAKa,eAAL,CAAqBlM,OAArB;;AACA;AACD;;AAED,QAAMW,kBAAkB,GAAGH,gCAAgC,CAACR,OAAD,CAA3D;AAEA0G,IAAAA,YAAY,CAACoC,GAAb,CAAiB9I,OAAjB,EAA0BhB,cAA1B,EAA0C;AAAA,aAAM,KAAI,CAACkN,eAAL,CAAqBlM,OAArB,CAAN;AAAA,KAA1C;AACAuB,IAAAA,oBAAoB,CAACvB,OAAD,EAAUW,kBAAV,CAApB;AACD;;SAEDuL,kBAAA,yBAAgBlM,OAAhB,EAAyB;AACvB,QAAIA,OAAO,CAACiD,UAAZ,EAAwB;AACtBjD,MAAAA,OAAO,CAACiD,UAAR,CAAmBkJ,WAAnB,CAA+BnM,OAA/B;AACD;;AAED0G,IAAAA,YAAY,CAAC2C,OAAb,CAAqBrJ,OAArB,EAA8BkL,YAA9B;AACD;;;QAIMkB,kBAAP,yBAAuBlK,MAAvB,EAA+B;AAC7B,WAAO,KAAKmK,IAAL,CAAU,YAAY;AAC3B,UAAIxH,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmBqF,QAAnB,CAAX;;AAEA,UAAI,CAAC7F,IAAL,EAAW;AACTA,QAAAA,IAAI,GAAG,IAAI0G,KAAJ,CAAU,IAAV,CAAP;AACD;;AAED,UAAIrJ,MAAM,KAAK,OAAf,EAAwB;AACtB2C,QAAAA,IAAI,CAAC3C,MAAD,CAAJ,CAAa,IAAb;AACD;AACF,KAVM,CAAP;AAWD;;QAEMoK,gBAAP,uBAAqBC,aAArB,EAAoC;AAClC,WAAO,UAAUhG,KAAV,EAAiB;AACtB,UAAIA,KAAJ,EAAW;AACTA,QAAAA,KAAK,CAAC8D,cAAN;AACD;;AAEDkC,MAAAA,aAAa,CAACf,KAAd,CAAoB,IAApB;AACD,KAND;AAOD;;;;AA3ED;wBAEsB;AACpB,aAAOd,QAAP;AACD;;;;EALiBH;AA+EpB;AACA;AACA;AACA;AACA;;;AACA7D,YAAY,CAACmC,EAAb,CAAgBhJ,QAAhB,EAA0BsL,oBAA1B,EAAgDH,gBAAhD,EAAkEO,KAAK,CAACe,aAAN,CAAoB,IAAIf,KAAJ,EAApB,CAAlE;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEApH,kBAAkB,CAAC,YAAM;AACvB,MAAMoF,CAAC,GAAGxF,SAAS,EAAnB;AACA;;AACA,MAAIwF,CAAJ,EAAO;AACL,QAAMiD,kBAAkB,GAAGjD,CAAC,CAAClD,EAAF,CAAKwE,IAAL,CAA3B;AACAtB,IAAAA,CAAC,CAAClD,EAAF,CAAKwE,IAAL,IAAaU,KAAK,CAACa,eAAnB;AACA7C,IAAAA,CAAC,CAAClD,EAAF,CAAKwE,IAAL,EAAW4B,WAAX,GAAyBlB,KAAzB;;AACAhC,IAAAA,CAAC,CAAClD,EAAF,CAAKwE,IAAL,EAAW6B,UAAX,GAAwB,YAAM;AAC5BnD,MAAAA,CAAC,CAAClD,EAAF,CAAKwE,IAAL,IAAa2B,kBAAb;AACA,aAAOjB,KAAK,CAACa,eAAb;AACD,KAHD;AAID;AACF,CAZiB,CAAlB;;AC/HA;AACA;AACA;AACA;AACA;;AAEA,IAAMvB,MAAI,GAAG,QAAb;AACA,IAAMH,UAAQ,GAAG,WAAjB;AACA,IAAMI,WAAS,SAAOJ,UAAtB;AACA,IAAMK,cAAY,GAAG,WAArB;AAEA,IAAM4B,iBAAiB,GAAG,QAA1B;AAEA,IAAMC,oBAAoB,GAAG,2BAA7B;AAEA,IAAMzB,sBAAoB,aAAWL,WAAX,GAAuBC,cAAjD;AAEA;AACA;AACA;AACA;AACA;;IAEM8B;;;;;;;;;AAOJ;SAEAC,SAAA,kBAAS;AACP;AACA,SAAKtC,QAAL,CAAcuC,YAAd,CAA2B,cAA3B,EAA2C,KAAKvC,QAAL,CAAcuB,SAAd,CAAwBe,MAAxB,CAA+BH,iBAA/B,CAA3C;AACD;;;SAIMP,kBAAP,yBAAuBlK,MAAvB,EAA+B;AAC7B,WAAO,KAAKmK,IAAL,CAAU,YAAY;AAC3B,UAAIxH,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmBqF,UAAnB,CAAX;;AAEA,UAAI,CAAC7F,IAAL,EAAW;AACTA,QAAAA,IAAI,GAAG,IAAIgI,MAAJ,CAAW,IAAX,CAAP;AACD;;AAED,UAAI3K,MAAM,KAAK,QAAf,EAAyB;AACvB2C,QAAAA,IAAI,CAAC3C,MAAD,CAAJ;AACD;AACF,KAVM,CAAP;AAWD;;;;AA3BD;wBAEsB;AACpB,aAAOwI,UAAP;AACD;;;;EALkBH;AA+BrB;AACA;AACA;AACA;AACA;;;AAEA7D,YAAY,CAACmC,EAAb,CAAgBhJ,QAAhB,EAA0BsL,sBAA1B,EAAgDyB,oBAAhD,EAAsE,UAAArG,KAAK,EAAI;AAC7EA,EAAAA,KAAK,CAAC8D,cAAN;AAEA,MAAM2C,MAAM,GAAGzG,KAAK,CAACU,MAAN,CAAa6E,OAAb,CAAqBc,oBAArB,CAAf;AAEA,MAAI/H,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa2H,MAAb,EAAqBtC,UAArB,CAAX;;AACA,MAAI,CAAC7F,IAAL,EAAW;AACTA,IAAAA,IAAI,GAAG,IAAIgI,MAAJ,CAAWG,MAAX,CAAP;AACD;;AAEDnI,EAAAA,IAAI,CAACiI,MAAL;AACD,CAXD;AAaA;AACA;AACA;AACA;AACA;AACA;;AAEA3I,kBAAkB,CAAC,YAAM;AACvB,MAAMoF,CAAC,GAAGxF,SAAS,EAAnB;AACA;;AACA,MAAIwF,CAAJ,EAAO;AACL,QAAMiD,kBAAkB,GAAGjD,CAAC,CAAClD,EAAF,CAAKwE,MAAL,CAA3B;AACAtB,IAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,IAAagC,MAAM,CAACT,eAApB;AACA7C,IAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,EAAW4B,WAAX,GAAyBI,MAAzB;;AAEAtD,IAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,EAAW6B,UAAX,GAAwB,YAAM;AAC5BnD,MAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,IAAa2B,kBAAb;AACA,aAAOK,MAAM,CAACT,eAAd;AACD,KAHD;AAID;AACF,CAbiB,CAAlB;;AC5FA;AACA;AACA;AACA;AACA;AACA;AAEA,SAASa,aAAT,CAAuBC,GAAvB,EAA4B;AAC1B,MAAIA,GAAG,KAAK,MAAZ,EAAoB;AAClB,WAAO,IAAP;AACD;;AAED,MAAIA,GAAG,KAAK,OAAZ,EAAqB;AACnB,WAAO,KAAP;AACD;;AAED,MAAIA,GAAG,KAAKpM,MAAM,CAACoM,GAAD,CAAN,CAAY9N,QAAZ,EAAZ,EAAoC;AAClC,WAAO0B,MAAM,CAACoM,GAAD,CAAb;AACD;;AAED,MAAIA,GAAG,KAAK,EAAR,IAAcA,GAAG,KAAK,MAA1B,EAAkC;AAChC,WAAO,IAAP;AACD;;AAED,SAAOA,GAAP;AACD;;AAED,SAASC,gBAAT,CAA0BvI,GAA1B,EAA+B;AAC7B,SAAOA,GAAG,CAACmD,OAAJ,CAAY,QAAZ,EAAsB,UAAAqF,GAAG;AAAA,iBAAQA,GAAG,CAAC7N,WAAJ,EAAR;AAAA,GAAzB,CAAP;AACD;;AAED,IAAM8N,WAAW,GAAG;AAClBC,EAAAA,gBADkB,4BACDtN,OADC,EACQ4E,GADR,EACanC,KADb,EACoB;AACpCzC,IAAAA,OAAO,CAAC+M,YAAR,cAAgCI,gBAAgB,CAACvI,GAAD,CAAhD,EAAyDnC,KAAzD;AACD,GAHiB;AAKlB8K,EAAAA,mBALkB,+BAKEvN,OALF,EAKW4E,GALX,EAKgB;AAChC5E,IAAAA,OAAO,CAACwN,eAAR,cAAmCL,gBAAgB,CAACvI,GAAD,CAAnD;AACD,GAPiB;AASlB6I,EAAAA,iBATkB,6BASAzN,OATA,EASS;AACzB,QAAI,CAACA,OAAL,EAAc;AACZ,aAAO,EAAP;AACD;;AAED,QAAM0N,UAAU,GAAG,EAAnB;AAEAtL,IAAAA,MAAM,CAACC,IAAP,CAAYrC,OAAO,CAAC2N,OAApB,EACGC,MADH,CACU,UAAAhJ,GAAG;AAAA,aAAIA,GAAG,CAACqE,UAAJ,CAAe,IAAf,CAAJ;AAAA,KADb,EAEG3G,OAFH,CAEW,UAAAsC,GAAG,EAAI;AACd,UAAIiJ,OAAO,GAAGjJ,GAAG,CAACmD,OAAJ,CAAY,KAAZ,EAAmB,EAAnB,CAAd;AACA8F,MAAAA,OAAO,GAAGA,OAAO,CAACC,MAAR,CAAe,CAAf,EAAkBvO,WAAlB,KAAkCsO,OAAO,CAAC1E,KAAR,CAAc,CAAd,EAAiB0E,OAAO,CAAC1G,MAAzB,CAA5C;AACAuG,MAAAA,UAAU,CAACG,OAAD,CAAV,GAAsBZ,aAAa,CAACjN,OAAO,CAAC2N,OAAR,CAAgB/I,GAAhB,CAAD,CAAnC;AACD,KANH;AAQA,WAAO8I,UAAP;AACD,GAzBiB;AA2BlBK,EAAAA,gBA3BkB,4BA2BD/N,OA3BC,EA2BQ4E,GA3BR,EA2Ba;AAC7B,WAAOqI,aAAa,CAACjN,OAAO,CAACE,YAAR,cAAgCiN,gBAAgB,CAACvI,GAAD,CAAhD,CAAD,CAApB;AACD,GA7BiB;AA+BlBoJ,EAAAA,MA/BkB,kBA+BXhO,OA/BW,EA+BF;AACd,QAAMiO,IAAI,GAAGjO,OAAO,CAACkO,qBAAR,EAAb;AAEA,WAAO;AACLC,MAAAA,GAAG,EAAEF,IAAI,CAACE,GAAL,GAAWtO,QAAQ,CAACoE,IAAT,CAAcmK,SADzB;AAELC,MAAAA,IAAI,EAAEJ,IAAI,CAACI,IAAL,GAAYxO,QAAQ,CAACoE,IAAT,CAAcqK;AAF3B,KAAP;AAID,GAtCiB;AAwClBC,EAAAA,QAxCkB,oBAwCTvO,OAxCS,EAwCA;AAChB,WAAO;AACLmO,MAAAA,GAAG,EAAEnO,OAAO,CAACwO,SADR;AAELH,MAAAA,IAAI,EAAErO,OAAO,CAACyO;AAFT,KAAP;AAID;AA7CiB,CAApB;;AC/BA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AAEA,IAAMC,SAAS,GAAG,CAAlB;AAEA,IAAMC,cAAc,GAAG;AACrBC,EAAAA,OADqB,mBACb5O,OADa,EACJC,QADI,EACM;AACzB,WAAOD,OAAO,CAAC4O,OAAR,CAAgB3O,QAAhB,CAAP;AACD,GAHoB;AAKrB4O,EAAAA,IALqB,gBAKhB5O,QALgB,EAKND,OALM,EAK8B;AAAA;;AAAA,QAApCA,OAAoC;AAApCA,MAAAA,OAAoC,GAA1BH,QAAQ,CAAC0D,eAAiB;AAAA;;AACjD,WAAO,YAAGuL,MAAH,aAAaC,OAAO,CAACC,SAAR,CAAkBhI,gBAAlB,CAAmC3H,IAAnC,CAAwCW,OAAxC,EAAiDC,QAAjD,CAAb,CAAP;AACD,GAPoB;AASrBgP,EAAAA,OATqB,mBASbhP,QATa,EASHD,OATG,EASiC;AAAA,QAApCA,OAAoC;AAApCA,MAAAA,OAAoC,GAA1BH,QAAQ,CAAC0D,eAAiB;AAAA;;AACpD,WAAOwL,OAAO,CAACC,SAAR,CAAkB1O,aAAlB,CAAgCjB,IAAhC,CAAqCW,OAArC,EAA8CC,QAA9C,CAAP;AACD,GAXoB;AAarBiP,EAAAA,QAbqB,oBAaZlP,OAbY,EAaHC,QAbG,EAaO;AAAA;;AAC1B,QAAMiP,QAAQ,GAAG,aAAGJ,MAAH,cAAa9O,OAAO,CAACkP,QAArB,CAAjB;;AAEA,WAAOA,QAAQ,CAACtB,MAAT,CAAgB,UAAAuB,KAAK;AAAA,aAAIA,KAAK,CAACP,OAAN,CAAc3O,QAAd,CAAJ;AAAA,KAArB,CAAP;AACD,GAjBoB;AAmBrBmP,EAAAA,OAnBqB,mBAmBbpP,OAnBa,EAmBJC,QAnBI,EAmBM;AACzB,QAAMmP,OAAO,GAAG,EAAhB;AAEA,QAAIC,QAAQ,GAAGrP,OAAO,CAACiD,UAAvB;;AAEA,WAAOoM,QAAQ,IAAIA,QAAQ,CAAC/N,QAAT,KAAsBgO,IAAI,CAACC,YAAvC,IAAuDF,QAAQ,CAAC/N,QAAT,KAAsBoN,SAApF,EAA+F;AAC7F,UAAI,KAAKE,OAAL,CAAaS,QAAb,EAAuBpP,QAAvB,CAAJ,EAAsC;AACpCmP,QAAAA,OAAO,CAACI,IAAR,CAAaH,QAAb;AACD;;AAEDA,MAAAA,QAAQ,GAAGA,QAAQ,CAACpM,UAApB;AACD;;AAED,WAAOmM,OAAP;AACD,GAjCoB;AAmCrBK,EAAAA,IAnCqB,gBAmChBzP,OAnCgB,EAmCPC,QAnCO,EAmCG;AACtB,QAAIyP,QAAQ,GAAG1P,OAAO,CAAC2P,sBAAvB;;AAEA,WAAOD,QAAP,EAAiB;AACf,UAAIA,QAAQ,CAACd,OAAT,CAAiB3O,QAAjB,CAAJ,EAAgC;AAC9B,eAAO,CAACyP,QAAD,CAAP;AACD;;AAEDA,MAAAA,QAAQ,GAAGA,QAAQ,CAACC,sBAApB;AACD;;AAED,WAAO,EAAP;AACD,GA/CoB;AAiDrBC,EAAAA,IAjDqB,gBAiDhB5P,OAjDgB,EAiDPC,QAjDO,EAiDG;AACtB,QAAI2P,IAAI,GAAG5P,OAAO,CAAC6P,kBAAnB;;AAEA,WAAOD,IAAP,EAAa;AACX,UAAI,KAAKhB,OAAL,CAAagB,IAAb,EAAmB3P,QAAnB,CAAJ,EAAkC;AAChC,eAAO,CAAC2P,IAAD,CAAP;AACD;;AAEDA,MAAAA,IAAI,GAAGA,IAAI,CAACC,kBAAZ;AACD;;AAED,WAAO,EAAP;AACD;AA7DoB,CAAvB;;ACUA;AACA;AACA;AACA;AACA;;AAEA,IAAMhF,MAAI,GAAG,UAAb;AACA,IAAMH,UAAQ,GAAG,aAAjB;AACA,IAAMI,WAAS,SAAOJ,UAAtB;AACA,IAAMK,cAAY,GAAG,WAArB;AAEA,IAAM+E,cAAc,GAAG,WAAvB;AACA,IAAMC,eAAe,GAAG,YAAxB;AACA,IAAMC,sBAAsB,GAAG,GAA/B;;AACA,IAAMC,eAAe,GAAG,EAAxB;AAEA,IAAMC,OAAO,GAAG;AACdC,EAAAA,QAAQ,EAAE,IADI;AAEdC,EAAAA,QAAQ,EAAE,IAFI;AAGdC,EAAAA,KAAK,EAAE,KAHO;AAIdC,EAAAA,KAAK,EAAE,OAJO;AAKdC,EAAAA,IAAI,EAAE,IALQ;AAMdC,EAAAA,KAAK,EAAE;AANO,CAAhB;AASA,IAAMC,WAAW,GAAG;AAClBN,EAAAA,QAAQ,EAAE,kBADQ;AAElBC,EAAAA,QAAQ,EAAE,SAFQ;AAGlBC,EAAAA,KAAK,EAAE,kBAHW;AAIlBC,EAAAA,KAAK,EAAE,kBAJW;AAKlBC,EAAAA,IAAI,EAAE,SALY;AAMlBC,EAAAA,KAAK,EAAE;AANW,CAApB;AASA,IAAME,cAAc,GAAG,MAAvB;AACA,IAAMC,cAAc,GAAG,MAAvB;AACA,IAAMC,cAAc,GAAG,MAAvB;AACA,IAAMC,eAAe,GAAG,OAAxB;AAEA,IAAMC,WAAW,aAAWhG,WAA5B;AACA,IAAMiG,UAAU,YAAUjG,WAA1B;AACA,IAAMkG,aAAa,eAAalG,WAAhC;AACA,IAAMmG,gBAAgB,kBAAgBnG,WAAtC;AACA,IAAMoG,gBAAgB,kBAAgBpG,WAAtC;AACA,IAAMqG,gBAAgB,kBAAgBrG,WAAtC;AACA,IAAMsG,eAAe,iBAAetG,WAApC;AACA,IAAMuG,cAAc,gBAAcvG,WAAlC;AACA,IAAMwG,iBAAiB,mBAAiBxG,WAAxC;AACA,IAAMyG,eAAe,iBAAezG,WAApC;AACA,IAAM0G,gBAAgB,iBAAe1G,WAArC;AACA,IAAM2G,mBAAmB,YAAU3G,WAAV,GAAsBC,cAA/C;AACA,IAAMI,sBAAoB,aAAWL,WAAX,GAAuBC,cAAjD;AAEA,IAAM2G,mBAAmB,GAAG,UAA5B;AACA,IAAM/E,mBAAiB,GAAG,QAA1B;AACA,IAAMgF,gBAAgB,GAAG,OAAzB;AACA,IAAMC,cAAc,GAAG,mBAAvB;AACA,IAAMC,gBAAgB,GAAG,qBAAzB;AACA,IAAMC,eAAe,GAAG,oBAAxB;AACA,IAAMC,eAAe,GAAG,oBAAxB;AACA,IAAMC,wBAAwB,GAAG,eAAjC;AAEA,IAAMC,eAAe,GAAG,SAAxB;AACA,IAAMC,oBAAoB,GAAG,uBAA7B;AACA,IAAMC,aAAa,GAAG,gBAAtB;AACA,IAAMC,iBAAiB,GAAG,oBAA1B;AACA,IAAMC,kBAAkB,GAAG,0CAA3B;AACA,IAAMC,mBAAmB,GAAG,sBAA5B;AACA,IAAMC,mBAAmB,GAAG,qCAA5B;AACA,IAAMC,kBAAkB,GAAG,2BAA3B;AAEA,IAAMC,WAAW,GAAG;AAClBC,EAAAA,KAAK,EAAE,OADW;AAElBC,EAAAA,GAAG,EAAE;AAFa,CAApB;AAKA;AACA;AACA;AACA;AACA;;IACMC;;;AACJ,oBAAY5S,OAAZ,EAAqBkC,MAArB,EAA6B;AAAA;;AAC3B,sCAAMlC,OAAN;AAEA,UAAK6S,MAAL,GAAc,IAAd;AACA,UAAKC,SAAL,GAAiB,IAAjB;AACA,UAAKC,cAAL,GAAsB,IAAtB;AACA,UAAKC,SAAL,GAAiB,KAAjB;AACA,UAAKC,UAAL,GAAkB,KAAlB;AACA,UAAKC,YAAL,GAAoB,IAApB;AACA,UAAKC,WAAL,GAAmB,CAAnB;AACA,UAAKC,WAAL,GAAmB,CAAnB;AAEA,UAAKC,OAAL,GAAe,MAAKC,UAAL,CAAgBpR,MAAhB,CAAf;AACA,UAAKqR,kBAAL,GAA0B5E,cAAc,CAACM,OAAf,CAAuBqD,mBAAvB,EAA4C,MAAK9H,QAAjD,CAA1B;AACA,UAAKgJ,eAAL,GAAuB,kBAAkB3T,QAAQ,CAAC0D,eAA3B,IAA8CkQ,SAAS,CAACC,cAAV,GAA2B,CAAhG;AACA,UAAKC,aAAL,GAAqBpL,OAAO,CAAC9H,MAAM,CAACmT,YAAR,CAA5B;;AAEA,UAAKC,kBAAL;;AAjB2B;AAkB5B;;;;;AAYD;SAEAjE,OAAA,gBAAO;AACL,QAAI,CAAC,KAAKqD,UAAV,EAAsB;AACpB,WAAKa,MAAL,CAAYpD,cAAZ;AACD;AACF;;SAEDqD,kBAAA,2BAAkB;AAChB;AACA;AACA,QAAI,CAAClU,QAAQ,CAACmU,MAAV,IAAoBjR,SAAS,CAAC,KAAKyH,QAAN,CAAjC,EAAkD;AAChD,WAAKoF,IAAL;AACD;AACF;;SAEDH,OAAA,gBAAO;AACL,QAAI,CAAC,KAAKwD,UAAV,EAAsB;AACpB,WAAKa,MAAL,CAAYnD,cAAZ;AACD;AACF;;SAEDL,QAAA,eAAM/J,KAAN,EAAa;AACX,QAAI,CAACA,KAAL,EAAY;AACV,WAAKyM,SAAL,GAAiB,IAAjB;AACD;;AAED,QAAIrE,cAAc,CAACM,OAAf,CAAuBoD,kBAAvB,EAA2C,KAAK7H,QAAhD,CAAJ,EAA+D;AAC7DtJ,MAAAA,oBAAoB,CAAC,KAAKsJ,QAAN,CAApB;AACA,WAAKyJ,KAAL,CAAW,IAAX;AACD;;AAEDC,IAAAA,aAAa,CAAC,KAAKpB,SAAN,CAAb;AACA,SAAKA,SAAL,GAAiB,IAAjB;AACD;;SAEDmB,QAAA,eAAM1N,KAAN,EAAa;AACX,QAAI,CAACA,KAAL,EAAY;AACV,WAAKyM,SAAL,GAAiB,KAAjB;AACD;;AAED,QAAI,KAAKF,SAAT,EAAoB;AAClBoB,MAAAA,aAAa,CAAC,KAAKpB,SAAN,CAAb;AACA,WAAKA,SAAL,GAAiB,IAAjB;AACD;;AAED,QAAI,KAAKO,OAAL,IAAgB,KAAKA,OAAL,CAAalD,QAA7B,IAAyC,CAAC,KAAK6C,SAAnD,EAA8D;AAC5D,WAAKmB,eAAL;;AAEA,WAAKrB,SAAL,GAAiBsB,WAAW,CAC1B,CAACvU,QAAQ,CAACwU,eAAT,GAA2B,KAAKN,eAAhC,GAAkD,KAAKnE,IAAxD,EAA8D0E,IAA9D,CAAmE,IAAnE,CAD0B,EAE1B,KAAKjB,OAAL,CAAalD,QAFa,CAA5B;AAID;AACF;;SAEDoE,KAAA,YAAGC,KAAH,EAAU;AAAA;;AACR,SAAKzB,cAAL,GAAsBpE,cAAc,CAACM,OAAf,CAAuBiD,oBAAvB,EAA6C,KAAK1H,QAAlD,CAAtB;;AACA,QAAMiK,WAAW,GAAG,KAAKC,aAAL,CAAmB,KAAK3B,cAAxB,CAApB;;AAEA,QAAIyB,KAAK,GAAG,KAAK3B,MAAL,CAAY1L,MAAZ,GAAqB,CAA7B,IAAkCqN,KAAK,GAAG,CAA9C,EAAiD;AAC/C;AACD;;AAED,QAAI,KAAKvB,UAAT,EAAqB;AACnBvM,MAAAA,YAAY,CAACoC,GAAb,CAAiB,KAAK0B,QAAtB,EAAgCuG,UAAhC,EAA4C;AAAA,eAAM,MAAI,CAACwD,EAAL,CAAQC,KAAR,CAAN;AAAA,OAA5C;AACA;AACD;;AAED,QAAIC,WAAW,KAAKD,KAApB,EAA2B;AACzB,WAAKlE,KAAL;AACA,WAAK2D,KAAL;AACA;AACD;;AAED,QAAMU,SAAS,GAAGH,KAAK,GAAGC,WAAR,GAChB/D,cADgB,GAEhBC,cAFF;;AAIA,SAAKmD,MAAL,CAAYa,SAAZ,EAAuB,KAAK9B,MAAL,CAAY2B,KAAZ,CAAvB;AACD;;SAED7J,UAAA,mBAAU;AACR,6BAAMA,OAAN;;AACAjE,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK6D,QAAtB,EAAgCM,WAAhC;AAEA,SAAK+H,MAAL,GAAc,IAAd;AACA,SAAKQ,OAAL,GAAe,IAAf;AACA,SAAKP,SAAL,GAAiB,IAAjB;AACA,SAAKE,SAAL,GAAiB,IAAjB;AACA,SAAKC,UAAL,GAAkB,IAAlB;AACA,SAAKF,cAAL,GAAsB,IAAtB;AACA,SAAKQ,kBAAL,GAA0B,IAA1B;AACD;;;SAIDD,aAAA,oBAAWpR,MAAX,EAAmB;AACjBA,IAAAA,MAAM,gBACDgO,OADC,EAEDhO,MAFC,CAAN;AAIAF,IAAAA,eAAe,CAAC6I,MAAD,EAAO3I,MAAP,EAAeuO,WAAf,CAAf;AACA,WAAOvO,MAAP;AACD;;SAED0S,eAAA,wBAAe;AACb,QAAMC,SAAS,GAAGnV,IAAI,CAACoV,GAAL,CAAS,KAAK1B,WAAd,CAAlB;;AAEA,QAAIyB,SAAS,IAAI5E,eAAjB,EAAkC;AAChC;AACD;;AAED,QAAM0E,SAAS,GAAGE,SAAS,GAAG,KAAKzB,WAAnC;AAEA,SAAKA,WAAL,GAAmB,CAAnB,CATa;;AAYb,QAAIuB,SAAS,GAAG,CAAhB,EAAmB;AACjB,WAAKlF,IAAL;AACD,KAdY;;;AAiBb,QAAIkF,SAAS,GAAG,CAAhB,EAAmB;AACjB,WAAK/E,IAAL;AACD;AACF;;SAEDiE,qBAAA,8BAAqB;AAAA;;AACnB,QAAI,KAAKR,OAAL,CAAajD,QAAjB,EAA2B;AACzB1J,MAAAA,YAAY,CAACmC,EAAb,CAAgB,KAAK2B,QAArB,EAA+BwG,aAA/B,EAA8C,UAAAzK,KAAK;AAAA,eAAI,MAAI,CAACwO,QAAL,CAAcxO,KAAd,CAAJ;AAAA,OAAnD;AACD;;AAED,QAAI,KAAK8M,OAAL,CAAa/C,KAAb,KAAuB,OAA3B,EAAoC;AAClC5J,MAAAA,YAAY,CAACmC,EAAb,CAAgB,KAAK2B,QAArB,EAA+ByG,gBAA/B,EAAiD,UAAA1K,KAAK;AAAA,eAAI,MAAI,CAAC+J,KAAL,CAAW/J,KAAX,CAAJ;AAAA,OAAtD;AACAG,MAAAA,YAAY,CAACmC,EAAb,CAAgB,KAAK2B,QAArB,EAA+B0G,gBAA/B,EAAiD,UAAA3K,KAAK;AAAA,eAAI,MAAI,CAAC0N,KAAL,CAAW1N,KAAX,CAAJ;AAAA,OAAtD;AACD;;AAED,QAAI,KAAK8M,OAAL,CAAa7C,KAAb,IAAsB,KAAKgD,eAA/B,EAAgD;AAC9C,WAAKwB,uBAAL;AACD;AACF;;SAEDA,0BAAA,mCAA0B;AAAA;;AACxB,QAAMC,KAAK,GAAG,SAARA,KAAQ,CAAA1O,KAAK,EAAI;AACrB,UAAI,MAAI,CAACoN,aAAL,IAAsBlB,WAAW,CAAClM,KAAK,CAAC2O,WAAN,CAAkBpS,WAAlB,EAAD,CAArC,EAAwE;AACtE,QAAA,MAAI,CAACqQ,WAAL,GAAmB5M,KAAK,CAAC4O,OAAzB;AACD,OAFD,MAEO,IAAI,CAAC,MAAI,CAACxB,aAAV,EAAyB;AAC9B,QAAA,MAAI,CAACR,WAAL,GAAmB5M,KAAK,CAAC6O,OAAN,CAAc,CAAd,EAAiBD,OAApC;AACD;AACF,KAND;;AAQA,QAAME,IAAI,GAAG,SAAPA,IAAO,CAAA9O,KAAK,EAAI;AACpB;AACA,UAAIA,KAAK,CAAC6O,OAAN,IAAiB7O,KAAK,CAAC6O,OAAN,CAAcjO,MAAd,GAAuB,CAA5C,EAA+C;AAC7C,QAAA,MAAI,CAACiM,WAAL,GAAmB,CAAnB;AACD,OAFD,MAEO;AACL,QAAA,MAAI,CAACA,WAAL,GAAmB7M,KAAK,CAAC6O,OAAN,CAAc,CAAd,EAAiBD,OAAjB,GAA2B,MAAI,CAAChC,WAAnD;AACD;AACF,KAPD;;AASA,QAAMmC,GAAG,GAAG,SAANA,GAAM,CAAA/O,KAAK,EAAI;AACnB,UAAI,MAAI,CAACoN,aAAL,IAAsBlB,WAAW,CAAClM,KAAK,CAAC2O,WAAN,CAAkBpS,WAAlB,EAAD,CAArC,EAAwE;AACtE,QAAA,MAAI,CAACsQ,WAAL,GAAmB7M,KAAK,CAAC4O,OAAN,GAAgB,MAAI,CAAChC,WAAxC;AACD;;AAED,MAAA,MAAI,CAACyB,YAAL;;AACA,UAAI,MAAI,CAACvB,OAAL,CAAa/C,KAAb,KAAuB,OAA3B,EAAoC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,QAAA,MAAI,CAACA,KAAL;;AACA,YAAI,MAAI,CAAC4C,YAAT,EAAuB;AACrBqC,UAAAA,YAAY,CAAC,MAAI,CAACrC,YAAN,CAAZ;AACD;;AAED,QAAA,MAAI,CAACA,YAAL,GAAoBnR,UAAU,CAAC,UAAAwE,KAAK;AAAA,iBAAI,MAAI,CAAC0N,KAAL,CAAW1N,KAAX,CAAJ;AAAA,SAAN,EAA6ByJ,sBAAsB,GAAG,MAAI,CAACqD,OAAL,CAAalD,QAAnE,CAA9B;AACD;AACF,KAtBD;;AAwBAxB,IAAAA,cAAc,CAACE,IAAf,CAAoBuD,iBAApB,EAAuC,KAAK5H,QAA5C,EAAsDlI,OAAtD,CAA8D,UAAAkT,OAAO,EAAI;AACvE9O,MAAAA,YAAY,CAACmC,EAAb,CAAgB2M,OAAhB,EAAyBhE,gBAAzB,EAA2C,UAAAiE,CAAC;AAAA,eAAIA,CAAC,CAACpL,cAAF,EAAJ;AAAA,OAA5C;AACD,KAFD;;AAIA,QAAI,KAAKsJ,aAAT,EAAwB;AACtBjN,MAAAA,YAAY,CAACmC,EAAb,CAAgB,KAAK2B,QAArB,EAA+B8G,iBAA/B,EAAkD,UAAA/K,KAAK;AAAA,eAAI0O,KAAK,CAAC1O,KAAD,CAAT;AAAA,OAAvD;AACAG,MAAAA,YAAY,CAACmC,EAAb,CAAgB,KAAK2B,QAArB,EAA+B+G,eAA/B,EAAgD,UAAAhL,KAAK;AAAA,eAAI+O,GAAG,CAAC/O,KAAD,CAAP;AAAA,OAArD;;AAEA,WAAKiE,QAAL,CAAcuB,SAAd,CAAwB2J,GAAxB,CAA4B1D,wBAA5B;AACD,KALD,MAKO;AACLtL,MAAAA,YAAY,CAACmC,EAAb,CAAgB,KAAK2B,QAArB,EAA+B2G,gBAA/B,EAAiD,UAAA5K,KAAK;AAAA,eAAI0O,KAAK,CAAC1O,KAAD,CAAT;AAAA,OAAtD;AACAG,MAAAA,YAAY,CAACmC,EAAb,CAAgB,KAAK2B,QAArB,EAA+B4G,eAA/B,EAAgD,UAAA7K,KAAK;AAAA,eAAI8O,IAAI,CAAC9O,KAAD,CAAR;AAAA,OAArD;AACAG,MAAAA,YAAY,CAACmC,EAAb,CAAgB,KAAK2B,QAArB,EAA+B6G,cAA/B,EAA+C,UAAA9K,KAAK;AAAA,eAAI+O,GAAG,CAAC/O,KAAD,CAAP;AAAA,OAApD;AACD;AACF;;SAEDwO,WAAA,kBAASxO,KAAT,EAAgB;AACd,QAAI,kBAAkB3D,IAAlB,CAAuB2D,KAAK,CAACU,MAAN,CAAa0O,OAApC,CAAJ,EAAkD;AAChD;AACD;;AAED,YAAQpP,KAAK,CAAC3B,GAAd;AACE,WAAKkL,cAAL;AACEvJ,QAAAA,KAAK,CAAC8D,cAAN;AACA,aAAKoF,IAAL;AACA;;AACF,WAAKM,eAAL;AACExJ,QAAAA,KAAK,CAAC8D,cAAN;AACA,aAAKuF,IAAL;AACA;AARJ;AAWD;;SAED8E,gBAAA,uBAAc1U,OAAd,EAAuB;AACrB,SAAK6S,MAAL,GAAc7S,OAAO,IAAIA,OAAO,CAACiD,UAAnB,GACZ0L,cAAc,CAACE,IAAf,CAAoBsD,aAApB,EAAmCnS,OAAO,CAACiD,UAA3C,CADY,GAEZ,EAFF;AAIA,WAAO,KAAK4P,MAAL,CAAY+C,OAAZ,CAAoB5V,OAApB,CAAP;AACD;;SAED6V,sBAAA,6BAAoBlB,SAApB,EAA+BmB,aAA/B,EAA8C;AAC5C,QAAMC,eAAe,GAAGpB,SAAS,KAAKjE,cAAtC;AACA,QAAMsF,eAAe,GAAGrB,SAAS,KAAKhE,cAAtC;;AACA,QAAM8D,WAAW,GAAG,KAAKC,aAAL,CAAmBoB,aAAnB,CAApB;;AACA,QAAMG,aAAa,GAAG,KAAKpD,MAAL,CAAY1L,MAAZ,GAAqB,CAA3C;AACA,QAAM+O,aAAa,GAAIF,eAAe,IAAIvB,WAAW,KAAK,CAApC,IACGsB,eAAe,IAAItB,WAAW,KAAKwB,aAD5D;;AAGA,QAAIC,aAAa,IAAI,CAAC,KAAK7C,OAAL,CAAa9C,IAAnC,EAAyC;AACvC,aAAOuF,aAAP;AACD;;AAED,QAAMK,KAAK,GAAGxB,SAAS,KAAKhE,cAAd,GAA+B,CAAC,CAAhC,GAAoC,CAAlD;AACA,QAAMyF,SAAS,GAAG,CAAC3B,WAAW,GAAG0B,KAAf,IAAwB,KAAKtD,MAAL,CAAY1L,MAAtD;AAEA,WAAOiP,SAAS,KAAK,CAAC,CAAf,GACL,KAAKvD,MAAL,CAAY,KAAKA,MAAL,CAAY1L,MAAZ,GAAqB,CAAjC,CADK,GAEL,KAAK0L,MAAL,CAAYuD,SAAZ,CAFF;AAGD;;SAEDC,qBAAA,4BAAmBC,aAAnB,EAAkCC,kBAAlC,EAAsD;AACpD,QAAMC,WAAW,GAAG,KAAK9B,aAAL,CAAmB4B,aAAnB,CAApB;;AACA,QAAMG,SAAS,GAAG,KAAK/B,aAAL,CAAmB/F,cAAc,CAACM,OAAf,CAAuBiD,oBAAvB,EAA6C,KAAK1H,QAAlD,CAAnB,CAAlB;;AAEA,WAAO9D,YAAY,CAAC2C,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCsG,WAApC,EAAiD;AACtDwF,MAAAA,aAAa,EAAbA,aADsD;AAEtD3B,MAAAA,SAAS,EAAE4B,kBAF2C;AAGtDG,MAAAA,IAAI,EAAED,SAHgD;AAItDlC,MAAAA,EAAE,EAAEiC;AAJkD,KAAjD,CAAP;AAMD;;SAEDG,6BAAA,oCAA2B3W,OAA3B,EAAoC;AAClC,QAAI,KAAKuT,kBAAT,EAA6B;AAC3B,UAAMqD,UAAU,GAAGjI,cAAc,CAACE,IAAf,CAAoBoD,eAApB,EAAqC,KAAKsB,kBAA1C,CAAnB;;AAEA,WAAK,IAAIrM,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG0P,UAAU,CAACzP,MAA/B,EAAuCD,CAAC,EAAxC,EAA4C;AAC1C0P,QAAAA,UAAU,CAAC1P,CAAD,CAAV,CAAc6E,SAAd,CAAwBC,MAAxB,CAA+BW,mBAA/B;AACD;;AAED,UAAMkK,aAAa,GAAG,KAAKtD,kBAAL,CAAwBrE,QAAxB,CACpB,KAAKwF,aAAL,CAAmB1U,OAAnB,CADoB,CAAtB;;AAIA,UAAI6W,aAAJ,EAAmB;AACjBA,QAAAA,aAAa,CAAC9K,SAAd,CAAwB2J,GAAxB,CAA4B/I,mBAA5B;AACD;AACF;AACF;;SAEDwH,kBAAA,2BAAkB;AAChB,QAAMnU,OAAO,GAAG,KAAK+S,cAAL,IAAuBpE,cAAc,CAACM,OAAf,CAAuBiD,oBAAvB,EAA6C,KAAK1H,QAAlD,CAAvC;;AAEA,QAAI,CAACxK,OAAL,EAAc;AACZ;AACD;;AAED,QAAM8W,eAAe,GAAGhW,MAAM,CAACiW,QAAP,CAAgB/W,OAAO,CAACE,YAAR,CAAqB,kBAArB,CAAhB,EAA0D,EAA1D,CAAxB;;AAEA,QAAI4W,eAAJ,EAAqB;AACnB,WAAKzD,OAAL,CAAa2D,eAAb,GAA+B,KAAK3D,OAAL,CAAa2D,eAAb,IAAgC,KAAK3D,OAAL,CAAalD,QAA5E;AACA,WAAKkD,OAAL,CAAalD,QAAb,GAAwB2G,eAAxB;AACD,KAHD,MAGO;AACL,WAAKzD,OAAL,CAAalD,QAAb,GAAwB,KAAKkD,OAAL,CAAa2D,eAAb,IAAgC,KAAK3D,OAAL,CAAalD,QAArE;AACD;AACF;;SAED2D,SAAA,gBAAOa,SAAP,EAAkB3U,OAAlB,EAA2B;AAAA;;AACzB,QAAM8V,aAAa,GAAGnH,cAAc,CAACM,OAAf,CAAuBiD,oBAAvB,EAA6C,KAAK1H,QAAlD,CAAtB;;AACA,QAAMyM,kBAAkB,GAAG,KAAKvC,aAAL,CAAmBoB,aAAnB,CAA3B;;AACA,QAAMoB,WAAW,GAAGlX,OAAO,IAAK8V,aAAa,IAAI,KAAKD,mBAAL,CAAyBlB,SAAzB,EAAoCmB,aAApC,CAAjD;;AAEA,QAAMqB,gBAAgB,GAAG,KAAKzC,aAAL,CAAmBwC,WAAnB,CAAzB;;AACA,QAAME,SAAS,GAAG7O,OAAO,CAAC,KAAKuK,SAAN,CAAzB;AAEA,QAAIuE,oBAAJ;AACA,QAAIC,cAAJ;AACA,QAAIf,kBAAJ;;AAEA,QAAI5B,SAAS,KAAKjE,cAAlB,EAAkC;AAChC2G,MAAAA,oBAAoB,GAAGxF,gBAAvB;AACAyF,MAAAA,cAAc,GAAGxF,eAAjB;AACAyE,MAAAA,kBAAkB,GAAG3F,cAArB;AACD,KAJD,MAIO;AACLyG,MAAAA,oBAAoB,GAAGzF,cAAvB;AACA0F,MAAAA,cAAc,GAAGvF,eAAjB;AACAwE,MAAAA,kBAAkB,GAAG1F,eAArB;AACD;;AAED,QAAIqG,WAAW,IAAIA,WAAW,CAACnL,SAAZ,CAAsBE,QAAtB,CAA+BU,mBAA/B,CAAnB,EAAsE;AACpE,WAAKsG,UAAL,GAAkB,KAAlB;AACA;AACD;;AAED,QAAMsE,UAAU,GAAG,KAAKlB,kBAAL,CAAwBa,WAAxB,EAAqCX,kBAArC,CAAnB;;AACA,QAAIgB,UAAU,CAAC5N,gBAAf,EAAiC;AAC/B;AACD;;AAED,QAAI,CAACmM,aAAD,IAAkB,CAACoB,WAAvB,EAAoC;AAClC;AACA;AACD;;AAED,SAAKjE,UAAL,GAAkB,IAAlB;;AAEA,QAAImE,SAAJ,EAAe;AACb,WAAK9G,KAAL;AACD;;AAED,SAAKqG,0BAAL,CAAgCO,WAAhC;;AACA,SAAKnE,cAAL,GAAsBmE,WAAtB;;AAEA,QAAI,KAAK1M,QAAL,CAAcuB,SAAd,CAAwBE,QAAxB,CAAiC0F,gBAAjC,CAAJ,EAAwD;AACtDuF,MAAAA,WAAW,CAACnL,SAAZ,CAAsB2J,GAAtB,CAA0B4B,cAA1B;AAEAzT,MAAAA,MAAM,CAACqT,WAAD,CAAN;AAEApB,MAAAA,aAAa,CAAC/J,SAAd,CAAwB2J,GAAxB,CAA4B2B,oBAA5B;AACAH,MAAAA,WAAW,CAACnL,SAAZ,CAAsB2J,GAAtB,CAA0B2B,oBAA1B;AAEA,UAAM1W,kBAAkB,GAAGH,gCAAgC,CAACsV,aAAD,CAA3D;AAEApP,MAAAA,YAAY,CAACoC,GAAb,CAAiBgN,aAAjB,EAAgC9W,cAAhC,EAAgD,YAAM;AACpDkY,QAAAA,WAAW,CAACnL,SAAZ,CAAsBC,MAAtB,CAA6BqL,oBAA7B,EAAmDC,cAAnD;AACAJ,QAAAA,WAAW,CAACnL,SAAZ,CAAsB2J,GAAtB,CAA0B/I,mBAA1B;AAEAmJ,QAAAA,aAAa,CAAC/J,SAAd,CAAwBC,MAAxB,CAA+BW,mBAA/B,EAAkD2K,cAAlD,EAAkED,oBAAlE;AAEA,QAAA,MAAI,CAACpE,UAAL,GAAkB,KAAlB;AAEAlR,QAAAA,UAAU,CAAC,YAAM;AACf2E,UAAAA,YAAY,CAAC2C,OAAb,CAAqB,MAAI,CAACmB,QAA1B,EAAoCuG,UAApC,EAAgD;AAC9CuF,YAAAA,aAAa,EAAEY,WAD+B;AAE9CvC,YAAAA,SAAS,EAAE4B,kBAFmC;AAG9CG,YAAAA,IAAI,EAAEO,kBAHwC;AAI9C1C,YAAAA,EAAE,EAAE4C;AAJ0C,WAAhD;AAMD,SAPS,EAOP,CAPO,CAAV;AAQD,OAhBD;AAkBA5V,MAAAA,oBAAoB,CAACuU,aAAD,EAAgBnV,kBAAhB,CAApB;AACD,KA7BD,MA6BO;AACLmV,MAAAA,aAAa,CAAC/J,SAAd,CAAwBC,MAAxB,CAA+BW,mBAA/B;AACAuK,MAAAA,WAAW,CAACnL,SAAZ,CAAsB2J,GAAtB,CAA0B/I,mBAA1B;AAEA,WAAKsG,UAAL,GAAkB,KAAlB;AACAvM,MAAAA,YAAY,CAAC2C,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCuG,UAApC,EAAgD;AAC9CuF,QAAAA,aAAa,EAAEY,WAD+B;AAE9CvC,QAAAA,SAAS,EAAE4B,kBAFmC;AAG9CG,QAAAA,IAAI,EAAEO,kBAHwC;AAI9C1C,QAAAA,EAAE,EAAE4C;AAJ0C,OAAhD;AAMD;;AAED,QAAIC,SAAJ,EAAe;AACb,WAAKnD,KAAL;AACD;AACF;;;WAIMuD,oBAAP,2BAAyBxX,OAAzB,EAAkCkC,MAAlC,EAA0C;AACxC,QAAI2C,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAarF,OAAb,EAAsB0K,UAAtB,CAAX;;AACA,QAAI2I,OAAO,gBACNnD,OADM,EAEN7C,WAAW,CAACI,iBAAZ,CAA8BzN,OAA9B,CAFM,CAAX;;AAKA,QAAI,OAAOkC,MAAP,KAAkB,QAAtB,EAAgC;AAC9BmR,MAAAA,OAAO,gBACFA,OADE,EAEFnR,MAFE,CAAP;AAID;;AAED,QAAMuV,MAAM,GAAG,OAAOvV,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsCmR,OAAO,CAAChD,KAA7D;;AAEA,QAAI,CAACxL,IAAL,EAAW;AACTA,MAAAA,IAAI,GAAG,IAAI+N,QAAJ,CAAa5S,OAAb,EAAsBqT,OAAtB,CAAP;AACD;;AAED,QAAI,OAAOnR,MAAP,KAAkB,QAAtB,EAAgC;AAC9B2C,MAAAA,IAAI,CAAC0P,EAAL,CAAQrS,MAAR;AACD,KAFD,MAEO,IAAI,OAAOuV,MAAP,KAAkB,QAAtB,EAAgC;AACrC,UAAI,OAAO5S,IAAI,CAAC4S,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,cAAM,IAAIC,SAAJ,wBAAkCD,MAAlC,QAAN;AACD;;AAED5S,MAAAA,IAAI,CAAC4S,MAAD,CAAJ;AACD,KANM,MAMA,IAAIpE,OAAO,CAAClD,QAAR,IAAoBkD,OAAO,CAACsE,IAAhC,EAAsC;AAC3C9S,MAAAA,IAAI,CAACyL,KAAL;AACAzL,MAAAA,IAAI,CAACoP,KAAL;AACD;AACF;;WAEM7H,kBAAP,yBAAuBlK,MAAvB,EAA+B;AAC7B,WAAO,KAAKmK,IAAL,CAAU,YAAY;AAC3BuG,MAAAA,QAAQ,CAAC4E,iBAAT,CAA2B,IAA3B,EAAiCtV,MAAjC;AACD,KAFM,CAAP;AAGD;;WAEM0V,sBAAP,6BAA2BrR,KAA3B,EAAkC;AAChC,QAAMU,MAAM,GAAG1G,sBAAsB,CAAC,IAAD,CAArC;;AAEA,QAAI,CAAC0G,MAAD,IAAW,CAACA,MAAM,CAAC8E,SAAP,CAAiBE,QAAjB,CAA0ByF,mBAA1B,CAAhB,EAAgE;AAC9D;AACD;;AAED,QAAMxP,MAAM,gBACPmL,WAAW,CAACI,iBAAZ,CAA8BxG,MAA9B,CADO,EAEPoG,WAAW,CAACI,iBAAZ,CAA8B,IAA9B,CAFO,CAAZ;;AAIA,QAAMoK,UAAU,GAAG,KAAK3X,YAAL,CAAkB,kBAAlB,CAAnB;;AAEA,QAAI2X,UAAJ,EAAgB;AACd3V,MAAAA,MAAM,CAACiO,QAAP,GAAkB,KAAlB;AACD;;AAEDyC,IAAAA,QAAQ,CAAC4E,iBAAT,CAA2BvQ,MAA3B,EAAmC/E,MAAnC;;AAEA,QAAI2V,UAAJ,EAAgB;AACd3S,MAAAA,IAAI,CAACG,OAAL,CAAa4B,MAAb,EAAqByD,UAArB,EAA+B6J,EAA/B,CAAkCsD,UAAlC;AACD;;AAEDtR,IAAAA,KAAK,CAAC8D,cAAN;AACD;;;;wBA7coB;AACnB,aAAO6F,OAAP;AACD;;;wBAEqB;AACpB,aAAOxF,UAAP;AACD;;;;EA7BoBH;AAuevB;AACA;AACA;AACA;AACA;;;AAEA7D,YAAY,CAACmC,EAAb,CAAgBhJ,QAAhB,EAA0BsL,sBAA1B,EAAgDoH,mBAAhD,EAAqEK,QAAQ,CAACgF,mBAA9E;AAEAlR,YAAY,CAACmC,EAAb,CAAgBpI,MAAhB,EAAwBgR,mBAAxB,EAA6C,YAAM;AACjD,MAAMqG,SAAS,GAAGnJ,cAAc,CAACE,IAAf,CAAoB2D,kBAApB,CAAlB;;AAEA,OAAK,IAAItL,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAGsQ,SAAS,CAAC3Q,MAAhC,EAAwCD,CAAC,GAAGM,GAA5C,EAAiDN,CAAC,EAAlD,EAAsD;AACpD0L,IAAAA,QAAQ,CAAC4E,iBAAT,CAA2BM,SAAS,CAAC5Q,CAAD,CAApC,EAAyChC,IAAI,CAACG,OAAL,CAAayS,SAAS,CAAC5Q,CAAD,CAAtB,EAA2BwD,UAA3B,CAAzC;AACD;AACF,CAND;AAQA;AACA;AACA;AACA;AACA;AACA;;AAEAvG,kBAAkB,CAAC,YAAM;AACvB,MAAMoF,CAAC,GAAGxF,SAAS,EAAnB;AACA;;AACA,MAAIwF,CAAJ,EAAO;AACL,QAAMiD,kBAAkB,GAAGjD,CAAC,CAAClD,EAAF,CAAKwE,MAAL,CAA3B;AACAtB,IAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,IAAa+H,QAAQ,CAACxG,eAAtB;AACA7C,IAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,EAAW4B,WAAX,GAAyBmG,QAAzB;;AACArJ,IAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,EAAW6B,UAAX,GAAwB,YAAM;AAC5BnD,MAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,IAAa2B,kBAAb;AACA,aAAOoG,QAAQ,CAACxG,eAAhB;AACD,KAHD;AAID;AACF,CAZiB,CAAlB;;AC/kBA;AACA;AACA;AACA;AACA;;AAEA,IAAMvB,MAAI,GAAG,UAAb;AACA,IAAMH,UAAQ,GAAG,aAAjB;AACA,IAAMI,WAAS,SAAOJ,UAAtB;AACA,IAAMK,cAAY,GAAG,WAArB;AAEA,IAAMmF,SAAO,GAAG;AACdpD,EAAAA,MAAM,EAAE,IADM;AAEdiL,EAAAA,MAAM,EAAE;AAFM,CAAhB;AAKA,IAAMtH,aAAW,GAAG;AAClB3D,EAAAA,MAAM,EAAE,SADU;AAElBiL,EAAAA,MAAM,EAAE;AAFU,CAApB;AAKA,IAAMC,UAAU,YAAUlN,WAA1B;AACA,IAAMmN,WAAW,aAAWnN,WAA5B;AACA,IAAMoN,UAAU,YAAUpN,WAA1B;AACA,IAAMqN,YAAY,cAAYrN,WAA9B;AACA,IAAMK,sBAAoB,aAAWL,WAAX,GAAuBC,cAAjD;AAEA,IAAMqN,eAAe,GAAG,MAAxB;AACA,IAAMC,mBAAmB,GAAG,UAA5B;AACA,IAAMC,qBAAqB,GAAG,YAA9B;AACA,IAAMC,oBAAoB,GAAG,WAA7B;AAEA,IAAMC,KAAK,GAAG,OAAd;AACA,IAAMC,MAAM,GAAG,QAAf;AAEA,IAAMC,gBAAgB,GAAG,oBAAzB;AACA,IAAM9L,sBAAoB,GAAG,6BAA7B;AAEA;AACA;AACA;AACA;AACA;;IAEM+L;;;AACJ,oBAAY3Y,OAAZ,EAAqBkC,MAArB,EAA6B;AAAA;;AAC3B,sCAAMlC,OAAN;AAEA,UAAK4Y,gBAAL,GAAwB,KAAxB;AACA,UAAKvF,OAAL,GAAe,MAAKC,UAAL,CAAgBpR,MAAhB,CAAf;AACA,UAAK2W,aAAL,GAAqBlK,cAAc,CAACE,IAAf,CAChBjC,sBAAH,iBAAkC5M,OAAO,CAAC0E,EAA1C,aACGkI,sBADH,2BAC4C5M,OAAO,CAAC0E,EADpD,SADmB,CAArB;AAKA,QAAMoU,UAAU,GAAGnK,cAAc,CAACE,IAAf,CAAoBjC,sBAApB,CAAnB;;AAEA,SAAK,IAAI1F,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAGsR,UAAU,CAAC3R,MAAjC,EAAyCD,CAAC,GAAGM,GAA7C,EAAkDN,CAAC,EAAnD,EAAuD;AACrD,UAAM6R,IAAI,GAAGD,UAAU,CAAC5R,CAAD,CAAvB;AACA,UAAMjH,QAAQ,GAAGI,sBAAsB,CAAC0Y,IAAD,CAAvC;AACA,UAAMC,aAAa,GAAGrK,cAAc,CAACE,IAAf,CAAoB5O,QAApB,EACnB2N,MADmB,CACZ,UAAAqL,SAAS;AAAA,eAAIA,SAAS,KAAKjZ,OAAlB;AAAA,OADG,CAAtB;;AAGA,UAAIC,QAAQ,KAAK,IAAb,IAAqB+Y,aAAa,CAAC7R,MAAvC,EAA+C;AAC7C,cAAK+R,SAAL,GAAiBjZ,QAAjB;;AACA,cAAK4Y,aAAL,CAAmBrJ,IAAnB,CAAwBuJ,IAAxB;AACD;AACF;;AAED,UAAKI,OAAL,GAAe,MAAK9F,OAAL,CAAa0E,MAAb,GAAsB,MAAKqB,UAAL,EAAtB,GAA0C,IAAzD;;AAEA,QAAI,CAAC,MAAK/F,OAAL,CAAa0E,MAAlB,EAA0B;AACxB,YAAKsB,yBAAL,CAA+B,MAAK7O,QAApC,EAA8C,MAAKqO,aAAnD;AACD;;AAED,QAAI,MAAKxF,OAAL,CAAavG,MAAjB,EAAyB;AACvB,YAAKA,MAAL;AACD;;AAhC0B;AAiC5B;;;;;AAYD;SAEAA,SAAA,kBAAS;AACP,QAAI,KAAKtC,QAAL,CAAcuB,SAAd,CAAwBE,QAAxB,CAAiCmM,eAAjC,CAAJ,EAAuD;AACrD,WAAKkB,IAAL;AACD,KAFD,MAEO;AACL,WAAKC,IAAL;AACD;AACF;;SAEDA,OAAA,gBAAO;AAAA;;AACL,QAAI,KAAKX,gBAAL,IAAyB,KAAKpO,QAAL,CAAcuB,SAAd,CAAwBE,QAAxB,CAAiCmM,eAAjC,CAA7B,EAAgF;AAC9E;AACD;;AAED,QAAIoB,OAAJ;AACA,QAAIC,WAAJ;;AAEA,QAAI,KAAKN,OAAT,EAAkB;AAChBK,MAAAA,OAAO,GAAG7K,cAAc,CAACE,IAAf,CAAoB6J,gBAApB,EAAsC,KAAKS,OAA3C,EACPvL,MADO,CACA,UAAAmL,IAAI,EAAI;AACd,YAAI,OAAO,MAAI,CAAC1F,OAAL,CAAa0E,MAApB,KAA+B,QAAnC,EAA6C;AAC3C,iBAAOgB,IAAI,CAAC7Y,YAAL,CAAkB,gBAAlB,MAAwC,MAAI,CAACmT,OAAL,CAAa0E,MAA5D;AACD;;AAED,eAAOgB,IAAI,CAAChN,SAAL,CAAeE,QAAf,CAAwBoM,mBAAxB,CAAP;AACD,OAPO,CAAV;;AASA,UAAImB,OAAO,CAACrS,MAAR,KAAmB,CAAvB,EAA0B;AACxBqS,QAAAA,OAAO,GAAG,IAAV;AACD;AACF;;AAED,QAAME,SAAS,GAAG/K,cAAc,CAACM,OAAf,CAAuB,KAAKiK,SAA5B,CAAlB;;AACA,QAAIM,OAAJ,EAAa;AACX,UAAMG,cAAc,GAAGH,OAAO,CAAC3K,IAAR,CAAa,UAAAkK,IAAI;AAAA,eAAIW,SAAS,KAAKX,IAAlB;AAAA,OAAjB,CAAvB;AACAU,MAAAA,WAAW,GAAGE,cAAc,GAAGzU,IAAI,CAACG,OAAL,CAAasU,cAAb,EAA6BjP,UAA7B,CAAH,GAA4C,IAAxE;;AAEA,UAAI+O,WAAW,IAAIA,WAAW,CAACb,gBAA/B,EAAiD;AAC/C;AACD;AACF;;AAED,QAAMgB,UAAU,GAAGlT,YAAY,CAAC2C,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCwN,UAApC,CAAnB;;AACA,QAAI4B,UAAU,CAACjQ,gBAAf,EAAiC;AAC/B;AACD;;AAED,QAAI6P,OAAJ,EAAa;AACXA,MAAAA,OAAO,CAAClX,OAAR,CAAgB,UAAAuX,UAAU,EAAI;AAC5B,YAAIH,SAAS,KAAKG,UAAlB,EAA8B;AAC5BlB,UAAAA,QAAQ,CAACmB,iBAAT,CAA2BD,UAA3B,EAAuC,MAAvC;AACD;;AAED,YAAI,CAACJ,WAAL,EAAkB;AAChBvU,UAAAA,IAAI,CAACC,OAAL,CAAa0U,UAAb,EAAyBnP,UAAzB,EAAmC,IAAnC;AACD;AACF,OARD;AASD;;AAED,QAAMqP,SAAS,GAAG,KAAKC,aAAL,EAAlB;;AAEA,SAAKxP,QAAL,CAAcuB,SAAd,CAAwBC,MAAxB,CAA+BqM,mBAA/B;;AACA,SAAK7N,QAAL,CAAcuB,SAAd,CAAwB2J,GAAxB,CAA4B4C,qBAA5B;;AAEA,SAAK9N,QAAL,CAAcxH,KAAd,CAAoB+W,SAApB,IAAiC,CAAjC;;AAEA,QAAI,KAAKlB,aAAL,CAAmB1R,MAAvB,EAA+B;AAC7B,WAAK0R,aAAL,CAAmBvW,OAAnB,CAA2B,UAAAtC,OAAO,EAAI;AACpCA,QAAAA,OAAO,CAAC+L,SAAR,CAAkBC,MAAlB,CAAyBuM,oBAAzB;AACAvY,QAAAA,OAAO,CAAC+M,YAAR,CAAqB,eAArB,EAAsC,IAAtC;AACD,OAHD;AAID;;AAED,SAAKkN,gBAAL,CAAsB,IAAtB;;AAEA,QAAMC,QAAQ,GAAG,SAAXA,QAAW,GAAM;AACrB,MAAA,MAAI,CAAC1P,QAAL,CAAcuB,SAAd,CAAwBC,MAAxB,CAA+BsM,qBAA/B;;AACA,MAAA,MAAI,CAAC9N,QAAL,CAAcuB,SAAd,CAAwB2J,GAAxB,CAA4B2C,mBAA5B,EAAiDD,eAAjD;;AAEA,MAAA,MAAI,CAAC5N,QAAL,CAAcxH,KAAd,CAAoB+W,SAApB,IAAiC,EAAjC;;AAEA,MAAA,MAAI,CAACE,gBAAL,CAAsB,KAAtB;;AAEAvT,MAAAA,YAAY,CAAC2C,OAAb,CAAqB,MAAI,CAACmB,QAA1B,EAAoCyN,WAApC;AACD,KATD;;AAWA,QAAMkC,oBAAoB,GAAGJ,SAAS,CAAC,CAAD,CAAT,CAAajX,WAAb,KAA6BiX,SAAS,CAAC5Q,KAAV,CAAgB,CAAhB,CAA1D;AACA,QAAMiR,UAAU,cAAYD,oBAA5B;AACA,QAAMxZ,kBAAkB,GAAGH,gCAAgC,CAAC,KAAKgK,QAAN,CAA3D;AAEA9D,IAAAA,YAAY,CAACoC,GAAb,CAAiB,KAAK0B,QAAtB,EAAgCxL,cAAhC,EAAgDkb,QAAhD;AAEA3Y,IAAAA,oBAAoB,CAAC,KAAKiJ,QAAN,EAAgB7J,kBAAhB,CAApB;AACA,SAAK6J,QAAL,CAAcxH,KAAd,CAAoB+W,SAApB,IAAoC,KAAKvP,QAAL,CAAc4P,UAAd,CAApC;AACD;;SAEDd,OAAA,gBAAO;AAAA;;AACL,QAAI,KAAKV,gBAAL,IAAyB,CAAC,KAAKpO,QAAL,CAAcuB,SAAd,CAAwBE,QAAxB,CAAiCmM,eAAjC,CAA9B,EAAiF;AAC/E;AACD;;AAED,QAAMwB,UAAU,GAAGlT,YAAY,CAAC2C,OAAb,CAAqB,KAAKmB,QAA1B,EAAoC0N,UAApC,CAAnB;;AACA,QAAI0B,UAAU,CAACjQ,gBAAf,EAAiC;AAC/B;AACD;;AAED,QAAMoQ,SAAS,GAAG,KAAKC,aAAL,EAAlB;;AAEA,SAAKxP,QAAL,CAAcxH,KAAd,CAAoB+W,SAApB,IAAoC,KAAKvP,QAAL,CAAc0D,qBAAd,GAAsC6L,SAAtC,CAApC;AAEAlW,IAAAA,MAAM,CAAC,KAAK2G,QAAN,CAAN;;AAEA,SAAKA,QAAL,CAAcuB,SAAd,CAAwB2J,GAAxB,CAA4B4C,qBAA5B;;AACA,SAAK9N,QAAL,CAAcuB,SAAd,CAAwBC,MAAxB,CAA+BqM,mBAA/B,EAAoDD,eAApD;;AAEA,QAAMiC,kBAAkB,GAAG,KAAKxB,aAAL,CAAmB1R,MAA9C;;AACA,QAAIkT,kBAAkB,GAAG,CAAzB,EAA4B;AAC1B,WAAK,IAAInT,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGmT,kBAApB,EAAwCnT,CAAC,EAAzC,EAA6C;AAC3C,YAAMmC,OAAO,GAAG,KAAKwP,aAAL,CAAmB3R,CAAnB,CAAhB;AACA,YAAM6R,IAAI,GAAGxY,sBAAsB,CAAC8I,OAAD,CAAnC;;AAEA,YAAI0P,IAAI,IAAI,CAACA,IAAI,CAAChN,SAAL,CAAeE,QAAf,CAAwBmM,eAAxB,CAAb,EAAuD;AACrD/O,UAAAA,OAAO,CAAC0C,SAAR,CAAkB2J,GAAlB,CAAsB6C,oBAAtB;AACAlP,UAAAA,OAAO,CAAC0D,YAAR,CAAqB,eAArB,EAAsC,KAAtC;AACD;AACF;AACF;;AAED,SAAKkN,gBAAL,CAAsB,IAAtB;;AAEA,QAAMC,QAAQ,GAAG,SAAXA,QAAW,GAAM;AACrB,MAAA,MAAI,CAACD,gBAAL,CAAsB,KAAtB;;AACA,MAAA,MAAI,CAACzP,QAAL,CAAcuB,SAAd,CAAwBC,MAAxB,CAA+BsM,qBAA/B;;AACA,MAAA,MAAI,CAAC9N,QAAL,CAAcuB,SAAd,CAAwB2J,GAAxB,CAA4B2C,mBAA5B;;AACA3R,MAAAA,YAAY,CAAC2C,OAAb,CAAqB,MAAI,CAACmB,QAA1B,EAAoC2N,YAApC;AACD,KALD;;AAOA,SAAK3N,QAAL,CAAcxH,KAAd,CAAoB+W,SAApB,IAAiC,EAAjC;AACA,QAAMpZ,kBAAkB,GAAGH,gCAAgC,CAAC,KAAKgK,QAAN,CAA3D;AAEA9D,IAAAA,YAAY,CAACoC,GAAb,CAAiB,KAAK0B,QAAtB,EAAgCxL,cAAhC,EAAgDkb,QAAhD;AACA3Y,IAAAA,oBAAoB,CAAC,KAAKiJ,QAAN,EAAgB7J,kBAAhB,CAApB;AACD;;SAEDsZ,mBAAA,0BAAiBK,eAAjB,EAAkC;AAChC,SAAK1B,gBAAL,GAAwB0B,eAAxB;AACD;;SAED3P,UAAA,mBAAU;AACR,6BAAMA,OAAN;;AACA,SAAK0I,OAAL,GAAe,IAAf;AACA,SAAK8F,OAAL,GAAe,IAAf;AACA,SAAKN,aAAL,GAAqB,IAArB;AACA,SAAKD,gBAAL,GAAwB,IAAxB;AACD;;;SAIDtF,aAAA,oBAAWpR,MAAX,EAAmB;AACjBA,IAAAA,MAAM,gBACDgO,SADC,EAEDhO,MAFC,CAAN;AAIAA,IAAAA,MAAM,CAAC4K,MAAP,GAAgBvE,OAAO,CAACrG,MAAM,CAAC4K,MAAR,CAAvB,CALiB;;AAMjB9K,IAAAA,eAAe,CAAC6I,MAAD,EAAO3I,MAAP,EAAeuO,aAAf,CAAf;AACA,WAAOvO,MAAP;AACD;;SAED8X,gBAAA,yBAAgB;AACd,WAAO,KAAKxP,QAAL,CAAcuB,SAAd,CAAwBE,QAAxB,CAAiCuM,KAAjC,IAA0CA,KAA1C,GAAkDC,MAAzD;AACD;;SAEDW,aAAA,sBAAa;AAAA;;AAAA,QACLrB,MADK,GACM,KAAK1E,OADX,CACL0E,MADK;;AAGX,QAAI1W,SAAS,CAAC0W,MAAD,CAAb,EAAuB;AACrB;AACA,UAAI,OAAOA,MAAM,CAACwC,MAAd,KAAyB,WAAzB,IAAwC,OAAOxC,MAAM,CAAC,CAAD,CAAb,KAAqB,WAAjE,EAA8E;AAC5EA,QAAAA,MAAM,GAAGA,MAAM,CAAC,CAAD,CAAf;AACD;AACF,KALD,MAKO;AACLA,MAAAA,MAAM,GAAGpJ,cAAc,CAACM,OAAf,CAAuB8I,MAAvB,CAAT;AACD;;AAED,QAAM9X,QAAQ,GAAM2M,sBAAN,0BAA8CmL,MAA9C,QAAd;AAEApJ,IAAAA,cAAc,CAACE,IAAf,CAAoB5O,QAApB,EAA8B8X,MAA9B,EACGzV,OADH,CACW,UAAAtC,OAAO,EAAI;AAClB,UAAMwa,QAAQ,GAAGja,sBAAsB,CAACP,OAAD,CAAvC;;AAEA,MAAA,MAAI,CAACqZ,yBAAL,CACEmB,QADF,EAEE,CAACxa,OAAD,CAFF;AAID,KARH;AAUA,WAAO+X,MAAP;AACD;;SAEDsB,4BAAA,mCAA0BrZ,OAA1B,EAAmCya,YAAnC,EAAiD;AAC/C,QAAI,CAACza,OAAD,IAAY,CAACya,YAAY,CAACtT,MAA9B,EAAsC;AACpC;AACD;;AAED,QAAMuT,MAAM,GAAG1a,OAAO,CAAC+L,SAAR,CAAkBE,QAAlB,CAA2BmM,eAA3B,CAAf;AAEAqC,IAAAA,YAAY,CAACnY,OAAb,CAAqB,UAAAyW,IAAI,EAAI;AAC3B,UAAI2B,MAAJ,EAAY;AACV3B,QAAAA,IAAI,CAAChN,SAAL,CAAeC,MAAf,CAAsBuM,oBAAtB;AACD,OAFD,MAEO;AACLQ,QAAAA,IAAI,CAAChN,SAAL,CAAe2J,GAAf,CAAmB6C,oBAAnB;AACD;;AAEDQ,MAAAA,IAAI,CAAChM,YAAL,CAAkB,eAAlB,EAAmC2N,MAAnC;AACD,KARD;AASD;;;WAIMZ,oBAAP,2BAAyB9Z,OAAzB,EAAkCkC,MAAlC,EAA0C;AACxC,QAAI2C,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAarF,OAAb,EAAsB0K,UAAtB,CAAX;;AACA,QAAM2I,OAAO,gBACRnD,SADQ,EAER7C,WAAW,CAACI,iBAAZ,CAA8BzN,OAA9B,CAFQ,EAGP,OAAOkC,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAHzC,CAAb;;AAMA,QAAI,CAAC2C,IAAD,IAASwO,OAAO,CAACvG,MAAjB,IAA2B,OAAO5K,MAAP,KAAkB,QAA7C,IAAyD,YAAYU,IAAZ,CAAiBV,MAAjB,CAA7D,EAAuF;AACrFmR,MAAAA,OAAO,CAACvG,MAAR,GAAiB,KAAjB;AACD;;AAED,QAAI,CAACjI,IAAL,EAAW;AACTA,MAAAA,IAAI,GAAG,IAAI8T,QAAJ,CAAa3Y,OAAb,EAAsBqT,OAAtB,CAAP;AACD;;AAED,QAAI,OAAOnR,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,UAAI,OAAO2C,IAAI,CAAC3C,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,cAAM,IAAIwV,SAAJ,wBAAkCxV,MAAlC,QAAN;AACD;;AAED2C,MAAAA,IAAI,CAAC3C,MAAD,CAAJ;AACD;AACF;;WAEMkK,kBAAP,yBAAuBlK,MAAvB,EAA+B;AAC7B,WAAO,KAAKmK,IAAL,CAAU,YAAY;AAC3BsM,MAAAA,QAAQ,CAACmB,iBAAT,CAA2B,IAA3B,EAAiC5X,MAAjC;AACD,KAFM,CAAP;AAGD;;;;wBAjQoB;AACnB,aAAOgO,SAAP;AACD;;;wBAEqB;AACpB,aAAOxF,UAAP;AACD;;;;EA5CoBH;AA0SvB;AACA;AACA;AACA;AACA;;;AAEA7D,YAAY,CAACmC,EAAb,CAAgBhJ,QAAhB,EAA0BsL,sBAA1B,EAAgDyB,sBAAhD,EAAsE,UAAUrG,KAAV,EAAiB;AACrF;AACA,MAAIA,KAAK,CAACU,MAAN,CAAa0O,OAAb,KAAyB,GAA7B,EAAkC;AAChCpP,IAAAA,KAAK,CAAC8D,cAAN;AACD;;AAED,MAAMsQ,WAAW,GAAGtN,WAAW,CAACI,iBAAZ,CAA8B,IAA9B,CAApB;AACA,MAAMxN,QAAQ,GAAGI,sBAAsB,CAAC,IAAD,CAAvC;AACA,MAAMua,gBAAgB,GAAGjM,cAAc,CAACE,IAAf,CAAoB5O,QAApB,CAAzB;AAEA2a,EAAAA,gBAAgB,CAACtY,OAAjB,CAAyB,UAAAtC,OAAO,EAAI;AAClC,QAAM6E,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAarF,OAAb,EAAsB0K,UAAtB,CAAb;AACA,QAAIxI,MAAJ;;AACA,QAAI2C,IAAJ,EAAU;AACR;AACA,UAAIA,IAAI,CAACsU,OAAL,KAAiB,IAAjB,IAAyB,OAAOwB,WAAW,CAAC5C,MAAnB,KAA8B,QAA3D,EAAqE;AACnElT,QAAAA,IAAI,CAACwO,OAAL,CAAa0E,MAAb,GAAsB4C,WAAW,CAAC5C,MAAlC;AACAlT,QAAAA,IAAI,CAACsU,OAAL,GAAetU,IAAI,CAACuU,UAAL,EAAf;AACD;;AAEDlX,MAAAA,MAAM,GAAG,QAAT;AACD,KARD,MAQO;AACLA,MAAAA,MAAM,GAAGyY,WAAT;AACD;;AAEDhC,IAAAA,QAAQ,CAACmB,iBAAT,CAA2B9Z,OAA3B,EAAoCkC,MAApC;AACD,GAhBD;AAiBD,CA3BD;AA6BA;AACA;AACA;AACA;AACA;AACA;;AAEAiC,kBAAkB,CAAC,YAAM;AACvB,MAAMoF,CAAC,GAAGxF,SAAS,EAAnB;AACA;;AACA,MAAIwF,CAAJ,EAAO;AACL,QAAMiD,kBAAkB,GAAGjD,CAAC,CAAClD,EAAF,CAAKwE,MAAL,CAA3B;AACAtB,IAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,IAAa8N,QAAQ,CAACvM,eAAtB;AACA7C,IAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,EAAW4B,WAAX,GAAyBkM,QAAzB;;AACApP,IAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,EAAW6B,UAAX,GAAwB,YAAM;AAC5BnD,MAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,IAAa2B,kBAAb;AACA,aAAOmM,QAAQ,CAACvM,eAAhB;AACD,KAHD;AAID;AACF,CAZiB,CAAlB;;AChYA;AACA;AACA;AACA;AACA;;AAEA,IAAMvB,MAAI,GAAG,UAAb;AACA,IAAMH,UAAQ,GAAG,aAAjB;AACA,IAAMI,WAAS,SAAOJ,UAAtB;AACA,IAAMK,cAAY,GAAG,WAArB;AAEA,IAAM8P,UAAU,GAAG,QAAnB;AACA,IAAMC,SAAS,GAAG,OAAlB;AACA,IAAMC,OAAO,GAAG,KAAhB;AACA,IAAMC,YAAY,GAAG,SAArB;AACA,IAAMC,cAAc,GAAG,WAAvB;AACA,IAAMC,kBAAkB,GAAG,CAA3B;;AAEA,IAAMC,cAAc,GAAG,IAAIxY,MAAJ,CAAcqY,YAAd,SAA8BC,cAA9B,SAAgDJ,UAAhD,CAAvB;AAEA,IAAM3C,YAAU,YAAUpN,WAA1B;AACA,IAAMqN,cAAY,cAAYrN,WAA9B;AACA,IAAMkN,YAAU,YAAUlN,WAA1B;AACA,IAAMmN,aAAW,aAAWnN,WAA5B;AACA,IAAMsQ,WAAW,aAAWtQ,WAA5B;AACA,IAAMK,sBAAoB,aAAWL,WAAX,GAAuBC,cAAjD;AACA,IAAMsQ,sBAAsB,eAAavQ,WAAb,GAAyBC,cAArD;AACA,IAAMuQ,oBAAoB,aAAWxQ,WAAX,GAAuBC,cAAjD;AAEA,IAAMwQ,mBAAmB,GAAG,UAA5B;AACA,IAAMnD,iBAAe,GAAG,MAAxB;AACA,IAAMoD,iBAAiB,GAAG,QAA1B;AACA,IAAMC,kBAAkB,GAAG,SAA3B;AACA,IAAMC,oBAAoB,GAAG,WAA7B;AACA,IAAMC,iBAAiB,GAAG,QAA1B;AAEA,IAAM/O,sBAAoB,GAAG,6BAA7B;AACA,IAAMgP,mBAAmB,GAAG,gBAA5B;AACA,IAAMC,aAAa,GAAG,gBAAtB;AACA,IAAMC,mBAAmB,GAAG,aAA5B;AACA,IAAMC,sBAAsB,GAAG,6DAA/B;AAEA,IAAMC,aAAa,GAAG1X,KAAK,GAAG,SAAH,GAAe,WAA1C;AACA,IAAM2X,gBAAgB,GAAG3X,KAAK,GAAG,WAAH,GAAiB,SAA/C;AACA,IAAM4X,gBAAgB,GAAG5X,KAAK,GAAG,YAAH,GAAkB,cAAhD;AACA,IAAM6X,mBAAmB,GAAG7X,KAAK,GAAG,cAAH,GAAoB,YAArD;AACA,IAAM8X,eAAe,GAAG9X,KAAK,GAAG,YAAH,GAAkB,aAA/C;AACA,IAAM+X,cAAc,GAAG/X,KAAK,GAAG,aAAH,GAAmB,YAA/C;AAEA,IAAM4L,SAAO,GAAG;AACdlC,EAAAA,MAAM,EAAE,CADM;AAEdsO,EAAAA,IAAI,EAAE,IAFQ;AAGdC,EAAAA,QAAQ,EAAE,iBAHI;AAIdC,EAAAA,SAAS,EAAE,QAJG;AAKdpZ,EAAAA,OAAO,EAAE,SALK;AAMdqZ,EAAAA,YAAY,EAAE;AANA,CAAhB;AASA,IAAMhM,aAAW,GAAG;AAClBzC,EAAAA,MAAM,EAAE,0BADU;AAElBsO,EAAAA,IAAI,EAAE,SAFY;AAGlBC,EAAAA,QAAQ,EAAE,kBAHQ;AAIlBC,EAAAA,SAAS,EAAE,kBAJO;AAKlBpZ,EAAAA,OAAO,EAAE,QALS;AAMlBqZ,EAAAA,YAAY,EAAE;AANI,CAApB;AASA;AACA;AACA;AACA;AACA;;IAEMC;;;AACJ,oBAAY1c,OAAZ,EAAqBkC,MAArB,EAA6B;AAAA;;AAC3B,sCAAMlC,OAAN;AAEA,UAAK2c,OAAL,GAAe,IAAf;AACA,UAAKtJ,OAAL,GAAe,MAAKC,UAAL,CAAgBpR,MAAhB,CAAf;AACA,UAAK0a,KAAL,GAAa,MAAKC,eAAL,EAAb;AACA,UAAKC,SAAL,GAAiB,MAAKC,aAAL,EAAjB;;AAEA,UAAKlJ,kBAAL;;AAR2B;AAS5B;;;;;AAgBD;SAEA/G,SAAA,kBAAS;AACP,QAAI,KAAKtC,QAAL,CAAcwS,QAAd,IAA0B,KAAKxS,QAAL,CAAcuB,SAAd,CAAwBE,QAAxB,CAAiCsP,mBAAjC,CAA9B,EAAqF;AACnF;AACD;;AAED,QAAM0B,QAAQ,GAAG,KAAKzS,QAAL,CAAcuB,SAAd,CAAwBE,QAAxB,CAAiCmM,iBAAjC,CAAjB;;AAEAsE,IAAAA,QAAQ,CAACQ,UAAT;;AAEA,QAAID,QAAJ,EAAc;AACZ;AACD;;AAED,SAAK1D,IAAL;AACD;;SAEDA,OAAA,gBAAO;AACL,QAAI,KAAK/O,QAAL,CAAcwS,QAAd,IAA0B,KAAKxS,QAAL,CAAcuB,SAAd,CAAwBE,QAAxB,CAAiCsP,mBAAjC,CAA1B,IAAmF,KAAKqB,KAAL,CAAW7Q,SAAX,CAAqBE,QAArB,CAA8BmM,iBAA9B,CAAvF,EAAuI;AACrI;AACD;;AAED,QAAML,MAAM,GAAG2E,QAAQ,CAACS,oBAAT,CAA8B,KAAK3S,QAAnC,CAAf;AACA,QAAM8L,aAAa,GAAG;AACpBA,MAAAA,aAAa,EAAE,KAAK9L;AADA,KAAtB;AAIA,QAAM4S,SAAS,GAAG1W,YAAY,CAAC2C,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCwN,YAApC,EAAgD1B,aAAhD,CAAlB;;AAEA,QAAI8G,SAAS,CAACzT,gBAAd,EAAgC;AAC9B;AACD,KAdI;;;AAiBL,QAAI,CAAC,KAAKmT,SAAV,EAAqB;AACnB,UAAI,OAAOO,MAAP,KAAkB,WAAtB,EAAmC;AACjC,cAAM,IAAI3F,SAAJ,CAAc,+DAAd,CAAN;AACD;;AAED,UAAI4F,gBAAgB,GAAG,KAAK9S,QAA5B;;AAEA,UAAI,KAAK6I,OAAL,CAAamJ,SAAb,KAA2B,QAA/B,EAAyC;AACvCc,QAAAA,gBAAgB,GAAGvF,MAAnB;AACD,OAFD,MAEO,IAAI1W,SAAS,CAAC,KAAKgS,OAAL,CAAamJ,SAAd,CAAb,EAAuC;AAC5Cc,QAAAA,gBAAgB,GAAG,KAAKjK,OAAL,CAAamJ,SAAhC,CAD4C;;AAI5C,YAAI,OAAO,KAAKnJ,OAAL,CAAamJ,SAAb,CAAuBjC,MAA9B,KAAyC,WAA7C,EAA0D;AACxD+C,UAAAA,gBAAgB,GAAG,KAAKjK,OAAL,CAAamJ,SAAb,CAAuB,CAAvB,CAAnB;AACD;AACF;;AAED,WAAKG,OAAL,GAAeU,YAAA,CAAoBC,gBAApB,EAAsC,KAAKV,KAA3C,EAAkD,KAAKW,gBAAL,EAAlD,CAAf;AACD,KApCI;AAuCL;AACA;AACA;;;AACA,QAAI,kBAAkB1d,QAAQ,CAAC0D,eAA3B,IACF,CAACwU,MAAM,CAACjM,OAAP,CAAegQ,mBAAf,CADH,EACwC;AAAA;;AACtC,kBAAGhN,MAAH,aAAajP,QAAQ,CAACoE,IAAT,CAAciL,QAA3B,EACG5M,OADH,CACW,UAAAyW,IAAI;AAAA,eAAIrS,YAAY,CAACmC,EAAb,CAAgBkQ,IAAhB,EAAsB,WAAtB,EAAmC,IAAnC,EAAyCnV,IAAI,EAA7C,CAAJ;AAAA,OADf;AAED;;AAED,SAAK4G,QAAL,CAAcgT,KAAd;;AACA,SAAKhT,QAAL,CAAcuC,YAAd,CAA2B,eAA3B,EAA4C,IAA5C;;AAEA,SAAK6P,KAAL,CAAW7Q,SAAX,CAAqBe,MAArB,CAA4BsL,iBAA5B;;AACA,SAAK5N,QAAL,CAAcuB,SAAd,CAAwBe,MAAxB,CAA+BsL,iBAA/B;;AACA1R,IAAAA,YAAY,CAAC2C,OAAb,CAAqB0O,MAArB,EAA6BE,aAA7B,EAA0C3B,aAA1C;AACD;;SAEDgD,OAAA,gBAAO;AACL,QAAI,KAAK9O,QAAL,CAAcwS,QAAd,IAA0B,KAAKxS,QAAL,CAAcuB,SAAd,CAAwBE,QAAxB,CAAiCsP,mBAAjC,CAA1B,IAAmF,CAAC,KAAKqB,KAAL,CAAW7Q,SAAX,CAAqBE,QAArB,CAA8BmM,iBAA9B,CAAxF,EAAwI;AACtI;AACD;;AAED,QAAML,MAAM,GAAG2E,QAAQ,CAACS,oBAAT,CAA8B,KAAK3S,QAAnC,CAAf;AACA,QAAM8L,aAAa,GAAG;AACpBA,MAAAA,aAAa,EAAE,KAAK9L;AADA,KAAtB;AAIA,QAAMiT,SAAS,GAAG/W,YAAY,CAAC2C,OAAb,CAAqB0O,MAArB,EAA6BG,YAA7B,EAAyC5B,aAAzC,CAAlB;;AAEA,QAAImH,SAAS,CAAC9T,gBAAd,EAAgC;AAC9B;AACD;;AAED,QAAI,KAAKgT,OAAT,EAAkB;AAChB,WAAKA,OAAL,CAAae,OAAb;AACD;;AAED,SAAKd,KAAL,CAAW7Q,SAAX,CAAqBe,MAArB,CAA4BsL,iBAA5B;;AACA,SAAK5N,QAAL,CAAcuB,SAAd,CAAwBe,MAAxB,CAA+BsL,iBAA/B;;AACA1R,IAAAA,YAAY,CAAC2C,OAAb,CAAqB0O,MAArB,EAA6BI,cAA7B,EAA2C7B,aAA3C;AACD;;SAED3L,UAAA,mBAAU;AACR,6BAAMA,OAAN;;AACAjE,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK6D,QAAtB,EAAgCM,WAAhC;AACA,SAAK8R,KAAL,GAAa,IAAb;;AAEA,QAAI,KAAKD,OAAT,EAAkB;AAChB,WAAKA,OAAL,CAAae,OAAb;;AACA,WAAKf,OAAL,GAAe,IAAf;AACD;AACF;;SAEDgB,SAAA,kBAAS;AACP,SAAKb,SAAL,GAAiB,KAAKC,aAAL,EAAjB;;AACA,QAAI,KAAKJ,OAAT,EAAkB;AAChB,WAAKA,OAAL,CAAagB,MAAb;AACD;AACF;;;SAID9J,qBAAA,8BAAqB;AAAA;;AACnBnN,IAAAA,YAAY,CAACmC,EAAb,CAAgB,KAAK2B,QAArB,EAA+B4Q,WAA/B,EAA4C,UAAA7U,KAAK,EAAI;AACnDA,MAAAA,KAAK,CAAC8D,cAAN;AACA9D,MAAAA,KAAK,CAACqX,eAAN;;AACA,MAAA,MAAI,CAAC9Q,MAAL;AACD,KAJD;AAKD;;SAEDwG,aAAA,oBAAWpR,MAAX,EAAmB;AACjBA,IAAAA,MAAM,gBACD,KAAKuI,WAAL,CAAiByF,OADhB,EAED7C,WAAW,CAACI,iBAAZ,CAA8B,KAAKjD,QAAnC,CAFC,EAGDtI,MAHC,CAAN;AAMAF,IAAAA,eAAe,CAAC6I,MAAD,EAAO3I,MAAP,EAAe,KAAKuI,WAAL,CAAiBgG,WAAhC,CAAf;AAEA,WAAOvO,MAAP;AACD;;SAED2a,kBAAA,2BAAkB;AAChB,WAAOlO,cAAc,CAACiB,IAAf,CAAoB,KAAKpF,QAAzB,EAAmCqR,aAAnC,EAAkD,CAAlD,CAAP;AACD;;SAEDgC,gBAAA,yBAAgB;AACd,QAAMC,cAAc,GAAG,KAAKtT,QAAL,CAAcvH,UAArC;;AAEA,QAAI6a,cAAc,CAAC/R,SAAf,CAAyBE,QAAzB,CAAkCwP,kBAAlC,CAAJ,EAA2D;AACzD,aAAOW,eAAP;AACD;;AAED,QAAI0B,cAAc,CAAC/R,SAAf,CAAyBE,QAAzB,CAAkCyP,oBAAlC,CAAJ,EAA6D;AAC3D,aAAOW,cAAP;AACD,KATa;;;AAYd,QAAM0B,KAAK,GAAGrd,gBAAgB,CAAC,KAAKkc,KAAN,CAAhB,CAA6BoB,gBAA7B,CAA8C,eAA9C,EAA+D5d,IAA/D,OAA0E,KAAxF;;AAEA,QAAI0d,cAAc,CAAC/R,SAAf,CAAyBE,QAAzB,CAAkCuP,iBAAlC,CAAJ,EAA0D;AACxD,aAAOuC,KAAK,GAAG9B,gBAAH,GAAsBD,aAAlC;AACD;;AAED,WAAO+B,KAAK,GAAG5B,mBAAH,GAAyBD,gBAArC;AACD;;SAEDa,gBAAA,yBAAgB;AACd,WAAO,KAAKvS,QAAL,CAAcsB,OAAd,OAA0B6P,iBAA1B,MAAmD,IAA1D;AACD;;SAED4B,mBAAA,4BAAmB;AACjB,QAAMd,YAAY,GAAG;AACnBwB,MAAAA,SAAS,EAAE,KAAKJ,aAAL,EADQ;AAEnBK,MAAAA,SAAS,EAAE,CAAC;AACVC,QAAAA,IAAI,EAAE,iBADI;AAEVC,QAAAA,OAAO,EAAE;AACPC,UAAAA,WAAW,EAAE,KAAKhL,OAAL,CAAaiJ,IADnB;AAEPgC,UAAAA,YAAY,EAAE,KAAKjL,OAAL,CAAakJ;AAFpB;AAFC,OAAD;AAFQ,KAArB,CADiB;;AAajB,QAAI,KAAKlJ,OAAL,CAAajQ,OAAb,KAAyB,QAA7B,EAAuC;AACrCqZ,MAAAA,YAAY,CAACyB,SAAb,GAAyB,CAAC;AACxBC,QAAAA,IAAI,EAAE,aADkB;AAExBI,QAAAA,OAAO,EAAE;AAFe,OAAD,CAAzB;AAID;;AAED,wBACK9B,YADL,EAEK,KAAKpJ,OAAL,CAAaoJ,YAFlB;AAID;;;WAIM+B,oBAAP,2BAAyBxe,OAAzB,EAAkCkC,MAAlC,EAA0C;AACxC,QAAI2C,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAarF,OAAb,EAAsB0K,UAAtB,CAAX;;AACA,QAAM2I,OAAO,GAAG,OAAOnR,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,IAAtD;;AAEA,QAAI,CAAC2C,IAAL,EAAW;AACTA,MAAAA,IAAI,GAAG,IAAI6X,QAAJ,CAAa1c,OAAb,EAAsBqT,OAAtB,CAAP;AACD;;AAED,QAAI,OAAOnR,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,UAAI,OAAO2C,IAAI,CAAC3C,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,cAAM,IAAIwV,SAAJ,wBAAkCxV,MAAlC,QAAN;AACD;;AAED2C,MAAAA,IAAI,CAAC3C,MAAD,CAAJ;AACD;AACF;;WAEMkK,kBAAP,yBAAuBlK,MAAvB,EAA+B;AAC7B,WAAO,KAAKmK,IAAL,CAAU,YAAY;AAC3BqQ,MAAAA,QAAQ,CAAC8B,iBAAT,CAA2B,IAA3B,EAAiCtc,MAAjC;AACD,KAFM,CAAP;AAGD;;WAEMgb,aAAP,oBAAkB3W,KAAlB,EAAyB;AACvB,QAAIA,KAAK,KAAKA,KAAK,CAACyG,MAAN,KAAiBkO,kBAAjB,IAAwC3U,KAAK,CAACK,IAAN,KAAe,OAAf,IAA0BL,KAAK,CAAC3B,GAAN,KAAcmW,OAArF,CAAT,EAAyG;AACvG;AACD;;AAED,QAAM0D,OAAO,GAAG9P,cAAc,CAACE,IAAf,CAAoBjC,sBAApB,CAAhB;;AAEA,SAAK,IAAI1F,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAGiX,OAAO,CAACtX,MAA9B,EAAsCD,CAAC,GAAGM,GAA1C,EAA+CN,CAAC,EAAhD,EAAoD;AAClD,UAAM6Q,MAAM,GAAG2E,QAAQ,CAACS,oBAAT,CAA8BsB,OAAO,CAACvX,CAAD,CAArC,CAAf;AACA,UAAMwX,OAAO,GAAGxZ,IAAI,CAACG,OAAL,CAAaoZ,OAAO,CAACvX,CAAD,CAApB,EAAyBwD,UAAzB,CAAhB;AACA,UAAM4L,aAAa,GAAG;AACpBA,QAAAA,aAAa,EAAEmI,OAAO,CAACvX,CAAD;AADF,OAAtB;;AAIA,UAAIX,KAAK,IAAIA,KAAK,CAACK,IAAN,KAAe,OAA5B,EAAqC;AACnC0P,QAAAA,aAAa,CAACqI,UAAd,GAA2BpY,KAA3B;AACD;;AAED,UAAI,CAACmY,OAAL,EAAc;AACZ;AACD;;AAED,UAAME,YAAY,GAAGF,OAAO,CAAC9B,KAA7B;;AACA,UAAI,CAAC6B,OAAO,CAACvX,CAAD,CAAP,CAAW6E,SAAX,CAAqBE,QAArB,CAA8BmM,iBAA9B,CAAL,EAAqD;AACnD;AACD;;AAED,UAAI7R,KAAK,KAAMA,KAAK,CAACK,IAAN,KAAe,OAAf,IACX,kBAAkBhE,IAAlB,CAAuB2D,KAAK,CAACU,MAAN,CAAa0O,OAApC,CADU,IAETpP,KAAK,CAACK,IAAN,KAAe,OAAf,IAA0BL,KAAK,CAAC3B,GAAN,KAAcmW,OAFpC,CAAL,IAGA6D,YAAY,CAAC3S,QAAb,CAAsB1F,KAAK,CAACU,MAA5B,CAHJ,EAGyC;AACvC;AACD;;AAED,UAAMwW,SAAS,GAAG/W,YAAY,CAAC2C,OAAb,CAAqB0O,MAArB,EAA6BG,YAA7B,EAAyC5B,aAAzC,CAAlB;;AACA,UAAImH,SAAS,CAAC9T,gBAAd,EAAgC;AAC9B;AACD,OA9BiD;AAiClD;;;AACA,UAAI,kBAAkB9J,QAAQ,CAAC0D,eAA/B,EAAgD;AAAA;;AAC9C,qBAAGuL,MAAH,cAAajP,QAAQ,CAACoE,IAAT,CAAciL,QAA3B,EACG5M,OADH,CACW,UAAAyW,IAAI;AAAA,iBAAIrS,YAAY,CAACC,GAAb,CAAiBoS,IAAjB,EAAuB,WAAvB,EAAoC,IAApC,EAA0CnV,IAAI,EAA9C,CAAJ;AAAA,SADf;AAED;;AAED6a,MAAAA,OAAO,CAACvX,CAAD,CAAP,CAAW6F,YAAX,CAAwB,eAAxB,EAAyC,OAAzC;;AAEA,UAAI2R,OAAO,CAAC/B,OAAZ,EAAqB;AACnB+B,QAAAA,OAAO,CAAC/B,OAAR,CAAgBe,OAAhB;AACD;;AAEDkB,MAAAA,YAAY,CAAC7S,SAAb,CAAuBC,MAAvB,CAA8BoM,iBAA9B;AACAqG,MAAAA,OAAO,CAACvX,CAAD,CAAP,CAAW6E,SAAX,CAAqBC,MAArB,CAA4BoM,iBAA5B;AACA1R,MAAAA,YAAY,CAAC2C,OAAb,CAAqB0O,MAArB,EAA6BI,cAA7B,EAA2C7B,aAA3C;AACD;AACF;;WAEM6G,uBAAP,8BAA4Bnd,OAA5B,EAAqC;AACnC,WAAOO,sBAAsB,CAACP,OAAD,CAAtB,IAAmCA,OAAO,CAACiD,UAAlD;AACD;;WAEM4b,wBAAP,+BAA6BtY,KAA7B,EAAoC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAI,kBAAkB3D,IAAlB,CAAuB2D,KAAK,CAACU,MAAN,CAAa0O,OAApC,IACFpP,KAAK,CAAC3B,GAAN,KAAckW,SAAd,IAA4BvU,KAAK,CAAC3B,GAAN,KAAciW,UAAd,KAC1BtU,KAAK,CAAC3B,GAAN,KAAcqW,cAAd,IAAgC1U,KAAK,CAAC3B,GAAN,KAAcoW,YAA/C,IACCzU,KAAK,CAACU,MAAN,CAAa6E,OAAb,CAAqB+P,aAArB,CAF0B,CAD1B,GAIF,CAACV,cAAc,CAACvY,IAAf,CAAoB2D,KAAK,CAAC3B,GAA1B,CAJH,EAImC;AACjC;AACD;;AAED2B,IAAAA,KAAK,CAAC8D,cAAN;AACA9D,IAAAA,KAAK,CAACqX,eAAN;;AAEA,QAAI,KAAKZ,QAAL,IAAiB,KAAKjR,SAAL,CAAeE,QAAf,CAAwBsP,mBAAxB,CAArB,EAAmE;AACjE;AACD;;AAED,QAAMxD,MAAM,GAAG2E,QAAQ,CAACS,oBAAT,CAA8B,IAA9B,CAAf;AACA,QAAMF,QAAQ,GAAG,KAAKlR,SAAL,CAAeE,QAAf,CAAwBmM,iBAAxB,CAAjB;;AAEA,QAAI7R,KAAK,CAAC3B,GAAN,KAAciW,UAAlB,EAA8B;AAC5B,UAAM7N,MAAM,GAAG,KAAK4B,OAAL,CAAahC,sBAAb,IAAqC,IAArC,GAA4C+B,cAAc,CAACc,IAAf,CAAoB,IAApB,EAA0B7C,sBAA1B,EAAgD,CAAhD,CAA3D;AACAI,MAAAA,MAAM,CAACwQ,KAAP;AACAd,MAAAA,QAAQ,CAACQ,UAAT;AACA;AACD;;AAED,QAAI,CAACD,QAAD,IAAa1W,KAAK,CAAC3B,GAAN,KAAckW,SAA/B,EAA0C;AACxC4B,MAAAA,QAAQ,CAACQ,UAAT;AACA;AACD;;AAED,QAAM4B,KAAK,GAAGnQ,cAAc,CAACE,IAAf,CAAoBkN,sBAApB,EAA4ChE,MAA5C,EAAoDnK,MAApD,CAA2D7K,SAA3D,CAAd;;AAEA,QAAI,CAAC+b,KAAK,CAAC3X,MAAX,EAAmB;AACjB;AACD;;AAED,QAAIqN,KAAK,GAAGsK,KAAK,CAAClJ,OAAN,CAAcrP,KAAK,CAACU,MAApB,CAAZ,CA5CkC;;AA+ClC,QAAIV,KAAK,CAAC3B,GAAN,KAAcoW,YAAd,IAA8BxG,KAAK,GAAG,CAA1C,EAA6C;AAC3CA,MAAAA,KAAK;AACN,KAjDiC;;;AAoDlC,QAAIjO,KAAK,CAAC3B,GAAN,KAAcqW,cAAd,IAAgCzG,KAAK,GAAGsK,KAAK,CAAC3X,MAAN,GAAe,CAA3D,EAA8D;AAC5DqN,MAAAA,KAAK;AACN,KAtDiC;;;AAyDlCA,IAAAA,KAAK,GAAGA,KAAK,KAAK,CAAC,CAAX,GAAe,CAAf,GAAmBA,KAA3B;AAEAsK,IAAAA,KAAK,CAACtK,KAAD,CAAL,CAAagJ,KAAb;AACD;;;;wBAjWoB;AACnB,aAAOtN,SAAP;AACD;;;wBAEwB;AACvB,aAAOO,aAAP;AACD;;;wBAEqB;AACpB,aAAO/F,UAAP;AACD;;;;EAxBoBH;AAkXvB;AACA;AACA;AACA;AACA;;;AAEA7D,YAAY,CAACmC,EAAb,CAAgBhJ,QAAhB,EAA0Bwb,sBAA1B,EAAkDzO,sBAAlD,EAAwE8P,QAAQ,CAACmC,qBAAjF;AACAnY,YAAY,CAACmC,EAAb,CAAgBhJ,QAAhB,EAA0Bwb,sBAA1B,EAAkDQ,aAAlD,EAAiEa,QAAQ,CAACmC,qBAA1E;AACAnY,YAAY,CAACmC,EAAb,CAAgBhJ,QAAhB,EAA0BsL,sBAA1B,EAAgDuR,QAAQ,CAACQ,UAAzD;AACAxW,YAAY,CAACmC,EAAb,CAAgBhJ,QAAhB,EAA0Byb,oBAA1B,EAAgDoB,QAAQ,CAACQ,UAAzD;AACAxW,YAAY,CAACmC,EAAb,CAAgBhJ,QAAhB,EAA0BsL,sBAA1B,EAAgDyB,sBAAhD,EAAsE,UAAUrG,KAAV,EAAiB;AACrFA,EAAAA,KAAK,CAAC8D,cAAN;AACA9D,EAAAA,KAAK,CAACqX,eAAN;AACAlB,EAAAA,QAAQ,CAAC8B,iBAAT,CAA2B,IAA3B,EAAiC,QAAjC;AACD,CAJD;AAKA9X,YAAY,CAACmC,EAAb,CAAgBhJ,QAAhB,EAA0BsL,sBAA1B,EAAgDyQ,mBAAhD,EAAqE,UAAAnG,CAAC;AAAA,SAAIA,CAAC,CAACmI,eAAF,EAAJ;AAAA,CAAtE;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEAzZ,kBAAkB,CAAC,YAAM;AACvB,MAAMoF,CAAC,GAAGxF,SAAS,EAAnB;AACA;;AACA,MAAIwF,CAAJ,EAAO;AACL,QAAMiD,kBAAkB,GAAGjD,CAAC,CAAClD,EAAF,CAAKwE,MAAL,CAA3B;AACAtB,IAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,IAAa6R,QAAQ,CAACtQ,eAAtB;AACA7C,IAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,EAAW4B,WAAX,GAAyBiQ,QAAzB;;AACAnT,IAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,EAAW6B,UAAX,GAAwB,YAAM;AAC5BnD,MAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,IAAa2B,kBAAb;AACA,aAAOkQ,QAAQ,CAACtQ,eAAhB;AACD,KAHD;AAID;AACF,CAZiB,CAAlB;;ACndA;AACA;AACA;AACA;AACA;;AAEA,IAAMvB,MAAI,GAAG,OAAb;AACA,IAAMH,UAAQ,GAAG,UAAjB;AACA,IAAMI,WAAS,SAAOJ,UAAtB;AACA,IAAMK,cAAY,GAAG,WAArB;AACA,IAAM8P,YAAU,GAAG,QAAnB;AAEA,IAAM3K,SAAO,GAAG;AACd6O,EAAAA,QAAQ,EAAE,IADI;AAEd3O,EAAAA,QAAQ,EAAE,IAFI;AAGdoN,EAAAA,KAAK,EAAE;AAHO,CAAhB;AAMA,IAAM/M,aAAW,GAAG;AAClBsO,EAAAA,QAAQ,EAAE,kBADQ;AAElB3O,EAAAA,QAAQ,EAAE,SAFQ;AAGlBoN,EAAAA,KAAK,EAAE;AAHW,CAApB;AAMA,IAAMtF,YAAU,YAAUpN,WAA1B;AACA,IAAMkU,oBAAoB,qBAAmBlU,WAA7C;AACA,IAAMqN,cAAY,cAAYrN,WAA9B;AACA,IAAMkN,YAAU,YAAUlN,WAA1B;AACA,IAAMmN,aAAW,aAAWnN,WAA5B;AACA,IAAMmU,aAAa,eAAanU,WAAhC;AACA,IAAMoU,YAAY,cAAYpU,WAA9B;AACA,IAAMqU,mBAAmB,qBAAmBrU,WAA5C;AACA,IAAMsU,qBAAqB,uBAAqBtU,WAAhD;AACA,IAAMuU,qBAAqB,uBAAqBvU,WAAhD;AACA,IAAMwU,uBAAuB,yBAAuBxU,WAApD;AACA,IAAMK,sBAAoB,aAAWL,WAAX,GAAuBC,cAAjD;AAEA,IAAMwU,6BAA6B,GAAG,yBAAtC;AACA,IAAMC,mBAAmB,GAAG,gBAA5B;AACA,IAAMC,eAAe,GAAG,YAAxB;AACA,IAAMC,eAAe,GAAG,MAAxB;AACA,IAAMtH,iBAAe,GAAG,MAAxB;AACA,IAAMuH,iBAAiB,GAAG,cAA1B;AAEA,IAAMC,eAAe,GAAG,eAAxB;AACA,IAAMC,mBAAmB,GAAG,aAA5B;AACA,IAAMjT,sBAAoB,GAAG,0BAA7B;AACA,IAAMkT,qBAAqB,GAAG,2BAA9B;AACA,IAAMC,sBAAsB,GAAG,mDAA/B;AACA,IAAMC,uBAAuB,GAAG,aAAhC;AAEA;AACA;AACA;AACA;AACA;;IAEMC;;;AACJ,iBAAYjgB,OAAZ,EAAqBkC,MAArB,EAA6B;AAAA;;AAC3B,sCAAMlC,OAAN;AAEA,UAAKqT,OAAL,GAAe,MAAKC,UAAL,CAAgBpR,MAAhB,CAAf;AACA,UAAKge,OAAL,GAAevR,cAAc,CAACM,OAAf,CAAuB2Q,eAAvB,EAAwC5f,OAAxC,CAAf;AACA,UAAKmgB,SAAL,GAAiB,IAAjB;AACA,UAAKC,QAAL,GAAgB,KAAhB;AACA,UAAKC,kBAAL,GAA0B,KAA1B;AACA,UAAKC,oBAAL,GAA4B,KAA5B;AACA,UAAK1H,gBAAL,GAAwB,KAAxB;AACA,UAAK2H,eAAL,GAAuB,CAAvB;AAV2B;AAW5B;;;;;AAYD;SAEAzT,SAAA,gBAAOwJ,aAAP,EAAsB;AACpB,WAAO,KAAK8J,QAAL,GAAgB,KAAK9G,IAAL,EAAhB,GAA8B,KAAKC,IAAL,CAAUjD,aAAV,CAArC;AACD;;SAEDiD,OAAA,cAAKjD,aAAL,EAAoB;AAAA;;AAClB,QAAI,KAAK8J,QAAL,IAAiB,KAAKxH,gBAA1B,EAA4C;AAC1C;AACD;;AAED,QAAI,KAAKpO,QAAL,CAAcuB,SAAd,CAAwBE,QAAxB,CAAiCyT,eAAjC,CAAJ,EAAuD;AACrD,WAAK9G,gBAAL,GAAwB,IAAxB;AACD;;AAED,QAAMwE,SAAS,GAAG1W,YAAY,CAAC2C,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCwN,YAApC,EAAgD;AAChE1B,MAAAA,aAAa,EAAbA;AADgE,KAAhD,CAAlB;;AAIA,QAAI,KAAK8J,QAAL,IAAiBhD,SAAS,CAACzT,gBAA/B,EAAiD;AAC/C;AACD;;AAED,SAAKyW,QAAL,GAAgB,IAAhB;;AAEA,SAAKI,eAAL;;AACA,SAAKC,aAAL;;AAEA,SAAKC,aAAL;;AAEA,SAAKC,eAAL;;AACA,SAAKC,eAAL;;AAEAla,IAAAA,YAAY,CAACmC,EAAb,CAAgB,KAAK2B,QAArB,EAA+B2U,mBAA/B,EAAoDW,qBAApD,EAA2E,UAAAvZ,KAAK;AAAA,aAAI,MAAI,CAAC+S,IAAL,CAAU/S,KAAV,CAAJ;AAAA,KAAhF;AAEAG,IAAAA,YAAY,CAACmC,EAAb,CAAgB,KAAKqX,OAArB,EAA8BZ,uBAA9B,EAAuD,YAAM;AAC3D5Y,MAAAA,YAAY,CAACoC,GAAb,CAAiB,MAAI,CAAC0B,QAAtB,EAAgC6U,qBAAhC,EAAuD,UAAA9Y,KAAK,EAAI;AAC9D,YAAIA,KAAK,CAACU,MAAN,KAAiB,MAAI,CAACuD,QAA1B,EAAoC;AAClC,UAAA,MAAI,CAAC8V,oBAAL,GAA4B,IAA5B;AACD;AACF,OAJD;AAKD,KAND;;AAQA,SAAKO,aAAL,CAAmB;AAAA,aAAM,MAAI,CAACC,YAAL,CAAkBxK,aAAlB,CAAN;AAAA,KAAnB;AACD;;SAEDgD,OAAA,cAAK/S,KAAL,EAAY;AAAA;;AACV,QAAIA,KAAJ,EAAW;AACTA,MAAAA,KAAK,CAAC8D,cAAN;AACD;;AAED,QAAI,CAAC,KAAK+V,QAAN,IAAkB,KAAKxH,gBAA3B,EAA6C;AAC3C;AACD;;AAED,QAAM6E,SAAS,GAAG/W,YAAY,CAAC2C,OAAb,CAAqB,KAAKmB,QAA1B,EAAoC0N,YAApC,CAAlB;;AAEA,QAAIuF,SAAS,CAAC9T,gBAAd,EAAgC;AAC9B;AACD;;AAED,SAAKyW,QAAL,GAAgB,KAAhB;;AACA,QAAMW,UAAU,GAAG,KAAKvW,QAAL,CAAcuB,SAAd,CAAwBE,QAAxB,CAAiCyT,eAAjC,CAAnB;;AAEA,QAAIqB,UAAJ,EAAgB;AACd,WAAKnI,gBAAL,GAAwB,IAAxB;AACD;;AAED,SAAK+H,eAAL;;AACA,SAAKC,eAAL;;AAEAla,IAAAA,YAAY,CAACC,GAAb,CAAiB9G,QAAjB,EAA2Bof,aAA3B;;AAEA,SAAKzU,QAAL,CAAcuB,SAAd,CAAwBC,MAAxB,CAA+BoM,iBAA/B;;AAEA1R,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK6D,QAAtB,EAAgC2U,mBAAhC;AACAzY,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKuZ,OAAtB,EAA+BZ,uBAA/B;;AAEA,QAAIyB,UAAJ,EAAgB;AACd,UAAMpgB,kBAAkB,GAAGH,gCAAgC,CAAC,KAAKgK,QAAN,CAA3D;AAEA9D,MAAAA,YAAY,CAACoC,GAAb,CAAiB,KAAK0B,QAAtB,EAAgCxL,cAAhC,EAAgD,UAAAuH,KAAK;AAAA,eAAI,MAAI,CAACya,UAAL,CAAgBza,KAAhB,CAAJ;AAAA,OAArD;AACAhF,MAAAA,oBAAoB,CAAC,KAAKiJ,QAAN,EAAgB7J,kBAAhB,CAApB;AACD,KALD,MAKO;AACL,WAAKqgB,UAAL;AACD;AACF;;SAEDrW,UAAA,mBAAU;AACR,KAAClK,MAAD,EAAS,KAAK+J,QAAd,EAAwB,KAAK0V,OAA7B,EACG5d,OADH,CACW,UAAA2e,WAAW;AAAA,aAAIva,YAAY,CAACC,GAAb,CAAiBsa,WAAjB,EAA8BnW,WAA9B,CAAJ;AAAA,KADtB;;AAGA,6BAAMH,OAAN;AAEA;AACJ;AACA;AACA;AACA;;;AACIjE,IAAAA,YAAY,CAACC,GAAb,CAAiB9G,QAAjB,EAA2Bof,aAA3B;AAEA,SAAK5L,OAAL,GAAe,IAAf;AACA,SAAK6M,OAAL,GAAe,IAAf;AACA,SAAKC,SAAL,GAAiB,IAAjB;AACA,SAAKC,QAAL,GAAgB,IAAhB;AACA,SAAKC,kBAAL,GAA0B,IAA1B;AACA,SAAKC,oBAAL,GAA4B,IAA5B;AACA,SAAK1H,gBAAL,GAAwB,IAAxB;AACA,SAAK2H,eAAL,GAAuB,IAAvB;AACD;;SAEDW,eAAA,wBAAe;AACb,SAAKR,aAAL;AACD;;;SAIDpN,aAAA,oBAAWpR,MAAX,EAAmB;AACjBA,IAAAA,MAAM,gBACDgO,SADC,EAEDhO,MAFC,CAAN;AAIAF,IAAAA,eAAe,CAAC6I,MAAD,EAAO3I,MAAP,EAAeuO,aAAf,CAAf;AACA,WAAOvO,MAAP;AACD;;SAED4e,eAAA,sBAAaxK,aAAb,EAA4B;AAAA;;AAC1B,QAAMyK,UAAU,GAAG,KAAKvW,QAAL,CAAcuB,SAAd,CAAwBE,QAAxB,CAAiCyT,eAAjC,CAAnB;;AACA,QAAMyB,SAAS,GAAGxS,cAAc,CAACM,OAAf,CAAuB4Q,mBAAvB,EAA4C,KAAKK,OAAjD,CAAlB;;AAEA,QAAI,CAAC,KAAK1V,QAAL,CAAcvH,UAAf,IAA6B,KAAKuH,QAAL,CAAcvH,UAAd,CAAyB3B,QAAzB,KAAsCgO,IAAI,CAACC,YAA5E,EAA0F;AACxF;AACA1P,MAAAA,QAAQ,CAACoE,IAAT,CAAcmd,WAAd,CAA0B,KAAK5W,QAA/B;AACD;;AAED,SAAKA,QAAL,CAAcxH,KAAd,CAAoBI,OAApB,GAA8B,OAA9B;;AACA,SAAKoH,QAAL,CAAcgD,eAAd,CAA8B,aAA9B;;AACA,SAAKhD,QAAL,CAAcuC,YAAd,CAA2B,YAA3B,EAAyC,IAAzC;;AACA,SAAKvC,QAAL,CAAcuC,YAAd,CAA2B,MAA3B,EAAmC,QAAnC;;AACA,SAAKvC,QAAL,CAAc4D,SAAd,GAA0B,CAA1B;;AAEA,QAAI+S,SAAJ,EAAe;AACbA,MAAAA,SAAS,CAAC/S,SAAV,GAAsB,CAAtB;AACD;;AAED,QAAI2S,UAAJ,EAAgB;AACdld,MAAAA,MAAM,CAAC,KAAK2G,QAAN,CAAN;AACD;;AAED,SAAKA,QAAL,CAAcuB,SAAd,CAAwB2J,GAAxB,CAA4B0C,iBAA5B;;AAEA,QAAI,KAAK/E,OAAL,CAAamK,KAAjB,EAAwB;AACtB,WAAK6D,aAAL;AACD;;AAED,QAAMC,kBAAkB,GAAG,SAArBA,kBAAqB,GAAM;AAC/B,UAAI,MAAI,CAACjO,OAAL,CAAamK,KAAjB,EAAwB;AACtB,QAAA,MAAI,CAAChT,QAAL,CAAcgT,KAAd;AACD;;AAED,MAAA,MAAI,CAAC5E,gBAAL,GAAwB,KAAxB;AACAlS,MAAAA,YAAY,CAAC2C,OAAb,CAAqB,MAAI,CAACmB,QAA1B,EAAoCyN,aAApC,EAAiD;AAC/C3B,QAAAA,aAAa,EAAbA;AAD+C,OAAjD;AAGD,KATD;;AAWA,QAAIyK,UAAJ,EAAgB;AACd,UAAMpgB,kBAAkB,GAAGH,gCAAgC,CAAC,KAAK0f,OAAN,CAA3D;AAEAxZ,MAAAA,YAAY,CAACoC,GAAb,CAAiB,KAAKoX,OAAtB,EAA+BlhB,cAA/B,EAA+CsiB,kBAA/C;AACA/f,MAAAA,oBAAoB,CAAC,KAAK2e,OAAN,EAAevf,kBAAf,CAApB;AACD,KALD,MAKO;AACL2gB,MAAAA,kBAAkB;AACnB;AACF;;SAEDD,gBAAA,yBAAgB;AAAA;;AACd3a,IAAAA,YAAY,CAACC,GAAb,CAAiB9G,QAAjB,EAA2Bof,aAA3B,EADc;;AAEdvY,IAAAA,YAAY,CAACmC,EAAb,CAAgBhJ,QAAhB,EAA0Bof,aAA1B,EAAyC,UAAA1Y,KAAK,EAAI;AAChD,UAAI1G,QAAQ,KAAK0G,KAAK,CAACU,MAAnB,IACA,MAAI,CAACuD,QAAL,KAAkBjE,KAAK,CAACU,MADxB,IAEA,CAAC,MAAI,CAACuD,QAAL,CAAcyB,QAAd,CAAuB1F,KAAK,CAACU,MAA7B,CAFL,EAE2C;AACzC,QAAA,MAAI,CAACuD,QAAL,CAAcgT,KAAd;AACD;AACF,KAND;AAOD;;SAEDmD,kBAAA,2BAAkB;AAAA;;AAChB,QAAI,KAAKP,QAAT,EAAmB;AACjB1Z,MAAAA,YAAY,CAACmC,EAAb,CAAgB,KAAK2B,QAArB,EAA+B4U,qBAA/B,EAAsD,UAAA7Y,KAAK,EAAI;AAC7D,YAAI,MAAI,CAAC8M,OAAL,CAAajD,QAAb,IAAyB7J,KAAK,CAAC3B,GAAN,KAAciW,YAA3C,EAAuD;AACrDtU,UAAAA,KAAK,CAAC8D,cAAN;;AACA,UAAA,MAAI,CAACiP,IAAL;AACD,SAHD,MAGO,IAAI,CAAC,MAAI,CAACjG,OAAL,CAAajD,QAAd,IAA0B7J,KAAK,CAAC3B,GAAN,KAAciW,YAA5C,EAAwD;AAC7D,UAAA,MAAI,CAAC0G,0BAAL;AACD;AACF,OAPD;AAQD,KATD,MASO;AACL7a,MAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK6D,QAAtB,EAAgC4U,qBAAhC;AACD;AACF;;SAEDwB,kBAAA,2BAAkB;AAAA;;AAChB,QAAI,KAAKR,QAAT,EAAmB;AACjB1Z,MAAAA,YAAY,CAACmC,EAAb,CAAgBpI,MAAhB,EAAwBye,YAAxB,EAAsC;AAAA,eAAM,MAAI,CAACwB,aAAL,EAAN;AAAA,OAAtC;AACD,KAFD,MAEO;AACLha,MAAAA,YAAY,CAACC,GAAb,CAAiBlG,MAAjB,EAAyBye,YAAzB;AACD;AACF;;SAED8B,aAAA,sBAAa;AAAA;;AACX,SAAKxW,QAAL,CAAcxH,KAAd,CAAoBI,OAApB,GAA8B,MAA9B;;AACA,SAAKoH,QAAL,CAAcuC,YAAd,CAA2B,aAA3B,EAA0C,IAA1C;;AACA,SAAKvC,QAAL,CAAcgD,eAAd,CAA8B,YAA9B;;AACA,SAAKhD,QAAL,CAAcgD,eAAd,CAA8B,MAA9B;;AACA,SAAKoL,gBAAL,GAAwB,KAAxB;;AACA,SAAKiI,aAAL,CAAmB,YAAM;AACvBhhB,MAAAA,QAAQ,CAACoE,IAAT,CAAc8H,SAAd,CAAwBC,MAAxB,CAA+ByT,eAA/B;;AACA,MAAA,MAAI,CAAC+B,iBAAL;;AACA,MAAA,MAAI,CAACC,eAAL;;AACA/a,MAAAA,YAAY,CAAC2C,OAAb,CAAqB,MAAI,CAACmB,QAA1B,EAAoC2N,cAApC;AACD,KALD;AAMD;;SAEDuJ,kBAAA,2BAAkB;AAChB,SAAKvB,SAAL,CAAeld,UAAf,CAA0BkJ,WAA1B,CAAsC,KAAKgU,SAA3C;;AACA,SAAKA,SAAL,GAAiB,IAAjB;AACD;;SAEDU,gBAAA,uBAAczc,QAAd,EAAwB;AAAA;;AACtB,QAAMud,OAAO,GAAG,KAAKnX,QAAL,CAAcuB,SAAd,CAAwBE,QAAxB,CAAiCyT,eAAjC,IACdA,eADc,GAEd,EAFF;;AAIA,QAAI,KAAKU,QAAL,IAAiB,KAAK/M,OAAL,CAAa0L,QAAlC,EAA4C;AAC1C,WAAKoB,SAAL,GAAiBtgB,QAAQ,CAAC+hB,aAAT,CAAuB,KAAvB,CAAjB;AACA,WAAKzB,SAAL,CAAe0B,SAAf,GAA2BrC,mBAA3B;;AAEA,UAAImC,OAAJ,EAAa;AACX,aAAKxB,SAAL,CAAepU,SAAf,CAAyB2J,GAAzB,CAA6BiM,OAA7B;AACD;;AAED9hB,MAAAA,QAAQ,CAACoE,IAAT,CAAcmd,WAAd,CAA0B,KAAKjB,SAA/B;AAEAzZ,MAAAA,YAAY,CAACmC,EAAb,CAAgB,KAAK2B,QAArB,EAA+B2U,mBAA/B,EAAoD,UAAA5Y,KAAK,EAAI;AAC3D,YAAI,MAAI,CAAC+Z,oBAAT,EAA+B;AAC7B,UAAA,MAAI,CAACA,oBAAL,GAA4B,KAA5B;AACA;AACD;;AAED,YAAI/Z,KAAK,CAACU,MAAN,KAAiBV,KAAK,CAACub,aAA3B,EAA0C;AACxC;AACD;;AAED,YAAI,MAAI,CAACzO,OAAL,CAAa0L,QAAb,KAA0B,QAA9B,EAAwC;AACtC,UAAA,MAAI,CAACwC,0BAAL;AACD,SAFD,MAEO;AACL,UAAA,MAAI,CAACjI,IAAL;AACD;AACF,OAfD;;AAiBA,UAAIqI,OAAJ,EAAa;AACX9d,QAAAA,MAAM,CAAC,KAAKsc,SAAN,CAAN;AACD;;AAED,WAAKA,SAAL,CAAepU,SAAf,CAAyB2J,GAAzB,CAA6B0C,iBAA7B;;AAEA,UAAI,CAACuJ,OAAL,EAAc;AACZvd,QAAAA,QAAQ;AACR;AACD;;AAED,UAAM2d,0BAA0B,GAAGvhB,gCAAgC,CAAC,KAAK2f,SAAN,CAAnE;AAEAzZ,MAAAA,YAAY,CAACoC,GAAb,CAAiB,KAAKqX,SAAtB,EAAiCnhB,cAAjC,EAAiDoF,QAAjD;AACA7C,MAAAA,oBAAoB,CAAC,KAAK4e,SAAN,EAAiB4B,0BAAjB,CAApB;AACD,KA1CD,MA0CO,IAAI,CAAC,KAAK3B,QAAN,IAAkB,KAAKD,SAA3B,EAAsC;AAC3C,WAAKA,SAAL,CAAepU,SAAf,CAAyBC,MAAzB,CAAgCoM,iBAAhC;;AAEA,UAAM4J,cAAc,GAAG,SAAjBA,cAAiB,GAAM;AAC3B,QAAA,MAAI,CAACN,eAAL;;AACAtd,QAAAA,QAAQ;AACT,OAHD;;AAKA,UAAI,KAAKoG,QAAL,CAAcuB,SAAd,CAAwBE,QAAxB,CAAiCyT,eAAjC,CAAJ,EAAuD;AACrD,YAAMqC,2BAA0B,GAAGvhB,gCAAgC,CAAC,KAAK2f,SAAN,CAAnE;;AACAzZ,QAAAA,YAAY,CAACoC,GAAb,CAAiB,KAAKqX,SAAtB,EAAiCnhB,cAAjC,EAAiDgjB,cAAjD;AACAzgB,QAAAA,oBAAoB,CAAC,KAAK4e,SAAN,EAAiB4B,2BAAjB,CAApB;AACD,OAJD,MAIO;AACLC,QAAAA,cAAc;AACf;AACF,KAfM,MAeA;AACL5d,MAAAA,QAAQ;AACT;AACF;;SAEDmd,6BAAA,sCAA6B;AAAA;;AAC3B,QAAM9D,SAAS,GAAG/W,YAAY,CAAC2C,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCwU,oBAApC,CAAlB;;AACA,QAAIvB,SAAS,CAAC9T,gBAAd,EAAgC;AAC9B;AACD;;AAED,QAAMsY,kBAAkB,GAAG,KAAKzX,QAAL,CAAc0X,YAAd,GAA6BriB,QAAQ,CAAC0D,eAAT,CAAyB4e,YAAjF;;AAEA,QAAI,CAACF,kBAAL,EAAyB;AACvB,WAAKzX,QAAL,CAAcxH,KAAd,CAAoBof,SAApB,GAAgC,QAAhC;AACD;;AAED,SAAK5X,QAAL,CAAcuB,SAAd,CAAwB2J,GAAxB,CAA4BiK,iBAA5B;;AACA,QAAM0C,uBAAuB,GAAG7hB,gCAAgC,CAAC,KAAK0f,OAAN,CAAhE;AACAxZ,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK6D,QAAtB,EAAgCxL,cAAhC;AACA0H,IAAAA,YAAY,CAACoC,GAAb,CAAiB,KAAK0B,QAAtB,EAAgCxL,cAAhC,EAAgD,YAAM;AACpD,MAAA,OAAI,CAACwL,QAAL,CAAcuB,SAAd,CAAwBC,MAAxB,CAA+B2T,iBAA/B;;AACA,UAAI,CAACsC,kBAAL,EAAyB;AACvBvb,QAAAA,YAAY,CAACoC,GAAb,CAAiB,OAAI,CAAC0B,QAAtB,EAAgCxL,cAAhC,EAAgD,YAAM;AACpD,UAAA,OAAI,CAACwL,QAAL,CAAcxH,KAAd,CAAoBof,SAApB,GAAgC,EAAhC;AACD,SAFD;AAGA7gB,QAAAA,oBAAoB,CAAC,OAAI,CAACiJ,QAAN,EAAgB6X,uBAAhB,CAApB;AACD;AACF,KARD;AASA9gB,IAAAA,oBAAoB,CAAC,KAAKiJ,QAAN,EAAgB6X,uBAAhB,CAApB;;AACA,SAAK7X,QAAL,CAAcgT,KAAd;AACD;AAGD;AACA;;;SAEAkD,gBAAA,yBAAgB;AACd,QAAMuB,kBAAkB,GACtB,KAAKzX,QAAL,CAAc0X,YAAd,GAA6BriB,QAAQ,CAAC0D,eAAT,CAAyB4e,YADxD;;AAGA,QAAK,CAAC,KAAK9B,kBAAN,IAA4B4B,kBAA5B,IAAkD,CAAC3d,KAApD,IAA+D,KAAK+b,kBAAL,IAA2B,CAAC4B,kBAA5B,IAAkD3d,KAArH,EAA6H;AAC3H,WAAKkG,QAAL,CAAcxH,KAAd,CAAoBsf,WAApB,GAAqC,KAAK/B,eAA1C;AACD;;AAED,QAAK,KAAKF,kBAAL,IAA2B,CAAC4B,kBAA5B,IAAkD,CAAC3d,KAApD,IAA+D,CAAC,KAAK+b,kBAAN,IAA4B4B,kBAA5B,IAAkD3d,KAArH,EAA6H;AAC3H,WAAKkG,QAAL,CAAcxH,KAAd,CAAoBuf,YAApB,GAAsC,KAAKhC,eAA3C;AACD;AACF;;SAEDiB,oBAAA,6BAAoB;AAClB,SAAKhX,QAAL,CAAcxH,KAAd,CAAoBsf,WAApB,GAAkC,EAAlC;AACA,SAAK9X,QAAL,CAAcxH,KAAd,CAAoBuf,YAApB,GAAmC,EAAnC;AACD;;SAED/B,kBAAA,2BAAkB;AAChB,QAAMvS,IAAI,GAAGpO,QAAQ,CAACoE,IAAT,CAAciK,qBAAd,EAAb;AACA,SAAKmS,kBAAL,GAA0B3gB,IAAI,CAAC8iB,KAAL,CAAWvU,IAAI,CAACI,IAAL,GAAYJ,IAAI,CAACwU,KAA5B,IAAqChiB,MAAM,CAACiiB,UAAtE;AACA,SAAKnC,eAAL,GAAuB,KAAKoC,kBAAL,EAAvB;AACD;;SAEDlC,gBAAA,yBAAgB;AAAA;;AACd,QAAI,KAAKJ,kBAAT,EAA6B;AAC3B;AACA;AAEA;AACA1R,MAAAA,cAAc,CAACE,IAAf,CAAoBkR,sBAApB,EACGzd,OADH,CACW,UAAAtC,OAAO,EAAI;AAClB,YAAM4iB,aAAa,GAAG5iB,OAAO,CAACgD,KAAR,CAAcuf,YAApC;AACA,YAAMM,iBAAiB,GAAGpiB,MAAM,CAACC,gBAAP,CAAwBV,OAAxB,EAAiC,eAAjC,CAA1B;AACAqN,QAAAA,WAAW,CAACC,gBAAZ,CAA6BtN,OAA7B,EAAsC,eAAtC,EAAuD4iB,aAAvD;AACA5iB,QAAAA,OAAO,CAACgD,KAAR,CAAcuf,YAAd,GAAgCzhB,MAAM,CAACC,UAAP,CAAkB8hB,iBAAlB,IAAuC,OAAI,CAACtC,eAA5E;AACD,OANH,EAL2B;;AAc3B5R,MAAAA,cAAc,CAACE,IAAf,CAAoBmR,uBAApB,EACG1d,OADH,CACW,UAAAtC,OAAO,EAAI;AAClB,YAAM8iB,YAAY,GAAG9iB,OAAO,CAACgD,KAAR,CAAc+f,WAAnC;AACA,YAAMC,gBAAgB,GAAGviB,MAAM,CAACC,gBAAP,CAAwBV,OAAxB,EAAiC,cAAjC,CAAzB;AACAqN,QAAAA,WAAW,CAACC,gBAAZ,CAA6BtN,OAA7B,EAAsC,cAAtC,EAAsD8iB,YAAtD;AACA9iB,QAAAA,OAAO,CAACgD,KAAR,CAAc+f,WAAd,GAA+BjiB,MAAM,CAACC,UAAP,CAAkBiiB,gBAAlB,IAAsC,OAAI,CAACzC,eAA1E;AACD,OANH,EAd2B;;AAuB3B,UAAMqC,aAAa,GAAG/iB,QAAQ,CAACoE,IAAT,CAAcjB,KAAd,CAAoBuf,YAA1C;AACA,UAAMM,iBAAiB,GAAGpiB,MAAM,CAACC,gBAAP,CAAwBb,QAAQ,CAACoE,IAAjC,EAAuC,eAAvC,CAA1B;AAEAoJ,MAAAA,WAAW,CAACC,gBAAZ,CAA6BzN,QAAQ,CAACoE,IAAtC,EAA4C,eAA5C,EAA6D2e,aAA7D;AACA/iB,MAAAA,QAAQ,CAACoE,IAAT,CAAcjB,KAAd,CAAoBuf,YAApB,GAAsCzhB,MAAM,CAACC,UAAP,CAAkB8hB,iBAAlB,IAAuC,KAAKtC,eAAlF;AACD;;AAED1gB,IAAAA,QAAQ,CAACoE,IAAT,CAAc8H,SAAd,CAAwB2J,GAAxB,CAA4B+J,eAA5B;AACD;;SAEDgC,kBAAA,2BAAkB;AAChB;AACA9S,IAAAA,cAAc,CAACE,IAAf,CAAoBkR,sBAApB,EACGzd,OADH,CACW,UAAAtC,OAAO,EAAI;AAClB,UAAMijB,OAAO,GAAG5V,WAAW,CAACU,gBAAZ,CAA6B/N,OAA7B,EAAsC,eAAtC,CAAhB;;AACA,UAAI,OAAOijB,OAAP,KAAmB,WAAvB,EAAoC;AAClC5V,QAAAA,WAAW,CAACE,mBAAZ,CAAgCvN,OAAhC,EAAyC,eAAzC;AACAA,QAAAA,OAAO,CAACgD,KAAR,CAAcuf,YAAd,GAA6BU,OAA7B;AACD;AACF,KAPH,EAFgB;;AAYhBtU,IAAAA,cAAc,CAACE,IAAf,MAAuBmR,uBAAvB,EACG1d,OADH,CACW,UAAAtC,OAAO,EAAI;AAClB,UAAMkjB,MAAM,GAAG7V,WAAW,CAACU,gBAAZ,CAA6B/N,OAA7B,EAAsC,cAAtC,CAAf;;AACA,UAAI,OAAOkjB,MAAP,KAAkB,WAAtB,EAAmC;AACjC7V,QAAAA,WAAW,CAACE,mBAAZ,CAAgCvN,OAAhC,EAAyC,cAAzC;AACAA,QAAAA,OAAO,CAACgD,KAAR,CAAc+f,WAAd,GAA4BG,MAA5B;AACD;AACF,KAPH,EAZgB;;AAsBhB,QAAMD,OAAO,GAAG5V,WAAW,CAACU,gBAAZ,CAA6BlO,QAAQ,CAACoE,IAAtC,EAA4C,eAA5C,CAAhB;;AACA,QAAI,OAAOgf,OAAP,KAAmB,WAAvB,EAAoC;AAClCpjB,MAAAA,QAAQ,CAACoE,IAAT,CAAcjB,KAAd,CAAoBuf,YAApB,GAAmC,EAAnC;AACD,KAFD,MAEO;AACLlV,MAAAA,WAAW,CAACE,mBAAZ,CAAgC1N,QAAQ,CAACoE,IAAzC,EAA+C,eAA/C;AACApE,MAAAA,QAAQ,CAACoE,IAAT,CAAcjB,KAAd,CAAoBuf,YAApB,GAAmCU,OAAnC;AACD;AACF;;SAEDN,qBAAA,8BAAqB;AAAE;AACrB,QAAMQ,SAAS,GAAGtjB,QAAQ,CAAC+hB,aAAT,CAAuB,KAAvB,CAAlB;AACAuB,IAAAA,SAAS,CAACtB,SAAV,GAAsBtC,6BAAtB;AACA1f,IAAAA,QAAQ,CAACoE,IAAT,CAAcmd,WAAd,CAA0B+B,SAA1B;AACA,QAAMC,cAAc,GAAGD,SAAS,CAACjV,qBAAV,GAAkCmV,KAAlC,GAA0CF,SAAS,CAACG,WAA3E;AACAzjB,IAAAA,QAAQ,CAACoE,IAAT,CAAckI,WAAd,CAA0BgX,SAA1B;AACA,WAAOC,cAAP;AACD;;;QAIMhX,kBAAP,yBAAuBlK,MAAvB,EAA+BoU,aAA/B,EAA8C;AAC5C,WAAO,KAAKjK,IAAL,CAAU,YAAY;AAC3B,UAAIxH,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmBqF,UAAnB,CAAX;;AACA,UAAM2I,OAAO,gBACRnD,SADQ,EAER7C,WAAW,CAACI,iBAAZ,CAA8B,IAA9B,CAFQ,EAGP,OAAOvL,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAHzC,CAAb;;AAMA,UAAI,CAAC2C,IAAL,EAAW;AACTA,QAAAA,IAAI,GAAG,IAAIob,KAAJ,CAAU,IAAV,EAAgB5M,OAAhB,CAAP;AACD;;AAED,UAAI,OAAOnR,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,YAAI,OAAO2C,IAAI,CAAC3C,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,gBAAM,IAAIwV,SAAJ,wBAAkCxV,MAAlC,QAAN;AACD;;AAED2C,QAAAA,IAAI,CAAC3C,MAAD,CAAJ,CAAaoU,aAAb;AACD;AACF,KAnBM,CAAP;AAoBD;;;;wBAzcoB;AACnB,aAAOpG,SAAP;AACD;;;wBAEqB;AACpB,aAAOxF,UAAP;AACD;;;;EAtBiBH;AA4dpB;AACA;AACA;AACA;AACA;;;AAEA7D,YAAY,CAACmC,EAAb,CAAgBhJ,QAAhB,EAA0BsL,sBAA1B,EAAgDyB,sBAAhD,EAAsE,UAAUrG,KAAV,EAAiB;AAAA;;AACrF,MAAMU,MAAM,GAAG1G,sBAAsB,CAAC,IAAD,CAArC;;AAEA,MAAI,KAAKoV,OAAL,KAAiB,GAAjB,IAAwB,KAAKA,OAAL,KAAiB,MAA7C,EAAqD;AACnDpP,IAAAA,KAAK,CAAC8D,cAAN;AACD;;AAED3D,EAAAA,YAAY,CAACoC,GAAb,CAAiB7B,MAAjB,EAAyB+Q,YAAzB,EAAqC,UAAAoF,SAAS,EAAI;AAChD,QAAIA,SAAS,CAACzT,gBAAd,EAAgC;AAC9B;AACA;AACD;;AAEDjD,IAAAA,YAAY,CAACoC,GAAb,CAAiB7B,MAAjB,EAAyBkR,cAAzB,EAAuC,YAAM;AAC3C,UAAIpV,SAAS,CAAC,OAAD,CAAb,EAAqB;AACnB,QAAA,OAAI,CAACya,KAAL;AACD;AACF,KAJD;AAKD,GAXD;AAaA,MAAI3Y,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa4B,MAAb,EAAqByD,UAArB,CAAX;;AACA,MAAI,CAAC7F,IAAL,EAAW;AACT,QAAM3C,MAAM,gBACPmL,WAAW,CAACI,iBAAZ,CAA8BxG,MAA9B,CADO,EAEPoG,WAAW,CAACI,iBAAZ,CAA8B,IAA9B,CAFO,CAAZ;;AAKA5I,IAAAA,IAAI,GAAG,IAAIob,KAAJ,CAAUhZ,MAAV,EAAkB/E,MAAlB,CAAP;AACD;;AAED2C,EAAAA,IAAI,CAAC0U,IAAL,CAAU,IAAV;AACD,CA/BD;AAiCA;AACA;AACA;AACA;AACA;AACA;;AAEApV,kBAAkB,CAAC,YAAM;AACvB,MAAMoF,CAAC,GAAGxF,SAAS,EAAnB;AACA;;AACA,MAAIwF,CAAJ,EAAO;AACL,QAAMiD,kBAAkB,GAAGjD,CAAC,CAAClD,EAAF,CAAKwE,MAAL,CAA3B;AACAtB,IAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,IAAaoV,KAAK,CAAC7T,eAAnB;AACA7C,IAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,EAAW4B,WAAX,GAAyBwT,KAAzB;;AACA1W,IAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,EAAW6B,UAAX,GAAwB,YAAM;AAC5BnD,MAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,IAAa2B,kBAAb;AACA,aAAOyT,KAAK,CAAC7T,eAAb;AACD,KAHD;AAID;AACF,CAZiB,CAAlB;;AC5lBA;AACA;AACA;AACA;AACA;AACA;AAEA,IAAMmX,QAAQ,GAAG,IAAIvd,GAAJ,CAAQ,CACvB,YADuB,EAEvB,MAFuB,EAGvB,MAHuB,EAIvB,UAJuB,EAKvB,UALuB,EAMvB,QANuB,EAOvB,KAPuB,EAQvB,YARuB,CAAR,CAAjB;AAWA,IAAMwd,sBAAsB,GAAG,gBAA/B;AAEA;AACA;AACA;AACA;AACA;;AACA,IAAMC,gBAAgB,GAAG,6DAAzB;AAEA;AACA;AACA;AACA;AACA;;AACA,IAAMC,gBAAgB,GAAG,oIAAzB;;AAEA,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAmB,CAACC,IAAD,EAAOC,oBAAP,EAAgC;AACvD,MAAMC,QAAQ,GAAGF,IAAI,CAACG,QAAL,CAAcxkB,WAAd,EAAjB;;AAEA,MAAIskB,oBAAoB,CAACjb,QAArB,CAA8Bkb,QAA9B,CAAJ,EAA6C;AAC3C,QAAIP,QAAQ,CAACrb,GAAT,CAAa4b,QAAb,CAAJ,EAA4B;AAC1B,aAAOvb,OAAO,CAACqb,IAAI,CAACI,SAAL,CAAe1kB,KAAf,CAAqBmkB,gBAArB,KAA0CG,IAAI,CAACI,SAAL,CAAe1kB,KAAf,CAAqBokB,gBAArB,CAA3C,CAAd;AACD;;AAED,WAAO,IAAP;AACD;;AAED,MAAMO,MAAM,GAAGJ,oBAAoB,CAACjW,MAArB,CAA4B,UAAAsW,SAAS;AAAA,WAAIA,SAAS,YAAYvhB,MAAzB;AAAA,GAArC,CAAf,CAXuD;;AAcvD,OAAK,IAAIuE,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAGyc,MAAM,CAAC9c,MAA7B,EAAqCD,CAAC,GAAGM,GAAzC,EAA8CN,CAAC,EAA/C,EAAmD;AACjD,QAAI4c,QAAQ,CAACxkB,KAAT,CAAe2kB,MAAM,CAAC/c,CAAD,CAArB,CAAJ,EAA+B;AAC7B,aAAO,IAAP;AACD;AACF;;AAED,SAAO,KAAP;AACD,CArBD;;AAuBO,IAAMid,gBAAgB,GAAG;AAC9B;AACA,OAAK,CAAC,OAAD,EAAU,KAAV,EAAiB,IAAjB,EAAuB,MAAvB,EAA+B,MAA/B,EAAuCX,sBAAvC,CAFyB;AAG9BY,EAAAA,CAAC,EAAE,CAAC,QAAD,EAAW,MAAX,EAAmB,OAAnB,EAA4B,KAA5B,CAH2B;AAI9BC,EAAAA,IAAI,EAAE,EAJwB;AAK9BC,EAAAA,CAAC,EAAE,EAL2B;AAM9BC,EAAAA,EAAE,EAAE,EAN0B;AAO9BC,EAAAA,GAAG,EAAE,EAPyB;AAQ9BC,EAAAA,IAAI,EAAE,EARwB;AAS9BC,EAAAA,GAAG,EAAE,EATyB;AAU9BC,EAAAA,EAAE,EAAE,EAV0B;AAW9BC,EAAAA,EAAE,EAAE,EAX0B;AAY9BC,EAAAA,EAAE,EAAE,EAZ0B;AAa9BC,EAAAA,EAAE,EAAE,EAb0B;AAc9BC,EAAAA,EAAE,EAAE,EAd0B;AAe9BC,EAAAA,EAAE,EAAE,EAf0B;AAgB9BC,EAAAA,EAAE,EAAE,EAhB0B;AAiB9BC,EAAAA,EAAE,EAAE,EAjB0B;AAkB9Bhe,EAAAA,CAAC,EAAE,EAlB2B;AAmB9Bie,EAAAA,GAAG,EAAE,CAAC,KAAD,EAAQ,QAAR,EAAkB,KAAlB,EAAyB,OAAzB,EAAkC,OAAlC,EAA2C,QAA3C,CAnByB;AAoB9BC,EAAAA,EAAE,EAAE,EApB0B;AAqB9BC,EAAAA,EAAE,EAAE,EArB0B;AAsB9BC,EAAAA,CAAC,EAAE,EAtB2B;AAuB9BC,EAAAA,GAAG,EAAE,EAvByB;AAwB9BC,EAAAA,CAAC,EAAE,EAxB2B;AAyB9BC,EAAAA,KAAK,EAAE,EAzBuB;AA0B9BC,EAAAA,IAAI,EAAE,EA1BwB;AA2B9BC,EAAAA,GAAG,EAAE,EA3ByB;AA4B9BC,EAAAA,GAAG,EAAE,EA5ByB;AA6B9BC,EAAAA,MAAM,EAAE,EA7BsB;AA8B9BC,EAAAA,CAAC,EAAE,EA9B2B;AA+B9BC,EAAAA,EAAE,EAAE;AA/B0B,CAAzB;AAkCA,SAASC,YAAT,CAAsBC,UAAtB,EAAkCC,SAAlC,EAA6CC,UAA7C,EAAyD;AAAA;;AAC9D,MAAI,CAACF,UAAU,CAAC9e,MAAhB,EAAwB;AACtB,WAAO8e,UAAP;AACD;;AAED,MAAIE,UAAU,IAAI,OAAOA,UAAP,KAAsB,UAAxC,EAAoD;AAClD,WAAOA,UAAU,CAACF,UAAD,CAAjB;AACD;;AAED,MAAMG,SAAS,GAAG,IAAI3lB,MAAM,CAAC4lB,SAAX,EAAlB;AACA,MAAMC,eAAe,GAAGF,SAAS,CAACG,eAAV,CAA0BN,UAA1B,EAAsC,WAAtC,CAAxB;AACA,MAAMO,aAAa,GAAGpkB,MAAM,CAACC,IAAP,CAAY6jB,SAAZ,CAAtB;;AACA,MAAMO,QAAQ,GAAG,YAAG3X,MAAH,aAAawX,eAAe,CAACriB,IAAhB,CAAqB+C,gBAArB,CAAsC,GAAtC,CAAb,CAAjB;;AAZ8D,6BAcrDE,CAdqD,EAc9CM,GAd8C;AAAA;;AAe5D,QAAMkf,EAAE,GAAGD,QAAQ,CAACvf,CAAD,CAAnB;AACA,QAAMyf,MAAM,GAAGD,EAAE,CAAC3C,QAAH,CAAYxkB,WAAZ,EAAf;;AAEA,QAAI,CAACinB,aAAa,CAAC5d,QAAd,CAAuB+d,MAAvB,CAAL,EAAqC;AACnCD,MAAAA,EAAE,CAACzjB,UAAH,CAAckJ,WAAd,CAA0Bua,EAA1B;AAEA;AACD;;AAED,QAAME,aAAa,GAAG,aAAG9X,MAAH,cAAa4X,EAAE,CAAChZ,UAAhB,CAAtB;;AACA,QAAMmZ,iBAAiB,GAAG,GAAG/X,MAAH,CAAUoX,SAAS,CAAC,GAAD,CAAT,IAAkB,EAA5B,EAAgCA,SAAS,CAACS,MAAD,CAAT,IAAqB,EAArD,CAA1B;AAEAC,IAAAA,aAAa,CAACtkB,OAAd,CAAsB,UAAAshB,IAAI,EAAI;AAC5B,UAAI,CAACD,gBAAgB,CAACC,IAAD,EAAOiD,iBAAP,CAArB,EAAgD;AAC9CH,QAAAA,EAAE,CAAClZ,eAAH,CAAmBoW,IAAI,CAACG,QAAxB;AACD;AACF,KAJD;AA3B4D;;AAc9D,OAAK,IAAI7c,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAGif,QAAQ,CAACtf,MAA/B,EAAuCD,CAAC,GAAGM,GAA3C,EAAgDN,CAAC,EAAjD,EAAqD;AAAA,qBAA5CA,CAA4C;;AAAA,6BAOjD;AAWH;;AAED,SAAOof,eAAe,CAACriB,IAAhB,CAAqB6iB,SAA5B;AACD;;AC9FD;AACA;AACA;AACA;AACA;;AAEA,IAAMjc,MAAI,GAAG,SAAb;AACA,IAAMH,UAAQ,GAAG,YAAjB;AACA,IAAMI,WAAS,SAAOJ,UAAtB;AACA,IAAMqc,YAAY,GAAG,YAArB;AACA,IAAMC,kBAAkB,GAAG,IAAIrkB,MAAJ,aAAqBokB,YAArB,WAAyC,GAAzC,CAA3B;AACA,IAAME,qBAAqB,GAAG,IAAIjhB,GAAJ,CAAQ,CAAC,UAAD,EAAa,WAAb,EAA0B,YAA1B,CAAR,CAA9B;AAEA,IAAMyK,aAAW,GAAG;AAClByW,EAAAA,SAAS,EAAE,SADO;AAElBC,EAAAA,QAAQ,EAAE,QAFQ;AAGlBC,EAAAA,KAAK,EAAE,2BAHW;AAIlB/d,EAAAA,OAAO,EAAE,QAJS;AAKlBge,EAAAA,KAAK,EAAE,iBALW;AAMlBC,EAAAA,IAAI,EAAE,SANY;AAOlBrnB,EAAAA,QAAQ,EAAE,kBAPQ;AAQlBge,EAAAA,SAAS,EAAE,mBARO;AASlBvE,EAAAA,SAAS,EAAE,0BATO;AAUlB6N,EAAAA,kBAAkB,EAAE,cAVF;AAWlBhL,EAAAA,QAAQ,EAAE,kBAXQ;AAYlBiL,EAAAA,WAAW,EAAE,mBAZK;AAalBC,EAAAA,QAAQ,EAAE,SAbQ;AAclBtB,EAAAA,UAAU,EAAE,iBAdM;AAelBD,EAAAA,SAAS,EAAE,QAfO;AAgBlBzJ,EAAAA,YAAY,EAAE;AAhBI,CAApB;AAmBA,IAAMiL,aAAa,GAAG;AACpBC,EAAAA,IAAI,EAAE,MADc;AAEpBC,EAAAA,GAAG,EAAE,KAFe;AAGpBC,EAAAA,KAAK,EAAEvjB,KAAK,GAAG,MAAH,GAAY,OAHJ;AAIpBwjB,EAAAA,MAAM,EAAE,QAJY;AAKpBC,EAAAA,IAAI,EAAEzjB,KAAK,GAAG,OAAH,GAAa;AALJ,CAAtB;AAQA,IAAM4L,SAAO,GAAG;AACdgX,EAAAA,SAAS,EAAE,IADG;AAEdC,EAAAA,QAAQ,EAAE,yCACE,mCADF,GAEE,mCAFF,GAGA,QALI;AAMd9d,EAAAA,OAAO,EAAE,aANK;AAOd+d,EAAAA,KAAK,EAAE,EAPO;AAQdC,EAAAA,KAAK,EAAE,CARO;AASdC,EAAAA,IAAI,EAAE,KATQ;AAUdrnB,EAAAA,QAAQ,EAAE,KAVI;AAWdge,EAAAA,SAAS,EAAE,KAXG;AAYdvE,EAAAA,SAAS,EAAE,KAZG;AAad6N,EAAAA,kBAAkB,EAAE,IAbN;AAcdhL,EAAAA,QAAQ,EAAE,iBAdI;AAediL,EAAAA,WAAW,EAAE,EAfC;AAgBdC,EAAAA,QAAQ,EAAE,IAhBI;AAiBdtB,EAAAA,UAAU,EAAE,IAjBE;AAkBdD,EAAAA,SAAS,EAAE/B,gBAlBG;AAmBd1H,EAAAA,YAAY,EAAE;AAnBA,CAAhB;AAsBA,IAAMrb,OAAK,GAAG;AACZ4mB,EAAAA,IAAI,WAASld,WADD;AAEZmd,EAAAA,MAAM,aAAWnd,WAFL;AAGZod,EAAAA,IAAI,WAASpd,WAHD;AAIZqd,EAAAA,KAAK,YAAUrd,WAJH;AAKZsd,EAAAA,QAAQ,eAAatd,WALT;AAMZud,EAAAA,KAAK,YAAUvd,WANH;AAOZwd,EAAAA,OAAO,cAAYxd,WAPP;AAQZyd,EAAAA,QAAQ,eAAazd,WART;AASZ0d,EAAAA,UAAU,iBAAe1d,WATb;AAUZ2d,EAAAA,UAAU,iBAAe3d;AAVb,CAAd;AAaA,IAAM4U,iBAAe,GAAG,MAAxB;AACA,IAAMgJ,gBAAgB,GAAG,OAAzB;AACA,IAAMtQ,iBAAe,GAAG,MAAxB;AAEA,IAAMuQ,gBAAgB,GAAG,MAAzB;AACA,IAAMC,eAAe,GAAG,KAAxB;AAEA,IAAMC,sBAAsB,GAAG,gBAA/B;AAEA,IAAMC,aAAa,GAAG,OAAtB;AACA,IAAMC,aAAa,GAAG,OAAtB;AACA,IAAMC,aAAa,GAAG,OAAtB;AACA,IAAMC,cAAc,GAAG,QAAvB;AAEA;AACA;AACA;AACA;AACA;;IAEMC;;;AACJ,mBAAYlpB,OAAZ,EAAqBkC,MAArB,EAA6B;AAAA;;AAC3B,QAAI,OAAOmb,MAAP,KAAkB,WAAtB,EAAmC;AACjC,YAAM,IAAI3F,SAAJ,CAAc,8DAAd,CAAN;AACD;;AAED,sCAAM1X,OAAN,UAL2B;;AAQ3B,UAAKmpB,UAAL,GAAkB,IAAlB;AACA,UAAKC,QAAL,GAAgB,CAAhB;AACA,UAAKC,WAAL,GAAmB,EAAnB;AACA,UAAKC,cAAL,GAAsB,EAAtB;AACA,UAAK3M,OAAL,GAAe,IAAf,CAZ2B;;AAe3B,UAAKza,MAAL,GAAc,MAAKoR,UAAL,CAAgBpR,MAAhB,CAAd;AACA,UAAKqnB,GAAL,GAAW,IAAX;;AAEA,UAAKC,aAAL;;AAlB2B;AAmB5B;;;;;AA4BD;SAEAC,SAAA,kBAAS;AACP,SAAKN,UAAL,GAAkB,IAAlB;AACD;;SAEDO,UAAA,mBAAU;AACR,SAAKP,UAAL,GAAkB,KAAlB;AACD;;SAEDQ,gBAAA,yBAAgB;AACd,SAAKR,UAAL,GAAkB,CAAC,KAAKA,UAAxB;AACD;;SAEDrc,SAAA,gBAAOvG,KAAP,EAAc;AACZ,QAAI,CAAC,KAAK4iB,UAAV,EAAsB;AACpB;AACD;;AAED,QAAI5iB,KAAJ,EAAW;AACT,UAAMqjB,OAAO,GAAG,KAAKnf,WAAL,CAAiBC,QAAjC;AACA,UAAIgU,OAAO,GAAGxZ,IAAI,CAACG,OAAL,CAAakB,KAAK,CAACC,cAAnB,EAAmCojB,OAAnC,CAAd;;AAEA,UAAI,CAAClL,OAAL,EAAc;AACZA,QAAAA,OAAO,GAAG,IAAI,KAAKjU,WAAT,CAAqBlE,KAAK,CAACC,cAA3B,EAA2C,KAAKqjB,kBAAL,EAA3C,CAAV;AACA3kB,QAAAA,IAAI,CAACC,OAAL,CAAaoB,KAAK,CAACC,cAAnB,EAAmCojB,OAAnC,EAA4ClL,OAA5C;AACD;;AAEDA,MAAAA,OAAO,CAAC4K,cAAR,CAAuBQ,KAAvB,GAA+B,CAACpL,OAAO,CAAC4K,cAAR,CAAuBQ,KAAvD;;AAEA,UAAIpL,OAAO,CAACqL,oBAAR,EAAJ,EAAoC;AAClCrL,QAAAA,OAAO,CAACsL,MAAR,CAAe,IAAf,EAAqBtL,OAArB;AACD,OAFD,MAEO;AACLA,QAAAA,OAAO,CAACuL,MAAR,CAAe,IAAf,EAAqBvL,OAArB;AACD;AACF,KAhBD,MAgBO;AACL,UAAI,KAAKwL,aAAL,GAAqBne,SAArB,CAA+BE,QAA/B,CAAwCmM,iBAAxC,CAAJ,EAA8D;AAC5D,aAAK6R,MAAL,CAAY,IAAZ,EAAkB,IAAlB;;AACA;AACD;;AAED,WAAKD,MAAL,CAAY,IAAZ,EAAkB,IAAlB;AACD;AACF;;SAEDrf,UAAA,mBAAU;AACR4K,IAAAA,YAAY,CAAC,KAAK6T,QAAN,CAAZ;AAEA1iB,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK6D,QAAtB,EAAgC,KAAKC,WAAL,CAAiBK,SAAjD;AACApE,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK6D,QAAL,CAAcsB,OAAd,OAA0B4c,gBAA1B,CAAjB,EAAgE,eAAhE,EAAiF,KAAKyB,iBAAtF;;AAEA,QAAI,KAAKZ,GAAT,EAAc;AACZ,WAAKA,GAAL,CAAStmB,UAAT,CAAoBkJ,WAApB,CAAgC,KAAKod,GAArC;AACD;;AAED,SAAKJ,UAAL,GAAkB,IAAlB;AACA,SAAKC,QAAL,GAAgB,IAAhB;AACA,SAAKC,WAAL,GAAmB,IAAnB;AACA,SAAKC,cAAL,GAAsB,IAAtB;;AACA,QAAI,KAAK3M,OAAT,EAAkB;AAChB,WAAKA,OAAL,CAAae,OAAb;AACD;;AAED,SAAKf,OAAL,GAAe,IAAf;AACA,SAAKza,MAAL,GAAc,IAAd;AACA,SAAKqnB,GAAL,GAAW,IAAX;;AACA,6BAAM5e,OAAN;AACD;;SAED4O,OAAA,gBAAO;AAAA;;AACL,QAAI,KAAK/O,QAAL,CAAcxH,KAAd,CAAoBI,OAApB,KAAgC,MAApC,EAA4C;AAC1C,YAAM,IAAIP,KAAJ,CAAU,qCAAV,CAAN;AACD;;AAED,QAAI,KAAKunB,aAAL,MAAwB,KAAKjB,UAAjC,EAA6C;AAC3C,UAAM/L,SAAS,GAAG1W,YAAY,CAAC2C,OAAb,CAAqB,KAAKmB,QAA1B,EAAoC,KAAKC,WAAL,CAAiBrJ,KAAjB,CAAuB8mB,IAA3D,CAAlB;AACA,UAAMmC,UAAU,GAAG/mB,cAAc,CAAC,KAAKkH,QAAN,CAAjC;AACA,UAAM8f,UAAU,GAAGD,UAAU,KAAK,IAAf,GACjB,KAAK7f,QAAL,CAAc+f,aAAd,CAA4BhnB,eAA5B,CAA4C0I,QAA5C,CAAqD,KAAKzB,QAA1D,CADiB,GAEjB6f,UAAU,CAACpe,QAAX,CAAoB,KAAKzB,QAAzB,CAFF;;AAIA,UAAI4S,SAAS,CAACzT,gBAAV,IAA8B,CAAC2gB,UAAnC,EAA+C;AAC7C;AACD;;AAED,UAAMf,GAAG,GAAG,KAAKW,aAAL,EAAZ;AACA,UAAMM,KAAK,GAAGhrB,MAAM,CAAC,KAAKiL,WAAL,CAAiBI,IAAlB,CAApB;AAEA0e,MAAAA,GAAG,CAACxc,YAAJ,CAAiB,IAAjB,EAAuByd,KAAvB;;AACA,WAAKhgB,QAAL,CAAcuC,YAAd,CAA2B,kBAA3B,EAA+Cyd,KAA/C;;AAEA,WAAKC,UAAL;;AAEA,UAAI,KAAKvoB,MAAL,CAAYglB,SAAhB,EAA2B;AACzBqC,QAAAA,GAAG,CAACxd,SAAJ,CAAc2J,GAAd,CAAkBgK,iBAAlB;AACD;;AAED,UAAMzB,SAAS,GAAG,OAAO,KAAK/b,MAAL,CAAY+b,SAAnB,KAAiC,UAAjC,GAChB,KAAK/b,MAAL,CAAY+b,SAAZ,CAAsB5e,IAAtB,CAA2B,IAA3B,EAAiCkqB,GAAjC,EAAsC,KAAK/e,QAA3C,CADgB,GAEhB,KAAKtI,MAAL,CAAY+b,SAFd;;AAIA,UAAMyM,UAAU,GAAG,KAAKC,cAAL,CAAoB1M,SAApB,CAAnB;;AACA,WAAK2M,mBAAL,CAAyBF,UAAzB;;AAEA,UAAMhR,SAAS,GAAG,KAAKmR,aAAL,EAAlB;;AACA3lB,MAAAA,IAAI,CAACC,OAAL,CAAaokB,GAAb,EAAkB,KAAK9e,WAAL,CAAiBC,QAAnC,EAA6C,IAA7C;;AAEA,UAAI,CAAC,KAAKF,QAAL,CAAc+f,aAAd,CAA4BhnB,eAA5B,CAA4C0I,QAA5C,CAAqD,KAAKsd,GAA1D,CAAL,EAAqE;AACnE7P,QAAAA,SAAS,CAAC0H,WAAV,CAAsBmI,GAAtB;AACD;;AAED7iB,MAAAA,YAAY,CAAC2C,OAAb,CAAqB,KAAKmB,QAA1B,EAAoC,KAAKC,WAAL,CAAiBrJ,KAAjB,CAAuBgnB,QAA3D;AAEA,WAAKzL,OAAL,GAAeU,YAAA,CAAoB,KAAK7S,QAAzB,EAAmC+e,GAAnC,EAAwC,KAAKhM,gBAAL,CAAsBmN,UAAtB,CAAxC,CAAf;AAEAnB,MAAAA,GAAG,CAACxd,SAAJ,CAAc2J,GAAd,CAAkB0C,iBAAlB;AAEA,UAAMoP,WAAW,GAAG,OAAO,KAAKtlB,MAAL,CAAYslB,WAAnB,KAAmC,UAAnC,GAAgD,KAAKtlB,MAAL,CAAYslB,WAAZ,EAAhD,GAA4E,KAAKtlB,MAAL,CAAYslB,WAA5G;;AACA,UAAIA,WAAJ,EAAiB;AAAA;;AACf,0BAAA+B,GAAG,CAACxd,SAAJ,EAAc2J,GAAd,uBAAqB8R,WAAW,CAACvmB,KAAZ,CAAkB,GAAlB,CAArB;AACD,OA9C0C;AAiD3C;AACA;AACA;;;AACA,UAAI,kBAAkBpB,QAAQ,CAAC0D,eAA/B,EAAgD;AAAA;;AAC9C,oBAAGuL,MAAH,aAAajP,QAAQ,CAACoE,IAAT,CAAciL,QAA3B,EAAqC5M,OAArC,CAA6C,UAAAtC,OAAO,EAAI;AACtD0G,UAAAA,YAAY,CAACmC,EAAb,CAAgB7I,OAAhB,EAAyB,WAAzB,EAAsC4D,IAAI,EAA1C;AACD,SAFD;AAGD;;AAED,UAAMsW,QAAQ,GAAG,SAAXA,QAAW,GAAM;AACrB,YAAM4Q,cAAc,GAAG,MAAI,CAACzB,WAA5B;AAEA,QAAA,MAAI,CAACA,WAAL,GAAmB,IAAnB;AACA3iB,QAAAA,YAAY,CAAC2C,OAAb,CAAqB,MAAI,CAACmB,QAA1B,EAAoC,MAAI,CAACC,WAAL,CAAiBrJ,KAAjB,CAAuB+mB,KAA3D;;AAEA,YAAI2C,cAAc,KAAKlC,eAAvB,EAAwC;AACtC,UAAA,MAAI,CAACqB,MAAL,CAAY,IAAZ,EAAkB,MAAlB;AACD;AACF,OATD;;AAWA,UAAI,KAAKV,GAAL,CAASxd,SAAT,CAAmBE,QAAnB,CAA4ByT,iBAA5B,CAAJ,EAAkD;AAChD,YAAM/e,kBAAkB,GAAGH,gCAAgC,CAAC,KAAK+oB,GAAN,CAA3D;AACA7iB,QAAAA,YAAY,CAACoC,GAAb,CAAiB,KAAKygB,GAAtB,EAA2BvqB,cAA3B,EAA2Ckb,QAA3C;AACA3Y,QAAAA,oBAAoB,CAAC,KAAKgoB,GAAN,EAAW5oB,kBAAX,CAApB;AACD,OAJD,MAIO;AACLuZ,QAAAA,QAAQ;AACT;AACF;AACF;;SAEDZ,OAAA,gBAAO;AAAA;;AACL,QAAI,CAAC,KAAKqD,OAAV,EAAmB;AACjB;AACD;;AAED,QAAM4M,GAAG,GAAG,KAAKW,aAAL,EAAZ;;AACA,QAAMhQ,QAAQ,GAAG,SAAXA,QAAW,GAAM;AACrB,UAAI,MAAI,CAACmP,WAAL,KAAqBV,gBAArB,IAAyCY,GAAG,CAACtmB,UAAjD,EAA6D;AAC3DsmB,QAAAA,GAAG,CAACtmB,UAAJ,CAAekJ,WAAf,CAA2Bod,GAA3B;AACD;;AAED,MAAA,MAAI,CAACwB,cAAL;;AACA,MAAA,MAAI,CAACvgB,QAAL,CAAcgD,eAAd,CAA8B,kBAA9B;;AACA9G,MAAAA,YAAY,CAAC2C,OAAb,CAAqB,MAAI,CAACmB,QAA1B,EAAoC,MAAI,CAACC,WAAL,CAAiBrJ,KAAjB,CAAuB6mB,MAA3D;;AAEA,UAAI,MAAI,CAACtL,OAAT,EAAkB;AAChB,QAAA,MAAI,CAACA,OAAL,CAAae,OAAb;;AACA,QAAA,MAAI,CAACf,OAAL,GAAe,IAAf;AACD;AACF,KAbD;;AAeA,QAAMc,SAAS,GAAG/W,YAAY,CAAC2C,OAAb,CAAqB,KAAKmB,QAA1B,EAAoC,KAAKC,WAAL,CAAiBrJ,KAAjB,CAAuB4mB,IAA3D,CAAlB;;AACA,QAAIvK,SAAS,CAAC9T,gBAAd,EAAgC;AAC9B;AACD;;AAED4f,IAAAA,GAAG,CAACxd,SAAJ,CAAcC,MAAd,CAAqBoM,iBAArB,EA1BK;AA6BL;;AACA,QAAI,kBAAkBvY,QAAQ,CAAC0D,eAA/B,EAAgD;AAAA;;AAC9C,mBAAGuL,MAAH,cAAajP,QAAQ,CAACoE,IAAT,CAAciL,QAA3B,EACG5M,OADH,CACW,UAAAtC,OAAO;AAAA,eAAI0G,YAAY,CAACC,GAAb,CAAiB3G,OAAjB,EAA0B,WAA1B,EAAuC4D,IAAvC,CAAJ;AAAA,OADlB;AAED;;AAED,SAAK0lB,cAAL,CAAoBN,aAApB,IAAqC,KAArC;AACA,SAAKM,cAAL,CAAoBP,aAApB,IAAqC,KAArC;AACA,SAAKO,cAAL,CAAoBR,aAApB,IAAqC,KAArC;;AAEA,QAAI,KAAKS,GAAL,CAASxd,SAAT,CAAmBE,QAAnB,CAA4ByT,iBAA5B,CAAJ,EAAkD;AAChD,UAAM/e,kBAAkB,GAAGH,gCAAgC,CAAC+oB,GAAD,CAA3D;AAEA7iB,MAAAA,YAAY,CAACoC,GAAb,CAAiBygB,GAAjB,EAAsBvqB,cAAtB,EAAsCkb,QAAtC;AACA3Y,MAAAA,oBAAoB,CAACgoB,GAAD,EAAM5oB,kBAAN,CAApB;AACD,KALD,MAKO;AACLuZ,MAAAA,QAAQ;AACT;;AAED,SAAKmP,WAAL,GAAmB,EAAnB;AACD;;SAED1L,SAAA,kBAAS;AACP,QAAI,KAAKhB,OAAL,KAAiB,IAArB,EAA2B;AACzB,WAAKA,OAAL,CAAagB,MAAb;AACD;AACF;;;SAIDyM,gBAAA,yBAAgB;AACd,WAAO7hB,OAAO,CAAC,KAAKyiB,QAAL,EAAD,CAAd;AACD;;SAEDd,gBAAA,yBAAgB;AACd,QAAI,KAAKX,GAAT,EAAc;AACZ,aAAO,KAAKA,GAAZ;AACD;;AAED,QAAMvpB,OAAO,GAAGH,QAAQ,CAAC+hB,aAAT,CAAuB,KAAvB,CAAhB;AACA5hB,IAAAA,OAAO,CAAC8mB,SAAR,GAAoB,KAAK5kB,MAAL,CAAYilB,QAAhC;AAEA,SAAKoC,GAAL,GAAWvpB,OAAO,CAACkP,QAAR,CAAiB,CAAjB,CAAX;AACA,WAAO,KAAKqa,GAAZ;AACD;;SAEDkB,aAAA,sBAAa;AACX,QAAMlB,GAAG,GAAG,KAAKW,aAAL,EAAZ;AACA,SAAKe,iBAAL,CAAuBtc,cAAc,CAACM,OAAf,CAAuB4Z,sBAAvB,EAA+CU,GAA/C,CAAvB,EAA4E,KAAKyB,QAAL,EAA5E;AACAzB,IAAAA,GAAG,CAACxd,SAAJ,CAAcC,MAAd,CAAqB0T,iBAArB,EAAsCtH,iBAAtC;AACD;;SAED6S,oBAAA,2BAAkBjrB,OAAlB,EAA2BkrB,OAA3B,EAAoC;AAClC,QAAIlrB,OAAO,KAAK,IAAhB,EAAsB;AACpB;AACD;;AAED,QAAI,OAAOkrB,OAAP,KAAmB,QAAnB,IAA+B7pB,SAAS,CAAC6pB,OAAD,CAA5C,EAAuD;AACrD,UAAIA,OAAO,CAAC3Q,MAAZ,EAAoB;AAClB2Q,QAAAA,OAAO,GAAGA,OAAO,CAAC,CAAD,CAAjB;AACD,OAHoD;;;AAMrD,UAAI,KAAKhpB,MAAL,CAAYolB,IAAhB,EAAsB;AACpB,YAAI4D,OAAO,CAACjoB,UAAR,KAAuBjD,OAA3B,EAAoC;AAClCA,UAAAA,OAAO,CAAC8mB,SAAR,GAAoB,EAApB;AACA9mB,UAAAA,OAAO,CAACohB,WAAR,CAAoB8J,OAApB;AACD;AACF,OALD,MAKO;AACLlrB,QAAAA,OAAO,CAACmrB,WAAR,GAAsBD,OAAO,CAACC,WAA9B;AACD;;AAED;AACD;;AAED,QAAI,KAAKjpB,MAAL,CAAYolB,IAAhB,EAAsB;AACpB,UAAI,KAAKplB,MAAL,CAAYulB,QAAhB,EAA0B;AACxByD,QAAAA,OAAO,GAAGlF,YAAY,CAACkF,OAAD,EAAU,KAAKhpB,MAAL,CAAYgkB,SAAtB,EAAiC,KAAKhkB,MAAL,CAAYikB,UAA7C,CAAtB;AACD;;AAEDnmB,MAAAA,OAAO,CAAC8mB,SAAR,GAAoBoE,OAApB;AACD,KAND,MAMO;AACLlrB,MAAAA,OAAO,CAACmrB,WAAR,GAAsBD,OAAtB;AACD;AACF;;SAEDF,WAAA,oBAAW;AACT,QAAI5D,KAAK,GAAG,KAAK5c,QAAL,CAActK,YAAd,CAA2B,wBAA3B,CAAZ;;AAEA,QAAI,CAACknB,KAAL,EAAY;AACVA,MAAAA,KAAK,GAAG,OAAO,KAAKllB,MAAL,CAAYklB,KAAnB,KAA6B,UAA7B,GACN,KAAKllB,MAAL,CAAYklB,KAAZ,CAAkB/nB,IAAlB,CAAuB,KAAKmL,QAA5B,CADM,GAEN,KAAKtI,MAAL,CAAYklB,KAFd;AAGD;;AAED,WAAOA,KAAP;AACD;;SAEDgE,mBAAA,0BAAiBV,UAAjB,EAA6B;AAC3B,QAAIA,UAAU,KAAK,OAAnB,EAA4B;AAC1B,aAAO,KAAP;AACD;;AAED,QAAIA,UAAU,KAAK,MAAnB,EAA2B;AACzB,aAAO,OAAP;AACD;;AAED,WAAOA,UAAP;AACD;;;SAIDnN,mBAAA,0BAAiBmN,UAAjB,EAA6B;AAAA;;AAC3B,QAAMW,YAAY,GAAG;AACnBlN,MAAAA,IAAI,EAAE,MADa;AAEnBC,MAAAA,OAAO,EAAE;AACPC,QAAAA,WAAW,EAAE;AADN;AAFU,KAArB;;AAOA,QAAI,KAAKnc,MAAL,CAAYqlB,kBAAhB,EAAoC;AAClC8D,MAAAA,YAAY,CAACjN,OAAb,CAAqBmJ,kBAArB,GAA0C,KAAKrlB,MAAL,CAAYqlB,kBAAtD;AACD;;AAED,QAAM+D,eAAe,GAAG;AACtBrN,MAAAA,SAAS,EAAEyM,UADW;AAEtBxM,MAAAA,SAAS,EAAE,CACTmN,YADS,EAET;AACElN,QAAAA,IAAI,EAAE,iBADR;AAEEC,QAAAA,OAAO,EAAE;AACPE,UAAAA,YAAY,EAAE,KAAKpc,MAAL,CAAYqa;AADnB;AAFX,OAFS,EAQT;AACE4B,QAAAA,IAAI,EAAE,OADR;AAEEC,QAAAA,OAAO,EAAE;AACPpe,UAAAA,OAAO,QAAM,KAAKyK,WAAL,CAAiBI,IAAvB;AADA;AAFX,OARS,EAcT;AACEsT,QAAAA,IAAI,EAAE,UADR;AAEEI,QAAAA,OAAO,EAAE,IAFX;AAGEgN,QAAAA,KAAK,EAAE,YAHT;AAIEllB,QAAAA,EAAE,EAAE,YAAAxB,IAAI;AAAA,iBAAI,MAAI,CAAC2mB,4BAAL,CAAkC3mB,IAAlC,CAAJ;AAAA;AAJV,OAdS,CAFW;AAuBtB4mB,MAAAA,aAAa,EAAE,uBAAA5mB,IAAI,EAAI;AACrB,YAAIA,IAAI,CAACuZ,OAAL,CAAaH,SAAb,KAA2BpZ,IAAI,CAACoZ,SAApC,EAA+C;AAC7C,UAAA,MAAI,CAACuN,4BAAL,CAAkC3mB,IAAlC;AACD;AACF;AA3BqB,KAAxB;AA8BA,wBACKymB,eADL,EAEK,KAAKppB,MAAL,CAAYua,YAFjB;AAID;;SAEDmO,sBAAA,6BAAoBF,UAApB,EAAgC;AAC9B,SAAKR,aAAL,GAAqBne,SAArB,CAA+B2J,GAA/B,CAAsCqR,YAAtC,SAAsD,KAAKqE,gBAAL,CAAsBV,UAAtB,CAAtD;AACD;;SAEDG,gBAAA,yBAAgB;AACd,QAAI,KAAK3oB,MAAL,CAAYwX,SAAZ,KAA0B,KAA9B,EAAqC;AACnC,aAAO7Z,QAAQ,CAACoE,IAAhB;AACD;;AAED,QAAI5C,SAAS,CAAC,KAAKa,MAAL,CAAYwX,SAAb,CAAb,EAAsC;AACpC,aAAO,KAAKxX,MAAL,CAAYwX,SAAnB;AACD;;AAED,WAAO/K,cAAc,CAACM,OAAf,CAAuB,KAAK/M,MAAL,CAAYwX,SAAnC,CAAP;AACD;;SAEDiR,iBAAA,wBAAe1M,SAAf,EAA0B;AACxB,WAAOyJ,aAAa,CAACzJ,SAAS,CAACnb,WAAV,EAAD,CAApB;AACD;;SAED0mB,gBAAA,yBAAgB;AAAA;;AACd,QAAMkC,QAAQ,GAAG,KAAKxpB,MAAL,CAAYmH,OAAZ,CAAoBpI,KAApB,CAA0B,GAA1B,CAAjB;AAEAyqB,IAAAA,QAAQ,CAACppB,OAAT,CAAiB,UAAA+G,OAAO,EAAI;AAC1B,UAAIA,OAAO,KAAK,OAAhB,EAAyB;AACvB3C,QAAAA,YAAY,CAACmC,EAAb,CAAgB,MAAI,CAAC2B,QAArB,EAA+B,MAAI,CAACC,WAAL,CAAiBrJ,KAAjB,CAAuBinB,KAAtD,EAA6D,MAAI,CAACnmB,MAAL,CAAYjC,QAAzE,EAAmF,UAAAsG,KAAK;AAAA,iBAAI,MAAI,CAACuG,MAAL,CAAYvG,KAAZ,CAAJ;AAAA,SAAxF;AAED,OAHD,MAGO,IAAI8C,OAAO,KAAK4f,cAAhB,EAAgC;AACrC,YAAM0C,OAAO,GAAGtiB,OAAO,KAAKyf,aAAZ,GACd,MAAI,CAACre,WAAL,CAAiBrJ,KAAjB,CAAuBonB,UADT,GAEd,MAAI,CAAC/d,WAAL,CAAiBrJ,KAAjB,CAAuBknB,OAFzB;AAGA,YAAMsD,QAAQ,GAAGviB,OAAO,KAAKyf,aAAZ,GACf,MAAI,CAACre,WAAL,CAAiBrJ,KAAjB,CAAuBqnB,UADR,GAEf,MAAI,CAAChe,WAAL,CAAiBrJ,KAAjB,CAAuBmnB,QAFzB;AAIA7hB,QAAAA,YAAY,CAACmC,EAAb,CAAgB,MAAI,CAAC2B,QAArB,EAA+BmhB,OAA/B,EAAwC,MAAI,CAACzpB,MAAL,CAAYjC,QAApD,EAA8D,UAAAsG,KAAK;AAAA,iBAAI,MAAI,CAACyjB,MAAL,CAAYzjB,KAAZ,CAAJ;AAAA,SAAnE;AACAG,QAAAA,YAAY,CAACmC,EAAb,CAAgB,MAAI,CAAC2B,QAArB,EAA+BohB,QAA/B,EAAyC,MAAI,CAAC1pB,MAAL,CAAYjC,QAArD,EAA+D,UAAAsG,KAAK;AAAA,iBAAI,MAAI,CAAC0jB,MAAL,CAAY1jB,KAAZ,CAAJ;AAAA,SAApE;AACD;AACF,KAfD;;AAiBA,SAAK4jB,iBAAL,GAAyB,YAAM;AAC7B,UAAI,MAAI,CAAC3f,QAAT,EAAmB;AACjB,QAAA,MAAI,CAAC8O,IAAL;AACD;AACF,KAJD;;AAMA5S,IAAAA,YAAY,CAACmC,EAAb,CAAgB,KAAK2B,QAAL,CAAcsB,OAAd,OAA0B4c,gBAA1B,CAAhB,EAA+D,eAA/D,EAAgF,KAAKyB,iBAArF;;AAEA,QAAI,KAAKjoB,MAAL,CAAYjC,QAAhB,EAA0B;AACxB,WAAKiC,MAAL,gBACK,KAAKA,MADV;AAEEmH,QAAAA,OAAO,EAAE,QAFX;AAGEpJ,QAAAA,QAAQ,EAAE;AAHZ;AAKD,KAND,MAMO;AACL,WAAK4rB,SAAL;AACD;AACF;;SAEDA,YAAA,qBAAY;AACV,QAAMzE,KAAK,GAAG,KAAK5c,QAAL,CAActK,YAAd,CAA2B,OAA3B,CAAd;;AACA,QAAM4rB,iBAAiB,GAAG,OAAO,KAAKthB,QAAL,CAActK,YAAd,CAA2B,wBAA3B,CAAjC;;AAEA,QAAIknB,KAAK,IAAI0E,iBAAiB,KAAK,QAAnC,EAA6C;AAC3C,WAAKthB,QAAL,CAAcuC,YAAd,CAA2B,wBAA3B,EAAqDqa,KAAK,IAAI,EAA9D;;AACA,UAAIA,KAAK,IAAI,CAAC,KAAK5c,QAAL,CAActK,YAAd,CAA2B,YAA3B,CAAV,IAAsD,CAAC,KAAKsK,QAAL,CAAc2gB,WAAzE,EAAsF;AACpF,aAAK3gB,QAAL,CAAcuC,YAAd,CAA2B,YAA3B,EAAyCqa,KAAzC;AACD;;AAED,WAAK5c,QAAL,CAAcuC,YAAd,CAA2B,OAA3B,EAAoC,EAApC;AACD;AACF;;SAEDid,SAAA,gBAAOzjB,KAAP,EAAcmY,OAAd,EAAuB;AACrB,QAAMkL,OAAO,GAAG,KAAKnf,WAAL,CAAiBC,QAAjC;AACAgU,IAAAA,OAAO,GAAGA,OAAO,IAAIxZ,IAAI,CAACG,OAAL,CAAakB,KAAK,CAACC,cAAnB,EAAmCojB,OAAnC,CAArB;;AAEA,QAAI,CAAClL,OAAL,EAAc;AACZA,MAAAA,OAAO,GAAG,IAAI,KAAKjU,WAAT,CACRlE,KAAK,CAACC,cADE,EAER,KAAKqjB,kBAAL,EAFQ,CAAV;AAIA3kB,MAAAA,IAAI,CAACC,OAAL,CAAaoB,KAAK,CAACC,cAAnB,EAAmCojB,OAAnC,EAA4ClL,OAA5C;AACD;;AAED,QAAInY,KAAJ,EAAW;AACTmY,MAAAA,OAAO,CAAC4K,cAAR,CACE/iB,KAAK,CAACK,IAAN,KAAe,SAAf,GAA2BmiB,aAA3B,GAA2CD,aAD7C,IAEI,IAFJ;AAGD;;AAED,QAAIpK,OAAO,CAACwL,aAAR,GAAwBne,SAAxB,CAAkCE,QAAlC,CAA2CmM,iBAA3C,KAA+DsG,OAAO,CAAC2K,WAAR,KAAwBV,gBAA3F,EAA6G;AAC3GjK,MAAAA,OAAO,CAAC2K,WAAR,GAAsBV,gBAAtB;AACA;AACD;;AAEDpT,IAAAA,YAAY,CAACmJ,OAAO,CAAC0K,QAAT,CAAZ;AAEA1K,IAAAA,OAAO,CAAC2K,WAAR,GAAsBV,gBAAtB;;AAEA,QAAI,CAACjK,OAAO,CAACxc,MAAR,CAAemlB,KAAhB,IAAyB,CAAC3I,OAAO,CAACxc,MAAR,CAAemlB,KAAf,CAAqB9N,IAAnD,EAAyD;AACvDmF,MAAAA,OAAO,CAACnF,IAAR;AACA;AACD;;AAEDmF,IAAAA,OAAO,CAAC0K,QAAR,GAAmBrnB,UAAU,CAAC,YAAM;AAClC,UAAI2c,OAAO,CAAC2K,WAAR,KAAwBV,gBAA5B,EAA8C;AAC5CjK,QAAAA,OAAO,CAACnF,IAAR;AACD;AACF,KAJ4B,EAI1BmF,OAAO,CAACxc,MAAR,CAAemlB,KAAf,CAAqB9N,IAJK,CAA7B;AAKD;;SAED0Q,SAAA,gBAAO1jB,KAAP,EAAcmY,OAAd,EAAuB;AACrB,QAAMkL,OAAO,GAAG,KAAKnf,WAAL,CAAiBC,QAAjC;AACAgU,IAAAA,OAAO,GAAGA,OAAO,IAAIxZ,IAAI,CAACG,OAAL,CAAakB,KAAK,CAACC,cAAnB,EAAmCojB,OAAnC,CAArB;;AAEA,QAAI,CAAClL,OAAL,EAAc;AACZA,MAAAA,OAAO,GAAG,IAAI,KAAKjU,WAAT,CACRlE,KAAK,CAACC,cADE,EAER,KAAKqjB,kBAAL,EAFQ,CAAV;AAIA3kB,MAAAA,IAAI,CAACC,OAAL,CAAaoB,KAAK,CAACC,cAAnB,EAAmCojB,OAAnC,EAA4ClL,OAA5C;AACD;;AAED,QAAInY,KAAJ,EAAW;AACTmY,MAAAA,OAAO,CAAC4K,cAAR,CACE/iB,KAAK,CAACK,IAAN,KAAe,UAAf,GAA4BmiB,aAA5B,GAA4CD,aAD9C,IAEI,KAFJ;AAGD;;AAED,QAAIpK,OAAO,CAACqL,oBAAR,EAAJ,EAAoC;AAClC;AACD;;AAEDxU,IAAAA,YAAY,CAACmJ,OAAO,CAAC0K,QAAT,CAAZ;AAEA1K,IAAAA,OAAO,CAAC2K,WAAR,GAAsBT,eAAtB;;AAEA,QAAI,CAAClK,OAAO,CAACxc,MAAR,CAAemlB,KAAhB,IAAyB,CAAC3I,OAAO,CAACxc,MAAR,CAAemlB,KAAf,CAAqB/N,IAAnD,EAAyD;AACvDoF,MAAAA,OAAO,CAACpF,IAAR;AACA;AACD;;AAEDoF,IAAAA,OAAO,CAAC0K,QAAR,GAAmBrnB,UAAU,CAAC,YAAM;AAClC,UAAI2c,OAAO,CAAC2K,WAAR,KAAwBT,eAA5B,EAA6C;AAC3ClK,QAAAA,OAAO,CAACpF,IAAR;AACD;AACF,KAJ4B,EAI1BoF,OAAO,CAACxc,MAAR,CAAemlB,KAAf,CAAqB/N,IAJK,CAA7B;AAKD;;SAEDyQ,uBAAA,gCAAuB;AACrB,SAAK,IAAM1gB,OAAX,IAAsB,KAAKigB,cAA3B,EAA2C;AACzC,UAAI,KAAKA,cAAL,CAAoBjgB,OAApB,CAAJ,EAAkC;AAChC,eAAO,IAAP;AACD;AACF;;AAED,WAAO,KAAP;AACD;;SAEDiK,aAAA,oBAAWpR,MAAX,EAAmB;AACjB,QAAM6pB,cAAc,GAAG1e,WAAW,CAACI,iBAAZ,CAA8B,KAAKjD,QAAnC,CAAvB;AAEApI,IAAAA,MAAM,CAACC,IAAP,CAAY0pB,cAAZ,EAA4BzpB,OAA5B,CAAoC,UAAA0pB,QAAQ,EAAI;AAC9C,UAAI/E,qBAAqB,CAAC/e,GAAtB,CAA0B8jB,QAA1B,CAAJ,EAAyC;AACvC,eAAOD,cAAc,CAACC,QAAD,CAArB;AACD;AACF,KAJD;;AAMA,QAAI9pB,MAAM,IAAI,OAAOA,MAAM,CAACwX,SAAd,KAA4B,QAAtC,IAAkDxX,MAAM,CAACwX,SAAP,CAAiBa,MAAvE,EAA+E;AAC7ErY,MAAAA,MAAM,CAACwX,SAAP,GAAmBxX,MAAM,CAACwX,SAAP,CAAiB,CAAjB,CAAnB;AACD;;AAEDxX,IAAAA,MAAM,gBACD,KAAKuI,WAAL,CAAiByF,OADhB,EAED6b,cAFC,EAGA,OAAO7pB,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAHhD,CAAN;;AAMA,QAAI,OAAOA,MAAM,CAACmlB,KAAd,KAAwB,QAA5B,EAAsC;AACpCnlB,MAAAA,MAAM,CAACmlB,KAAP,GAAe;AACb9N,QAAAA,IAAI,EAAErX,MAAM,CAACmlB,KADA;AAEb/N,QAAAA,IAAI,EAAEpX,MAAM,CAACmlB;AAFA,OAAf;AAID;;AAED,QAAI,OAAOnlB,MAAM,CAACklB,KAAd,KAAwB,QAA5B,EAAsC;AACpCllB,MAAAA,MAAM,CAACklB,KAAP,GAAellB,MAAM,CAACklB,KAAP,CAAahoB,QAAb,EAAf;AACD;;AAED,QAAI,OAAO8C,MAAM,CAACgpB,OAAd,KAA0B,QAA9B,EAAwC;AACtChpB,MAAAA,MAAM,CAACgpB,OAAP,GAAiBhpB,MAAM,CAACgpB,OAAP,CAAe9rB,QAAf,EAAjB;AACD;;AAED4C,IAAAA,eAAe,CAAC6I,MAAD,EAAO3I,MAAP,EAAe,KAAKuI,WAAL,CAAiBgG,WAAhC,CAAf;;AAEA,QAAIvO,MAAM,CAACulB,QAAX,EAAqB;AACnBvlB,MAAAA,MAAM,CAACilB,QAAP,GAAkBnB,YAAY,CAAC9jB,MAAM,CAACilB,QAAR,EAAkBjlB,MAAM,CAACgkB,SAAzB,EAAoChkB,MAAM,CAACikB,UAA3C,CAA9B;AACD;;AAED,WAAOjkB,MAAP;AACD;;SAED2nB,qBAAA,8BAAqB;AACnB,QAAM3nB,MAAM,GAAG,EAAf;;AAEA,QAAI,KAAKA,MAAT,EAAiB;AACf,WAAK,IAAM0C,GAAX,IAAkB,KAAK1C,MAAvB,EAA+B;AAC7B,YAAI,KAAKuI,WAAL,CAAiByF,OAAjB,CAAyBtL,GAAzB,MAAkC,KAAK1C,MAAL,CAAY0C,GAAZ,CAAtC,EAAwD;AACtD1C,UAAAA,MAAM,CAAC0C,GAAD,CAAN,GAAc,KAAK1C,MAAL,CAAY0C,GAAZ,CAAd;AACD;AACF;AACF;;AAED,WAAO1C,MAAP;AACD;;SAED6oB,iBAAA,0BAAiB;AACf,QAAMxB,GAAG,GAAG,KAAKW,aAAL,EAAZ;AACA,QAAM+B,QAAQ,GAAG1C,GAAG,CAACrpB,YAAJ,CAAiB,OAAjB,EAA0BZ,KAA1B,CAAgC0nB,kBAAhC,CAAjB;;AACA,QAAIiF,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,CAAC9kB,MAAT,GAAkB,CAA3C,EAA8C;AAC5C8kB,MAAAA,QAAQ,CAACC,GAAT,CAAa,UAAAC,KAAK;AAAA,eAAIA,KAAK,CAAC/rB,IAAN,EAAJ;AAAA,OAAlB,EACGkC,OADH,CACW,UAAA8pB,MAAM;AAAA,eAAI7C,GAAG,CAACxd,SAAJ,CAAcC,MAAd,CAAqBogB,MAArB,CAAJ;AAAA,OADjB;AAED;AACF;;SAEDZ,+BAAA,sCAA6Ba,UAA7B,EAAyC;AAAA,QAC/BC,KAD+B,GACrBD,UADqB,CAC/BC,KAD+B;;AAGvC,QAAI,CAACA,KAAL,EAAY;AACV;AACD;;AAED,SAAK/C,GAAL,GAAW+C,KAAK,CAAC7F,QAAN,CAAe8F,MAA1B;;AACA,SAAKxB,cAAL;;AACA,SAAKH,mBAAL,CAAyB,KAAKD,cAAL,CAAoB2B,KAAK,CAACrO,SAA1B,CAAzB;AACD;;;UAIM7R,kBAAP,yBAAuBlK,MAAvB,EAA+B;AAC7B,WAAO,KAAKmK,IAAL,CAAU,YAAY;AAC3B,UAAIxH,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmBqF,UAAnB,CAAX;;AACA,UAAM2I,OAAO,GAAG,OAAOnR,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C;;AAEA,UAAI,CAAC2C,IAAD,IAAS,eAAejC,IAAf,CAAoBV,MAApB,CAAb,EAA0C;AACxC;AACD;;AAED,UAAI,CAAC2C,IAAL,EAAW;AACTA,QAAAA,IAAI,GAAG,IAAIqkB,OAAJ,CAAY,IAAZ,EAAkB7V,OAAlB,CAAP;AACD;;AAED,UAAI,OAAOnR,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,YAAI,OAAO2C,IAAI,CAAC3C,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,gBAAM,IAAIwV,SAAJ,wBAAkCxV,MAAlC,QAAN;AACD;;AAED2C,QAAAA,IAAI,CAAC3C,MAAD,CAAJ;AACD;AACF,KAnBM,CAAP;AAoBD;;;;wBAnnBoB;AACnB,aAAOgO,SAAP;AACD;;;wBAEiB;AAChB,aAAOrF,MAAP;AACD;;;wBAEqB;AACpB,aAAOH,UAAP;AACD;;;wBAEkB;AACjB,aAAOtJ,OAAP;AACD;;;wBAEsB;AACrB,aAAO0J,WAAP;AACD;;;wBAEwB;AACvB,aAAO2F,aAAP;AACD;;;;EA9CmBlG;AA8oBtB;AACA;AACA;AACA;AACA;AACA;;;AAEApG,kBAAkB,CAAC,YAAM;AACvB,MAAMoF,CAAC,GAAGxF,SAAS,EAAnB;AACA;;AACA,MAAIwF,CAAJ,EAAO;AACL,QAAMiD,kBAAkB,GAAGjD,CAAC,CAAClD,EAAF,CAAKwE,MAAL,CAA3B;AACAtB,IAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,IAAaqe,OAAO,CAAC9c,eAArB;AACA7C,IAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,EAAW4B,WAAX,GAAyByc,OAAzB;;AACA3f,IAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,EAAW6B,UAAX,GAAwB,YAAM;AAC5BnD,MAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,IAAa2B,kBAAb;AACA,aAAO0c,OAAO,CAAC9c,eAAf;AACD,KAHD;AAID;AACF,CAZiB,CAAlB;;ACxwBA;AACA;AACA;AACA;AACA;;AAEA,IAAMvB,MAAI,GAAG,SAAb;AACA,IAAMH,UAAQ,GAAG,YAAjB;AACA,IAAMI,WAAS,SAAOJ,UAAtB;AACA,IAAMqc,cAAY,GAAG,YAArB;AACA,IAAMC,oBAAkB,GAAG,IAAIrkB,MAAJ,aAAqBokB,cAArB,WAAyC,GAAzC,CAA3B;;AAEA,IAAM7W,SAAO,gBACRgZ,OAAO,CAAChZ,OADA;AAEX+N,EAAAA,SAAS,EAAE,OAFA;AAGX5U,EAAAA,OAAO,EAAE,OAHE;AAIX6hB,EAAAA,OAAO,EAAE,EAJE;AAKX/D,EAAAA,QAAQ,EAAE,yCACE,mCADF,GAEI,kCAFJ,GAGE,kCAHF,GAIA;AATC,EAAb;;AAYA,IAAM1W,aAAW,gBACZyY,OAAO,CAACzY,WADI;AAEfya,EAAAA,OAAO,EAAE;AAFM,EAAjB;;AAKA,IAAM9pB,OAAK,GAAG;AACZ4mB,EAAAA,IAAI,WAASld,WADD;AAEZmd,EAAAA,MAAM,aAAWnd,WAFL;AAGZod,EAAAA,IAAI,WAASpd,WAHD;AAIZqd,EAAAA,KAAK,YAAUrd,WAJH;AAKZsd,EAAAA,QAAQ,eAAatd,WALT;AAMZud,EAAAA,KAAK,YAAUvd,WANH;AAOZwd,EAAAA,OAAO,cAAYxd,WAPP;AAQZyd,EAAAA,QAAQ,eAAazd,WART;AASZ0d,EAAAA,UAAU,iBAAe1d,WATb;AAUZ2d,EAAAA,UAAU,iBAAe3d;AAVb,CAAd;AAaA,IAAM4U,iBAAe,GAAG,MAAxB;AACA,IAAMtH,iBAAe,GAAG,MAAxB;AAEA,IAAMoU,cAAc,GAAG,iBAAvB;AACA,IAAMC,gBAAgB,GAAG,eAAzB;AAEA;AACA;AACA;AACA;AACA;;IAEMC;;;;;;;;;AA2BJ;SAEAtC,gBAAA,yBAAgB;AACd,WAAO,KAAKY,QAAL,MAAmB,KAAK2B,WAAL,EAA1B;AACD;;SAEDlC,aAAA,sBAAa;AACX,QAAMlB,GAAG,GAAG,KAAKW,aAAL,EAAZ,CADW;;AAIX,SAAKe,iBAAL,CAAuBtc,cAAc,CAACM,OAAf,CAAuBud,cAAvB,EAAuCjD,GAAvC,CAAvB,EAAoE,KAAKyB,QAAL,EAApE;;AACA,QAAIE,OAAO,GAAG,KAAKyB,WAAL,EAAd;;AACA,QAAI,OAAOzB,OAAP,KAAmB,UAAvB,EAAmC;AACjCA,MAAAA,OAAO,GAAGA,OAAO,CAAC7rB,IAAR,CAAa,KAAKmL,QAAlB,CAAV;AACD;;AAED,SAAKygB,iBAAL,CAAuBtc,cAAc,CAACM,OAAf,CAAuBwd,gBAAvB,EAAyClD,GAAzC,CAAvB,EAAsE2B,OAAtE;AAEA3B,IAAAA,GAAG,CAACxd,SAAJ,CAAcC,MAAd,CAAqB0T,iBAArB,EAAsCtH,iBAAtC;AACD;;;SAIDwS,sBAAA,6BAAoBF,UAApB,EAAgC;AAC9B,SAAKR,aAAL,GAAqBne,SAArB,CAA+B2J,GAA/B,CAAsCqR,cAAtC,SAAsD,KAAKqE,gBAAL,CAAsBV,UAAtB,CAAtD;AACD;;SAEDiC,cAAA,uBAAc;AACZ,WAAO,KAAKniB,QAAL,CAActK,YAAd,CAA2B,iBAA3B,KAAiD,KAAKgC,MAAL,CAAYgpB,OAApE;AACD;;SAEDH,iBAAA,0BAAiB;AACf,QAAMxB,GAAG,GAAG,KAAKW,aAAL,EAAZ;AACA,QAAM+B,QAAQ,GAAG1C,GAAG,CAACrpB,YAAJ,CAAiB,OAAjB,EAA0BZ,KAA1B,CAAgC0nB,oBAAhC,CAAjB;;AACA,QAAIiF,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,CAAC9kB,MAAT,GAAkB,CAA3C,EAA8C;AAC5C8kB,MAAAA,QAAQ,CAACC,GAAT,CAAa,UAAAC,KAAK;AAAA,eAAIA,KAAK,CAAC/rB,IAAN,EAAJ;AAAA,OAAlB,EACGkC,OADH,CACW,UAAA8pB,MAAM;AAAA,eAAI7C,GAAG,CAACxd,SAAJ,CAAcC,MAAd,CAAqBogB,MAArB,CAAJ;AAAA,OADjB;AAED;AACF;;;UAIMhgB,kBAAP,yBAAuBlK,MAAvB,EAA+B;AAC7B,WAAO,KAAKmK,IAAL,CAAU,YAAY;AAC3B,UAAIxH,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmBqF,UAAnB,CAAX;;AACA,UAAM2I,OAAO,GAAG,OAAOnR,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,IAAtD;;AAEA,UAAI,CAAC2C,IAAD,IAAS,eAAejC,IAAf,CAAoBV,MAApB,CAAb,EAA0C;AACxC;AACD;;AAED,UAAI,CAAC2C,IAAL,EAAW;AACTA,QAAAA,IAAI,GAAG,IAAI6nB,OAAJ,CAAY,IAAZ,EAAkBrZ,OAAlB,CAAP;AACAnO,QAAAA,IAAI,CAACC,OAAL,CAAa,IAAb,EAAmBuF,UAAnB,EAA6B7F,IAA7B;AACD;;AAED,UAAI,OAAO3C,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,YAAI,OAAO2C,IAAI,CAAC3C,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,gBAAM,IAAIwV,SAAJ,wBAAkCxV,MAAlC,QAAN;AACD;;AAED2C,QAAAA,IAAI,CAAC3C,MAAD,CAAJ;AACD;AACF,KApBM,CAAP;AAqBD;;;;AA1FD;wBAEqB;AACnB,aAAOgO,SAAP;AACD;;;wBAEiB;AAChB,aAAOrF,MAAP;AACD;;;wBAEqB;AACpB,aAAOH,UAAP;AACD;;;wBAEkB;AACjB,aAAOtJ,OAAP;AACD;;;wBAEsB;AACrB,aAAO0J,WAAP;AACD;;;wBAEwB;AACvB,aAAO2F,aAAP;AACD;;;;EAzBmByY;AA8FtB;AACA;AACA;AACA;AACA;AACA;;;AAEA/kB,kBAAkB,CAAC,YAAM;AACvB,MAAMoF,CAAC,GAAGxF,SAAS,EAAnB;AACA;;AACA,MAAIwF,CAAJ,EAAO;AACL,QAAMiD,kBAAkB,GAAGjD,CAAC,CAAClD,EAAF,CAAKwE,MAAL,CAA3B;AACAtB,IAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,IAAa6hB,OAAO,CAACtgB,eAArB;AACA7C,IAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,EAAW4B,WAAX,GAAyBigB,OAAzB;;AACAnjB,IAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,EAAW6B,UAAX,GAAwB,YAAM;AAC5BnD,MAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,IAAa2B,kBAAb;AACA,aAAOkgB,OAAO,CAACtgB,eAAf;AACD,KAHD;AAID;AACF,CAZiB,CAAlB;;AClJA;AACA;AACA;AACA;AACA;;AAEA,IAAMvB,MAAI,GAAG,WAAb;AACA,IAAMH,UAAQ,GAAG,cAAjB;AACA,IAAMI,WAAS,SAAOJ,UAAtB;AACA,IAAMK,cAAY,GAAG,WAArB;AAEA,IAAMmF,SAAO,GAAG;AACdlC,EAAAA,MAAM,EAAE,EADM;AAEd4e,EAAAA,MAAM,EAAE,MAFM;AAGd3lB,EAAAA,MAAM,EAAE;AAHM,CAAhB;AAMA,IAAMwJ,aAAW,GAAG;AAClBzC,EAAAA,MAAM,EAAE,QADU;AAElB4e,EAAAA,MAAM,EAAE,QAFU;AAGlB3lB,EAAAA,MAAM,EAAE;AAHU,CAApB;AAMA,IAAM4lB,cAAc,gBAAc/hB,WAAlC;AACA,IAAMgiB,YAAY,cAAYhiB,WAA9B;AACA,IAAM2G,qBAAmB,YAAU3G,WAAV,GAAsBC,cAA/C;AAEA,IAAMgiB,wBAAwB,GAAG,eAAjC;AACA,IAAMpgB,mBAAiB,GAAG,QAA1B;AAEA,IAAMqgB,iBAAiB,GAAG,wBAA1B;AACA,IAAMC,uBAAuB,GAAG,mBAAhC;AACA,IAAMC,kBAAkB,GAAG,WAA3B;AACA,IAAMC,kBAAkB,GAAG,WAA3B;AACA,IAAMC,mBAAmB,GAAG,kBAA5B;AACA,IAAMC,iBAAiB,GAAG,WAA1B;AACA,IAAMC,wBAAwB,GAAG,kBAAjC;AAEA,IAAMC,aAAa,GAAG,QAAtB;AACA,IAAMC,eAAe,GAAG,UAAxB;AAEA;AACA;AACA;AACA;AACA;;IAEMC;;;AACJ,qBAAYztB,OAAZ,EAAqBkC,MAArB,EAA6B;AAAA;;AAC3B,sCAAMlC,OAAN;AACA,UAAK0tB,cAAL,GAAsB1tB,OAAO,CAAC2V,OAAR,KAAoB,MAApB,GAA6BlV,MAA7B,GAAsCT,OAA5D;AACA,UAAKqT,OAAL,GAAe,MAAKC,UAAL,CAAgBpR,MAAhB,CAAf;AACA,UAAKgX,SAAL,GAAoB,MAAK7F,OAAL,CAAapM,MAAjC,SAA2CimB,kBAA3C,UAAkE,MAAK7Z,OAAL,CAAapM,MAA/E,SAAyFmmB,mBAAzF,UAAiH,MAAK/Z,OAAL,CAAapM,MAA9H,UAAyI8lB,wBAAzI;AACA,UAAKY,QAAL,GAAgB,EAAhB;AACA,UAAKC,QAAL,GAAgB,EAAhB;AACA,UAAKC,aAAL,GAAqB,IAArB;AACA,UAAKC,aAAL,GAAqB,CAArB;AAEApnB,IAAAA,YAAY,CAACmC,EAAb,CAAgB,MAAK6kB,cAArB,EAAqCZ,YAArC,EAAmD,UAAAvmB,KAAK;AAAA,aAAI,MAAKwnB,QAAL,CAAcxnB,KAAd,CAAJ;AAAA,KAAxD;;AAEA,UAAKynB,OAAL;;AACA,UAAKD,QAAL;;AAb2B;AAc5B;;;;;AAYD;SAEAC,UAAA,mBAAU;AAAA;;AACR,QAAMC,UAAU,GAAG,KAAKP,cAAL,KAAwB,KAAKA,cAAL,CAAoBjtB,MAA5C,GACjB8sB,aADiB,GAEjBC,eAFF;AAIA,QAAMU,YAAY,GAAG,KAAK7a,OAAL,CAAauZ,MAAb,KAAwB,MAAxB,GACnBqB,UADmB,GAEnB,KAAK5a,OAAL,CAAauZ,MAFf;AAIA,QAAMuB,UAAU,GAAGD,YAAY,KAAKV,eAAjB,GACjB,KAAKY,aAAL,EADiB,GAEjB,CAFF;AAIA,SAAKT,QAAL,GAAgB,EAAhB;AACA,SAAKC,QAAL,GAAgB,EAAhB;AACA,SAAKE,aAAL,GAAqB,KAAKO,gBAAL,EAArB;AAEA,QAAMC,OAAO,GAAG3f,cAAc,CAACE,IAAf,CAAoB,KAAKqK,SAAzB,CAAhB;AAEAoV,IAAAA,OAAO,CAACpC,GAAR,CAAY,UAAAlsB,OAAO,EAAI;AACrB,UAAMuuB,cAAc,GAAGluB,sBAAsB,CAACL,OAAD,CAA7C;AACA,UAAMiH,MAAM,GAAGsnB,cAAc,GAAG5f,cAAc,CAACM,OAAf,CAAuBsf,cAAvB,CAAH,GAA4C,IAAzE;;AAEA,UAAItnB,MAAJ,EAAY;AACV,YAAMunB,SAAS,GAAGvnB,MAAM,CAACiH,qBAAP,EAAlB;;AACA,YAAIsgB,SAAS,CAACnL,KAAV,IAAmBmL,SAAS,CAACC,MAAjC,EAAyC;AACvC,iBAAO,CACLphB,WAAW,CAAC6gB,YAAD,CAAX,CAA0BjnB,MAA1B,EAAkCkH,GAAlC,GAAwCggB,UADnC,EAELI,cAFK,CAAP;AAID;AACF;;AAED,aAAO,IAAP;AACD,KAfD,EAgBG3gB,MAhBH,CAgBU,UAAA8gB,IAAI;AAAA,aAAIA,IAAJ;AAAA,KAhBd,EAiBGC,IAjBH,CAiBQ,UAACvK,CAAD,EAAIE,CAAJ;AAAA,aAAUF,CAAC,CAAC,CAAD,CAAD,GAAOE,CAAC,CAAC,CAAD,CAAlB;AAAA,KAjBR,EAkBGhiB,OAlBH,CAkBW,UAAAosB,IAAI,EAAI;AACf,MAAA,MAAI,CAACf,QAAL,CAAcne,IAAd,CAAmBkf,IAAI,CAAC,CAAD,CAAvB;;AACA,MAAA,MAAI,CAACd,QAAL,CAAcpe,IAAd,CAAmBkf,IAAI,CAAC,CAAD,CAAvB;AACD,KArBH;AAsBD;;SAED/jB,UAAA,mBAAU;AACR,6BAAMA,OAAN;;AACAjE,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK+mB,cAAtB,EAAsC5iB,WAAtC;AAEA,SAAK4iB,cAAL,GAAsB,IAAtB;AACA,SAAKra,OAAL,GAAe,IAAf;AACA,SAAK6F,SAAL,GAAiB,IAAjB;AACA,SAAKyU,QAAL,GAAgB,IAAhB;AACA,SAAKC,QAAL,GAAgB,IAAhB;AACA,SAAKC,aAAL,GAAqB,IAArB;AACA,SAAKC,aAAL,GAAqB,IAArB;AACD;;;SAIDxa,aAAA,oBAAWpR,MAAX,EAAmB;AACjBA,IAAAA,MAAM,gBACDgO,SADC,EAEA,OAAOhO,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAFhD,CAAN;;AAKA,QAAI,OAAOA,MAAM,CAAC+E,MAAd,KAAyB,QAAzB,IAAqC5F,SAAS,CAACa,MAAM,CAAC+E,MAAR,CAAlD,EAAmE;AAAA,UAC3DvC,EAD2D,GACpDxC,MAAM,CAAC+E,MAD6C,CAC3DvC,EAD2D;;AAEjE,UAAI,CAACA,EAAL,EAAS;AACPA,QAAAA,EAAE,GAAGlF,MAAM,CAACqL,MAAD,CAAX;AACA3I,QAAAA,MAAM,CAAC+E,MAAP,CAAcvC,EAAd,GAAmBA,EAAnB;AACD;;AAEDxC,MAAAA,MAAM,CAAC+E,MAAP,SAAoBvC,EAApB;AACD;;AAED1C,IAAAA,eAAe,CAAC6I,MAAD,EAAO3I,MAAP,EAAeuO,aAAf,CAAf;AAEA,WAAOvO,MAAP;AACD;;SAEDksB,gBAAA,yBAAgB;AACd,WAAO,KAAKV,cAAL,KAAwBjtB,MAAxB,GACL,KAAKitB,cAAL,CAAoBkB,WADf,GAEL,KAAKlB,cAAL,CAAoBtf,SAFtB;AAGD;;SAEDigB,mBAAA,4BAAmB;AACjB,WAAO,KAAKX,cAAL,CAAoBxL,YAApB,IAAoCxiB,IAAI,CAACmvB,GAAL,CACzChvB,QAAQ,CAACoE,IAAT,CAAcie,YAD2B,EAEzCriB,QAAQ,CAAC0D,eAAT,CAAyB2e,YAFgB,CAA3C;AAID;;SAED4M,mBAAA,4BAAmB;AACjB,WAAO,KAAKpB,cAAL,KAAwBjtB,MAAxB,GACLA,MAAM,CAACsuB,WADF,GAEL,KAAKrB,cAAL,CAAoBxf,qBAApB,GAA4CugB,MAF9C;AAGD;;SAEDV,WAAA,oBAAW;AACT,QAAM3f,SAAS,GAAG,KAAKggB,aAAL,KAAuB,KAAK/a,OAAL,CAAarF,MAAtD;;AACA,QAAMkU,YAAY,GAAG,KAAKmM,gBAAL,EAArB;;AACA,QAAMW,SAAS,GAAG,KAAK3b,OAAL,CAAarF,MAAb,GAAsBkU,YAAtB,GAAqC,KAAK4M,gBAAL,EAAvD;;AAEA,QAAI,KAAKhB,aAAL,KAAuB5L,YAA3B,EAAyC;AACvC,WAAK8L,OAAL;AACD;;AAED,QAAI5f,SAAS,IAAI4gB,SAAjB,EAA4B;AAC1B,UAAM/nB,MAAM,GAAG,KAAK2mB,QAAL,CAAc,KAAKA,QAAL,CAAczmB,MAAd,GAAuB,CAArC,CAAf;;AAEA,UAAI,KAAK0mB,aAAL,KAAuB5mB,MAA3B,EAAmC;AACjC,aAAKgoB,SAAL,CAAehoB,MAAf;AACD;;AAED;AACD;;AAED,QAAI,KAAK4mB,aAAL,IAAsBzf,SAAS,GAAG,KAAKuf,QAAL,CAAc,CAAd,CAAlC,IAAsD,KAAKA,QAAL,CAAc,CAAd,IAAmB,CAA7E,EAAgF;AAC9E,WAAKE,aAAL,GAAqB,IAArB;;AACA,WAAKqB,MAAL;;AACA;AACD;;AAED,SAAK,IAAIhoB,CAAC,GAAG,KAAKymB,QAAL,CAAcxmB,MAA3B,EAAmCD,CAAC,EAApC,GAAyC;AACvC,UAAMioB,cAAc,GAAG,KAAKtB,aAAL,KAAuB,KAAKD,QAAL,CAAc1mB,CAAd,CAAvB,IACnBkH,SAAS,IAAI,KAAKuf,QAAL,CAAczmB,CAAd,CADM,KAElB,OAAO,KAAKymB,QAAL,CAAczmB,CAAC,GAAG,CAAlB,CAAP,KAAgC,WAAhC,IAA+CkH,SAAS,GAAG,KAAKuf,QAAL,CAAczmB,CAAC,GAAG,CAAlB,CAFzC,CAAvB;;AAIA,UAAIioB,cAAJ,EAAoB;AAClB,aAAKF,SAAL,CAAe,KAAKrB,QAAL,CAAc1mB,CAAd,CAAf;AACD;AACF;AACF;;SAED+nB,YAAA,mBAAUhoB,MAAV,EAAkB;AAChB,SAAK4mB,aAAL,GAAqB5mB,MAArB;;AAEA,SAAKioB,MAAL;;AAEA,QAAME,OAAO,GAAG,KAAKlW,SAAL,CAAejY,KAAf,CAAqB,GAArB,EACbirB,GADa,CACT,UAAAjsB,QAAQ;AAAA,aAAOA,QAAP,0BAAmCgH,MAAnC,YAA+ChH,QAA/C,gBAAiEgH,MAAjE;AAAA,KADC,CAAhB;;AAGA,QAAMooB,IAAI,GAAG1gB,cAAc,CAACM,OAAf,CAAuBmgB,OAAO,CAACE,IAAR,CAAa,GAAb,CAAvB,CAAb;;AAEA,QAAID,IAAI,CAACtjB,SAAL,CAAeE,QAAf,CAAwB8gB,wBAAxB,CAAJ,EAAuD;AACrDpe,MAAAA,cAAc,CAACM,OAAf,CAAuBqe,wBAAvB,EAAiD+B,IAAI,CAACvjB,OAAL,CAAauhB,iBAAb,CAAjD,EACGthB,SADH,CACa2J,GADb,CACiB/I,mBADjB;AAGA0iB,MAAAA,IAAI,CAACtjB,SAAL,CAAe2J,GAAf,CAAmB/I,mBAAnB;AACD,KALD,MAKO;AACL;AACA0iB,MAAAA,IAAI,CAACtjB,SAAL,CAAe2J,GAAf,CAAmB/I,mBAAnB;AAEAgC,MAAAA,cAAc,CAACS,OAAf,CAAuBigB,IAAvB,EAA6BpC,uBAA7B,EACG3qB,OADH,CACW,UAAAitB,SAAS,EAAI;AACpB;AACA;AACA5gB,QAAAA,cAAc,CAACc,IAAf,CAAoB8f,SAApB,EAAkCrC,kBAAlC,UAAyDE,mBAAzD,EACG9qB,OADH,CACW,UAAAosB,IAAI;AAAA,iBAAIA,IAAI,CAAC3iB,SAAL,CAAe2J,GAAf,CAAmB/I,mBAAnB,CAAJ;AAAA,SADf,EAHoB;;AAOpBgC,QAAAA,cAAc,CAACc,IAAf,CAAoB8f,SAApB,EAA+BpC,kBAA/B,EACG7qB,OADH,CACW,UAAAktB,OAAO,EAAI;AAClB7gB,UAAAA,cAAc,CAACO,QAAf,CAAwBsgB,OAAxB,EAAiCtC,kBAAjC,EACG5qB,OADH,CACW,UAAAosB,IAAI;AAAA,mBAAIA,IAAI,CAAC3iB,SAAL,CAAe2J,GAAf,CAAmB/I,mBAAnB,CAAJ;AAAA,WADf;AAED,SAJH;AAKD,OAbH;AAcD;;AAEDjG,IAAAA,YAAY,CAAC2C,OAAb,CAAqB,KAAKqkB,cAA1B,EAA0Cb,cAA1C,EAA0D;AACxDvW,MAAAA,aAAa,EAAErP;AADyC,KAA1D;AAGD;;SAEDioB,SAAA,kBAAS;AACPvgB,IAAAA,cAAc,CAACE,IAAf,CAAoB,KAAKqK,SAAzB,EACGtL,MADH,CACU,UAAA6hB,IAAI;AAAA,aAAIA,IAAI,CAAC1jB,SAAL,CAAeE,QAAf,CAAwBU,mBAAxB,CAAJ;AAAA,KADd,EAEGrK,OAFH,CAEW,UAAAmtB,IAAI;AAAA,aAAIA,IAAI,CAAC1jB,SAAL,CAAeC,MAAf,CAAsBW,mBAAtB,CAAJ;AAAA,KAFf;AAGD;;;YAIMP,kBAAP,yBAAuBlK,MAAvB,EAA+B;AAC7B,WAAO,KAAKmK,IAAL,CAAU,YAAY;AAC3B,UAAIxH,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmBqF,UAAnB,CAAX;;AACA,UAAM2I,OAAO,GAAG,OAAOnR,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C;;AAEA,UAAI,CAAC2C,IAAL,EAAW;AACTA,QAAAA,IAAI,GAAG,IAAI4oB,SAAJ,CAAc,IAAd,EAAoBpa,OAApB,CAAP;AACD;;AAED,UAAI,OAAOnR,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,YAAI,OAAO2C,IAAI,CAAC3C,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,gBAAM,IAAIwV,SAAJ,wBAAkCxV,MAAlC,QAAN;AACD;;AAED2C,QAAAA,IAAI,CAAC3C,MAAD,CAAJ;AACD;AACF,KAfM,CAAP;AAgBD;;;;wBAjNoB;AACnB,aAAOgO,SAAP;AACD;;;wBAEqB;AACpB,aAAOxF,UAAP;AACD;;;;EAzBqBH;AAuOxB;AACA;AACA;AACA;AACA;;;AAEA7D,YAAY,CAACmC,EAAb,CAAgBpI,MAAhB,EAAwBgR,qBAAxB,EAA6C,YAAM;AACjD9C,EAAAA,cAAc,CAACE,IAAf,CAAoBme,iBAApB,EACG1qB,OADH,CACW,UAAAotB,GAAG;AAAA,WAAI,IAAIjC,SAAJ,CAAciC,GAAd,EAAmBriB,WAAW,CAACI,iBAAZ,CAA8BiiB,GAA9B,CAAnB,CAAJ;AAAA,GADd;AAED,CAHD;AAKA;AACA;AACA;AACA;AACA;AACA;;AAEAvrB,kBAAkB,CAAC,YAAM;AACvB,MAAMoF,CAAC,GAAGxF,SAAS,EAAnB;AACA;;AACA,MAAIwF,CAAJ,EAAO;AACL,QAAMiD,kBAAkB,GAAGjD,CAAC,CAAClD,EAAF,CAAKwE,MAAL,CAA3B;AACAtB,IAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,IAAa4iB,SAAS,CAACrhB,eAAvB;AACA7C,IAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,EAAW4B,WAAX,GAAyBghB,SAAzB;;AACAlkB,IAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,EAAW6B,UAAX,GAAwB,YAAM;AAC5BnD,MAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,IAAa2B,kBAAb;AACA,aAAOihB,SAAS,CAACrhB,eAAjB;AACD,KAHD;AAID;AACF,CAZiB,CAAlB;;ACxSA;AACA;AACA;AACA;AACA;;AAEA,IAAMvB,MAAI,GAAG,KAAb;AACA,IAAMH,UAAQ,GAAG,QAAjB;AACA,IAAMI,WAAS,SAAOJ,UAAtB;AACA,IAAMK,cAAY,GAAG,WAArB;AAEA,IAAMmN,YAAU,YAAUpN,WAA1B;AACA,IAAMqN,cAAY,cAAYrN,WAA9B;AACA,IAAMkN,YAAU,YAAUlN,WAA1B;AACA,IAAMmN,aAAW,aAAWnN,WAA5B;AACA,IAAMK,sBAAoB,aAAWL,WAAX,GAAuBC,cAAjD;AAEA,IAAM4kB,wBAAwB,GAAG,eAAjC;AACA,IAAMhjB,mBAAiB,GAAG,QAA1B;AACA,IAAM4O,qBAAmB,GAAG,UAA5B;AACA,IAAMmE,iBAAe,GAAG,MAAxB;AACA,IAAMtH,iBAAe,GAAG,MAAxB;AAEA,IAAMiV,mBAAiB,GAAG,WAA1B;AACA,IAAMJ,yBAAuB,GAAG,mBAAhC;AACA,IAAMhb,iBAAe,GAAG,SAAxB;AACA,IAAM2d,kBAAkB,GAAG,uBAA3B;AACA,IAAMhjB,sBAAoB,GAAG,0EAA7B;AACA,IAAM0gB,0BAAwB,GAAG,kBAAjC;AACA,IAAMuC,8BAA8B,GAAG,iCAAvC;AAEA;AACA;AACA;AACA;AACA;;IAEMC;;;;;;;;;AAOJ;SAEAvW,OAAA,gBAAO;AAAA;;AACL,QAAK,KAAK/O,QAAL,CAAcvH,UAAd,IACH,KAAKuH,QAAL,CAAcvH,UAAd,CAAyB3B,QAAzB,KAAsCgO,IAAI,CAACC,YADxC,IAEH,KAAK/E,QAAL,CAAcuB,SAAd,CAAwBE,QAAxB,CAAiCU,mBAAjC,CAFE,IAGF,KAAKnC,QAAL,CAAcuB,SAAd,CAAwBE,QAAxB,CAAiCsP,qBAAjC,CAHF,EAGyD;AACvD;AACD;;AAED,QAAI7L,QAAJ;AACA,QAAMzI,MAAM,GAAG1G,sBAAsB,CAAC,KAAKiK,QAAN,CAArC;;AACA,QAAMulB,WAAW,GAAG,KAAKvlB,QAAL,CAAcsB,OAAd,CAAsBmhB,yBAAtB,CAApB;;AAEA,QAAI8C,WAAJ,EAAiB;AACf,UAAMC,YAAY,GAAGD,WAAW,CAAChM,QAAZ,KAAyB,IAAzB,IAAiCgM,WAAW,CAAChM,QAAZ,KAAyB,IAA1D,GAAiE6L,kBAAjE,GAAsF3d,iBAA3G;AACAvC,MAAAA,QAAQ,GAAGf,cAAc,CAACE,IAAf,CAAoBmhB,YAApB,EAAkCD,WAAlC,CAAX;AACArgB,MAAAA,QAAQ,GAAGA,QAAQ,CAACA,QAAQ,CAACvI,MAAT,GAAkB,CAAnB,CAAnB;AACD;;AAED,QAAIsW,SAAS,GAAG,IAAhB;;AAEA,QAAI/N,QAAJ,EAAc;AACZ+N,MAAAA,SAAS,GAAG/W,YAAY,CAAC2C,OAAb,CAAqBqG,QAArB,EAA+BwI,YAA/B,EAA2C;AACrD5B,QAAAA,aAAa,EAAE,KAAK9L;AADiC,OAA3C,CAAZ;AAGD;;AAED,QAAM4S,SAAS,GAAG1W,YAAY,CAAC2C,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCwN,YAApC,EAAgD;AAChE1B,MAAAA,aAAa,EAAE5G;AADiD,KAAhD,CAAlB;;AAIA,QAAI0N,SAAS,CAACzT,gBAAV,IAA+B8T,SAAS,KAAK,IAAd,IAAsBA,SAAS,CAAC9T,gBAAnE,EAAsF;AACpF;AACD;;AAED,SAAKslB,SAAL,CAAe,KAAKzkB,QAApB,EAA8BulB,WAA9B;;AAEA,QAAM7V,QAAQ,GAAG,SAAXA,QAAW,GAAM;AACrBxT,MAAAA,YAAY,CAAC2C,OAAb,CAAqBqG,QAArB,EAA+ByI,cAA/B,EAA6C;AAC3C7B,QAAAA,aAAa,EAAE,KAAI,CAAC9L;AADuB,OAA7C;AAGA9D,MAAAA,YAAY,CAAC2C,OAAb,CAAqB,KAAI,CAACmB,QAA1B,EAAoCyN,aAApC,EAAiD;AAC/C3B,QAAAA,aAAa,EAAE5G;AADgC,OAAjD;AAGD,KAPD;;AASA,QAAIzI,MAAJ,EAAY;AACV,WAAKgoB,SAAL,CAAehoB,MAAf,EAAuBA,MAAM,CAAChE,UAA9B,EAA0CiX,QAA1C;AACD,KAFD,MAEO;AACLA,MAAAA,QAAQ;AACT;AACF;;;SAID+U,YAAA,mBAAUjvB,OAAV,EAAmB0Z,SAAnB,EAA8BtV,QAA9B,EAAwC;AAAA;;AACtC,QAAM6rB,cAAc,GAAGvW,SAAS,KAAKA,SAAS,CAACqK,QAAV,KAAuB,IAAvB,IAA+BrK,SAAS,CAACqK,QAAV,KAAuB,IAA3D,CAAT,GACrBpV,cAAc,CAACE,IAAf,CAAoB+gB,kBAApB,EAAwClW,SAAxC,CADqB,GAErB/K,cAAc,CAACO,QAAf,CAAwBwK,SAAxB,EAAmCzH,iBAAnC,CAFF;AAIA,QAAMie,MAAM,GAAGD,cAAc,CAAC,CAAD,CAA7B;AACA,QAAM3V,eAAe,GAAGlW,QAAQ,IAAK8rB,MAAM,IAAIA,MAAM,CAACnkB,SAAP,CAAiBE,QAAjB,CAA0ByT,iBAA1B,CAA/C;;AAEA,QAAMxF,QAAQ,GAAG,SAAXA,QAAW;AAAA,aAAM,MAAI,CAACiW,mBAAL,CAAyBnwB,OAAzB,EAAkCkwB,MAAlC,EAA0C9rB,QAA1C,CAAN;AAAA,KAAjB;;AAEA,QAAI8rB,MAAM,IAAI5V,eAAd,EAA+B;AAC7B,UAAM3Z,kBAAkB,GAAGH,gCAAgC,CAAC0vB,MAAD,CAA3D;AACAA,MAAAA,MAAM,CAACnkB,SAAP,CAAiBC,MAAjB,CAAwBoM,iBAAxB;AAEA1R,MAAAA,YAAY,CAACoC,GAAb,CAAiBonB,MAAjB,EAAyBlxB,cAAzB,EAAyCkb,QAAzC;AACA3Y,MAAAA,oBAAoB,CAAC2uB,MAAD,EAASvvB,kBAAT,CAApB;AACD,KAND,MAMO;AACLuZ,MAAAA,QAAQ;AACT;AACF;;SAEDiW,sBAAA,6BAAoBnwB,OAApB,EAA6BkwB,MAA7B,EAAqC9rB,QAArC,EAA+C;AAC7C,QAAI8rB,MAAJ,EAAY;AACVA,MAAAA,MAAM,CAACnkB,SAAP,CAAiBC,MAAjB,CAAwBW,mBAAxB;AAEA,UAAMyjB,aAAa,GAAGzhB,cAAc,CAACM,OAAf,CAAuB4gB,8BAAvB,EAAuDK,MAAM,CAACjtB,UAA9D,CAAtB;;AAEA,UAAImtB,aAAJ,EAAmB;AACjBA,QAAAA,aAAa,CAACrkB,SAAd,CAAwBC,MAAxB,CAA+BW,mBAA/B;AACD;;AAED,UAAIujB,MAAM,CAAChwB,YAAP,CAAoB,MAApB,MAAgC,KAApC,EAA2C;AACzCgwB,QAAAA,MAAM,CAACnjB,YAAP,CAAoB,eAApB,EAAqC,KAArC;AACD;AACF;;AAED/M,IAAAA,OAAO,CAAC+L,SAAR,CAAkB2J,GAAlB,CAAsB/I,mBAAtB;;AACA,QAAI3M,OAAO,CAACE,YAAR,CAAqB,MAArB,MAAiC,KAArC,EAA4C;AAC1CF,MAAAA,OAAO,CAAC+M,YAAR,CAAqB,eAArB,EAAsC,IAAtC;AACD;;AAEDlJ,IAAAA,MAAM,CAAC7D,OAAD,CAAN;;AAEA,QAAIA,OAAO,CAAC+L,SAAR,CAAkBE,QAAlB,CAA2ByT,iBAA3B,CAAJ,EAAiD;AAC/C1f,MAAAA,OAAO,CAAC+L,SAAR,CAAkB2J,GAAlB,CAAsB0C,iBAAtB;AACD;;AAED,QAAIpY,OAAO,CAACiD,UAAR,IAAsBjD,OAAO,CAACiD,UAAR,CAAmB8I,SAAnB,CAA6BE,QAA7B,CAAsC0jB,wBAAtC,CAA1B,EAA2F;AACzF,UAAMU,eAAe,GAAGrwB,OAAO,CAAC8L,OAAR,CAAgBuhB,mBAAhB,CAAxB;;AAEA,UAAIgD,eAAJ,EAAqB;AACnB1hB,QAAAA,cAAc,CAACE,IAAf,CAAoBye,0BAApB,EACGhrB,OADH,CACW,UAAAguB,QAAQ;AAAA,iBAAIA,QAAQ,CAACvkB,SAAT,CAAmB2J,GAAnB,CAAuB/I,mBAAvB,CAAJ;AAAA,SADnB;AAED;;AAED3M,MAAAA,OAAO,CAAC+M,YAAR,CAAqB,eAArB,EAAsC,IAAtC;AACD;;AAED,QAAI3I,QAAJ,EAAc;AACZA,MAAAA,QAAQ;AACT;AACF;;;MAIMgI,kBAAP,yBAAuBlK,MAAvB,EAA+B;AAC7B,WAAO,KAAKmK,IAAL,CAAU,YAAY;AAC3B,UAAMxH,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmBqF,UAAnB,KAAgC,IAAIolB,GAAJ,CAAQ,IAAR,CAA7C;;AAEA,UAAI,OAAO5tB,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,YAAI,OAAO2C,IAAI,CAAC3C,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,gBAAM,IAAIwV,SAAJ,wBAAkCxV,MAAlC,QAAN;AACD;;AAED2C,QAAAA,IAAI,CAAC3C,MAAD,CAAJ;AACD;AACF,KAVM,CAAP;AAWD;;;;AA3ID;wBAEsB;AACpB,aAAOwI,UAAP;AACD;;;;EALeH;AA+IlB;AACA;AACA;AACA;AACA;;;AAEA7D,YAAY,CAACmC,EAAb,CAAgBhJ,QAAhB,EAA0BsL,sBAA1B,EAAgDyB,sBAAhD,EAAsE,UAAUrG,KAAV,EAAiB;AACrFA,EAAAA,KAAK,CAAC8D,cAAN;AAEA,MAAMxF,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmBqF,UAAnB,KAAgC,IAAIolB,GAAJ,CAAQ,IAAR,CAA7C;AACAjrB,EAAAA,IAAI,CAAC0U,IAAL;AACD,CALD;AAOA;AACA;AACA;AACA;AACA;AACA;;AAEApV,kBAAkB,CAAC,YAAM;AACvB,MAAMoF,CAAC,GAAGxF,SAAS,EAAnB;AACA;;AACA,MAAIwF,CAAJ,EAAO;AACL,QAAMiD,kBAAkB,GAAGjD,CAAC,CAAClD,EAAF,CAAKwE,MAAL,CAA3B;AACAtB,IAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,IAAailB,GAAG,CAAC1jB,eAAjB;AACA7C,IAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,EAAW4B,WAAX,GAAyBqjB,GAAzB;;AACAvmB,IAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,EAAW6B,UAAX,GAAwB,YAAM;AAC5BnD,MAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,IAAa2B,kBAAb;AACA,aAAOsjB,GAAG,CAAC1jB,eAAX;AACD,KAHD;AAID;AACF,CAZiB,CAAlB;;ACxMA;AACA;AACA;AACA;AACA;;AAEA,IAAMvB,MAAI,GAAG,OAAb;AACA,IAAMH,UAAQ,GAAG,UAAjB;AACA,IAAMI,WAAS,SAAOJ,UAAtB;AAEA,IAAMyU,qBAAmB,qBAAmBrU,WAA5C;AACA,IAAMoN,YAAU,YAAUpN,WAA1B;AACA,IAAMqN,cAAY,cAAYrN,WAA9B;AACA,IAAMkN,YAAU,YAAUlN,WAA1B;AACA,IAAMmN,aAAW,aAAWnN,WAA5B;AAEA,IAAM4U,iBAAe,GAAG,MAAxB;AACA,IAAM6Q,eAAe,GAAG,MAAxB;AACA,IAAMnY,iBAAe,GAAG,MAAxB;AACA,IAAMoY,kBAAkB,GAAG,SAA3B;AAEA,IAAM/f,aAAW,GAAG;AAClByW,EAAAA,SAAS,EAAE,SADO;AAElBuJ,EAAAA,QAAQ,EAAE,SAFQ;AAGlBpJ,EAAAA,KAAK,EAAE;AAHW,CAApB;AAMA,IAAMnX,SAAO,GAAG;AACdgX,EAAAA,SAAS,EAAE,IADG;AAEduJ,EAAAA,QAAQ,EAAE,IAFI;AAGdpJ,EAAAA,KAAK,EAAE;AAHO,CAAhB;AAMA,IAAMvH,uBAAqB,GAAG,2BAA9B;AAEA;AACA;AACA;AACA;AACA;;IAEM4Q;;;AACJ,iBAAY1wB,OAAZ,EAAqBkC,MAArB,EAA6B;AAAA;;AAC3B,sCAAMlC,OAAN;AAEA,UAAKqT,OAAL,GAAe,MAAKC,UAAL,CAAgBpR,MAAhB,CAAf;AACA,UAAKknB,QAAL,GAAgB,IAAhB;;AACA,UAAKI,aAAL;;AAL2B;AAM5B;;;;;AAgBD;SAEAjQ,OAAA,gBAAO;AAAA;;AACL,QAAM6D,SAAS,GAAG1W,YAAY,CAAC2C,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCwN,YAApC,CAAlB;;AAEA,QAAIoF,SAAS,CAACzT,gBAAd,EAAgC;AAC9B;AACD;;AAED,SAAKgnB,aAAL;;AAEA,QAAI,KAAKtd,OAAL,CAAa6T,SAAjB,EAA4B;AAC1B,WAAK1c,QAAL,CAAcuB,SAAd,CAAwB2J,GAAxB,CAA4BgK,iBAA5B;AACD;;AAED,QAAMxF,QAAQ,GAAG,SAAXA,QAAW,GAAM;AACrB,MAAA,MAAI,CAAC1P,QAAL,CAAcuB,SAAd,CAAwBC,MAAxB,CAA+BwkB,kBAA/B;;AACA,MAAA,MAAI,CAAChmB,QAAL,CAAcuB,SAAd,CAAwB2J,GAAxB,CAA4B0C,iBAA5B;;AAEA1R,MAAAA,YAAY,CAAC2C,OAAb,CAAqB,MAAI,CAACmB,QAA1B,EAAoCyN,aAApC;;AAEA,UAAI,MAAI,CAAC5E,OAAL,CAAaod,QAAjB,EAA2B;AACzB,QAAA,MAAI,CAACrH,QAAL,GAAgBrnB,UAAU,CAAC,YAAM;AAC/B,UAAA,MAAI,CAACuX,IAAL;AACD,SAFyB,EAEvB,MAAI,CAACjG,OAAL,CAAagU,KAFU,CAA1B;AAGD;AACF,KAXD;;AAaA,SAAK7c,QAAL,CAAcuB,SAAd,CAAwBC,MAAxB,CAA+BukB,eAA/B;;AACA1sB,IAAAA,MAAM,CAAC,KAAK2G,QAAN,CAAN;;AACA,SAAKA,QAAL,CAAcuB,SAAd,CAAwB2J,GAAxB,CAA4B8a,kBAA5B;;AACA,QAAI,KAAKnd,OAAL,CAAa6T,SAAjB,EAA4B;AAC1B,UAAMvmB,kBAAkB,GAAGH,gCAAgC,CAAC,KAAKgK,QAAN,CAA3D;AAEA9D,MAAAA,YAAY,CAACoC,GAAb,CAAiB,KAAK0B,QAAtB,EAAgCxL,cAAhC,EAAgDkb,QAAhD;AACA3Y,MAAAA,oBAAoB,CAAC,KAAKiJ,QAAN,EAAgB7J,kBAAhB,CAApB;AACD,KALD,MAKO;AACLuZ,MAAAA,QAAQ;AACT;AACF;;SAEDZ,OAAA,gBAAO;AAAA;;AACL,QAAI,CAAC,KAAK9O,QAAL,CAAcuB,SAAd,CAAwBE,QAAxB,CAAiCmM,iBAAjC,CAAL,EAAwD;AACtD;AACD;;AAED,QAAMqF,SAAS,GAAG/W,YAAY,CAAC2C,OAAb,CAAqB,KAAKmB,QAA1B,EAAoC0N,YAApC,CAAlB;;AAEA,QAAIuF,SAAS,CAAC9T,gBAAd,EAAgC;AAC9B;AACD;;AAED,QAAMuQ,QAAQ,GAAG,SAAXA,QAAW,GAAM;AACrB,MAAA,MAAI,CAAC1P,QAAL,CAAcuB,SAAd,CAAwB2J,GAAxB,CAA4B6a,eAA5B;;AACA7pB,MAAAA,YAAY,CAAC2C,OAAb,CAAqB,MAAI,CAACmB,QAA1B,EAAoC2N,cAApC;AACD,KAHD;;AAKA,SAAK3N,QAAL,CAAcuB,SAAd,CAAwBC,MAAxB,CAA+BoM,iBAA/B;;AACA,QAAI,KAAK/E,OAAL,CAAa6T,SAAjB,EAA4B;AAC1B,UAAMvmB,kBAAkB,GAAGH,gCAAgC,CAAC,KAAKgK,QAAN,CAA3D;AAEA9D,MAAAA,YAAY,CAACoC,GAAb,CAAiB,KAAK0B,QAAtB,EAAgCxL,cAAhC,EAAgDkb,QAAhD;AACA3Y,MAAAA,oBAAoB,CAAC,KAAKiJ,QAAN,EAAgB7J,kBAAhB,CAApB;AACD,KALD,MAKO;AACLuZ,MAAAA,QAAQ;AACT;AACF;;SAEDvP,UAAA,mBAAU;AACR,SAAKgmB,aAAL;;AAEA,QAAI,KAAKnmB,QAAL,CAAcuB,SAAd,CAAwBE,QAAxB,CAAiCmM,iBAAjC,CAAJ,EAAuD;AACrD,WAAK5N,QAAL,CAAcuB,SAAd,CAAwBC,MAAxB,CAA+BoM,iBAA/B;AACD;;AAED1R,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK6D,QAAtB,EAAgC2U,qBAAhC;;AAEA,6BAAMxU,OAAN;;AACA,SAAK0I,OAAL,GAAe,IAAf;AACD;;;SAIDC,aAAA,oBAAWpR,MAAX,EAAmB;AACjBA,IAAAA,MAAM,gBACDgO,SADC,EAED7C,WAAW,CAACI,iBAAZ,CAA8B,KAAKjD,QAAnC,CAFC,EAGA,OAAOtI,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAHhD,CAAN;AAMAF,IAAAA,eAAe,CAAC6I,MAAD,EAAO3I,MAAP,EAAe,KAAKuI,WAAL,CAAiBgG,WAAhC,CAAf;AAEA,WAAOvO,MAAP;AACD;;SAEDsnB,gBAAA,yBAAgB;AAAA;;AACd9iB,IAAAA,YAAY,CAACmC,EAAb,CAAgB,KAAK2B,QAArB,EAA+B2U,qBAA/B,EAAoDW,uBAApD,EAA2E;AAAA,aAAM,MAAI,CAACxG,IAAL,EAAN;AAAA,KAA3E;AACD;;SAEDqX,gBAAA,yBAAgB;AACdpb,IAAAA,YAAY,CAAC,KAAK6T,QAAN,CAAZ;AACA,SAAKA,QAAL,GAAgB,IAAhB;AACD;;;QAIMhd,kBAAP,yBAAuBlK,MAAvB,EAA+B;AAC7B,WAAO,KAAKmK,IAAL,CAAU,YAAY;AAC3B,UAAIxH,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmBqF,UAAnB,CAAX;;AACA,UAAM2I,OAAO,GAAG,OAAOnR,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C;;AAEA,UAAI,CAAC2C,IAAL,EAAW;AACTA,QAAAA,IAAI,GAAG,IAAI6rB,KAAJ,CAAU,IAAV,EAAgBrd,OAAhB,CAAP;AACD;;AAED,UAAI,OAAOnR,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,YAAI,OAAO2C,IAAI,CAAC3C,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,gBAAM,IAAIwV,SAAJ,wBAAkCxV,MAAlC,QAAN;AACD;;AAED2C,QAAAA,IAAI,CAAC3C,MAAD,CAAJ,CAAa,IAAb;AACD;AACF,KAfM,CAAP;AAgBD;;;;wBAvIwB;AACvB,aAAOuO,aAAP;AACD;;;wBAEoB;AACnB,aAAOP,SAAP;AACD;;;wBAEqB;AACpB,aAAOxF,UAAP;AACD;;;;EArBiBH;AAqJpB;AACA;AACA;AACA;AACA;AACA;;;AAEApG,kBAAkB,CAAC,YAAM;AACvB,MAAMoF,CAAC,GAAGxF,SAAS,EAAnB;AACA;;AACA,MAAIwF,CAAJ,EAAO;AACL,QAAMiD,kBAAkB,GAAGjD,CAAC,CAAClD,EAAF,CAAKwE,MAAL,CAA3B;AACAtB,IAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,IAAa6lB,KAAK,CAACtkB,eAAnB;AACA7C,IAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,EAAW4B,WAAX,GAAyBikB,KAAzB;;AACAnnB,IAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,EAAW6B,UAAX,GAAwB,YAAM;AAC5BnD,MAAAA,CAAC,CAAClD,EAAF,CAAKwE,MAAL,IAAa2B,kBAAb;AACA,aAAOkkB,KAAK,CAACtkB,eAAb;AACD,KAHD;AAID;AACF,CAZiB,CAAlB;;;;"}