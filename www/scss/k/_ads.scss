.adsbygoogle {
    display: block;
    margin: auto;
    margin-bottom: 1rem;
}

@media (max-width: 767px) {
    .hide-xs { display: none !important; }
    .srec-xs { width:250px; height:250px; }
    .mrec-xs { width:300px; height:250px; }
    .lrec-xs { width:336px; height:280px; }
    .leaderboard-xs { width:728px; height:90px; }
    .mobile-xs { width:320px; height:50px; }
    .skyscraper-xs { width:120px; height:600px; }
    .halfpage-xs { width:300px; height:600px; }
    .banner-xs { width:468px; height:60px; }
}

/* small */
@media (min-width: 768px) and (max-width: 991px) {
    .hide-sm { display: none; }
    .srec-sm { width:250px; height:250px; }
    .mrec-sm { width:300px; height:250px; }
    .lrec-sm { width:336px; height:280px; }
    .leaderboard-sm { width:728px; height:90px; }
    .mobile-sm { width:320px; height:50px; }
    .skyscraper-sm { width:120px; height:600px; }
    .halfpage-sm { width:300px; height:600px; }
    .banner-sm { width:468px; height:60px; }
}

/* medium */
@media (min-width: 992px) and (max-width: 1199px) {
    .hide-md { display: none; }
    .srec-md { width:250px; height:250px; }
    .mrec-md { width:300px; height:250px; }
    .lrec-md { width:336px; height:280px; }
    .leaderboard-md { width:728px; height:90px; }
    .mobile-md { width:320px; height:50px; }
    .skyscraper-md { width:120px; height:600px; }
    .halfpage-md { width:300px; height:600px; }
    .banner-md { width:468px; height:60px; }
}

/* large */
@media (min-width: 1200px) {
    .hide-lg { display: none; }
    .srec-lg { width:250px; height:250px; }
    .mrec-lg { width:300px; height:250px; }
    .lrec-lg { width:336px; height:280px; }
    .leaderboard-lg { width:728px; height:90px; }
    .mobile-lg { width:320px; height:50px; }
    .skyscraper-lg { width:120px; height:600px; }
    .halfpage-lg { width:300px; height:600px; }
    .banner-lg { width:468px; height:60px; }
}
