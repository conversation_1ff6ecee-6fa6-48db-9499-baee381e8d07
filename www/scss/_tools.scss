// Text ellipsis
// --------------------------
@mixin ellipsis($width) {
	width: $width;
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
}

// Media query mixin ===========================================================

@mixin breakpoint($query) {
	@media (min-width: $query) {
		@content;
	}
}

// Background image mixin =====================================================

@mixin background(
	$img-name,
	$size,
	$position: 0 0,
	$img-dir: $img-dir,
	$repeat: no-repeat
) {
	background: {
		image: url($img-dir+$img-name);
		size: $size;
		position: $position;
		repeat: $repeat;
	}
}

// --------------------------
// Font weight
// --------------------------

@mixin light() {
	font-weight: 300;
}

@mixin regular() {
	font-weight: 400;
}

@mixin semibold() {
	font-weight: 600;
}

@mixin bold() {
	font-weight: 700;
}

@mixin black() {
	font-weight: 900;
}
