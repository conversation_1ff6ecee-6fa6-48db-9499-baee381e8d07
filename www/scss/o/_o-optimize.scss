@use "tools";
@use "vars";

.o-optimize-0 .leaflet-preview img,
.o-optimize-1 .leaflet-preview img,
.o-optimize-2 .leaflet-preview img {
    max-width: 90%;    
    margin: auto;

    @include tools.breakpoint(vars.$desktop) {
        max-width: 645px;
    }
}





.o-optimize-2 .button-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.o-optimize-2 .button-wrapper picture {
    opacity: 0.4;
}

.o-optimize-2 .button {
    position: absolute;    
    background-color: #2bb673;
    border-color: #2bb673;
    color: #fff;
    font-size: 22px;
    font-weight: 700;
    width: auto;
    height: auto;
    padding: 2rem;
    margin: 1rem;
}

.o-optimize-0 .k-paginator__button {
    background-color: #2bb673;
    border-color: #2bb673;
    color: #fff;
    font-size: 16px;
    font-weight: 700;
    width: auto;
    height: auto;
    padding: 1rem;
    margin: 1rem;
}

.o-optimize-1 .k-paginator__button {
    background-color: transparent;
    border-color: #2bb673;
    border: 1px solid;
    color: #2bb673;
    font-size: 16px;
    font-weight: 700;
    width: auto;
    height: auto;
    padding: 1rem;
    margin: 1rem;
}

.o-optimize-2 .k-paginator__button {    
    background-color: transparent;
    //border: 0;
    color: #2bb673;
    font-size: 16px;
    font-weight: 700;
    width: auto;
    height: auto;
    padding: 1rem;
    margin: 1rem;
    //text-decoration: underline;
}