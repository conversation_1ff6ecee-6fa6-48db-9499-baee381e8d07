#!/bin/bash

# Define translations for each language
declare -A translations=(
  ["en_US"]="Current %brand% weekly ad"
  ["en_GB"]="Current %brand% leaflet"
  ["en_CA"]="Current %brand% flyer"
  ["en_AU"]="Current %brand% catalogue"
  ["en_NZ"]="Current %brand% catalogue"
  ["en_ZA"]="Current %brand% special"
  ["en_AE"]="Current %brand% leaflet"
  ["en_TR"]="Current %brand% leaflet"
  ["en_CL"]="Current %brand% leaflet"
  ["en_IE"]="Current %brand% leaflet"
  ["de_AT"]="Aktueller %brand% Prospekt"
  ["de_CH"]="Aktueller %brand% Prospekt"
  ["de_DE"]="Aktueller %brand% Prospekt"
  ["es_ES"]="Catálogo actual de %brand%"
  ["es_AR"]="Catálogo actual de %brand%"
  ["es_MX"]="Catálogo actual de %brand%"
  ["es_PE"]="Catálogo actual de %brand%"
  ["es_CO"]="Catálogo actual de %brand%"
  ["fr_FR"]="Catalogue actuel %brand%"
  ["fr_TN"]="Catalogue actuel %brand%"
  ["fr_MA"]="Catalogue actuel %brand%"
  ["it_IT"]="Volantino attuale %brand%"
  ["nl_NL"]="Huidige %brand% folder"
  ["nl_BE"]="Huidige %brand% folder"
  ["pl_PL"]="Aktualny katalog %brand%"
  ["pt_PT"]="Folheto atual %brand%"
  ["pt_BR"]="Folheto atual %brand%"
  ["ro_RO"]="Catalog actual %brand%"
  ["ro_MD"]="Catalog actual %brand%"
  ["da_DK"]="Aktuel %brand% tilbudsavis"
  ["fi_FI"]="Nykyinen %brand% tarjouslehti"
  ["sv_SE"]="Aktuell %brand% reklamblad"
  ["no_NO"]="Gjeldende %brand% kundeavis"
  ["hu_HU"]="Aktuális %brand% szórólap"
  ["bg_BG"]="Текуща брошура на %brand%"
  ["el_GR"]="Τρέχον φυλλάδιο %brand%"
  ["el_CY"]="Τρέχον φυλλάδιο %brand%"
  ["et_EE"]="Praegune %brand% infoleht"
  ["hr_HR"]="Trenutni %brand% letak"
  ["hr_BA"]="Trenutni %brand% letak"
  ["lv_LV"]="Pašreizējā %brand% skrejlapa"
  ["lt_LT"]="Dabartinis %brand% lankstinukas"
  ["sk_SK"]="Aktuálny %brand% leták"
  ["sl_SI"]="Trenutni %brand% letak"
  ["sr_RS"]="Trenutni %brand% letak"
  ["tr_TR"]="Güncel %brand% broşürü"
  ["ja_JP"]="現在の%brand%チラシ"
  ["uk_UA"]="Поточний %brand% буклет"
)

# Directory containing the language files
locale_dir="app/locale"

# Process each language file
for lang_file in "$locale_dir"/kaufino.*.neon; do
  # Extract language code from filename
  lang_code=$(basename "$lang_file" | sed -E 's/kaufino\.([a-z]{2}_[A-Z]{2})\.neon/\1/')
  
  # Skip Czech file as it already has the translation
  if [ "$lang_code" = "cs_CZ" ]; then
    echo "Skipping Czech file as it already has the translation"
    continue
  fi
  
  # Get translation for this language
  translation="${translations[$lang_code]}"
  
  # If no specific translation is available, use English as fallback
  if [ -z "$translation" ]; then
    translation="Current %brand% leaflet"
    echo "No specific translation for $lang_code, using fallback"
  fi
  
  echo "Processing $lang_file with translation: $translation"
  
  # Check if tag.offers section exists and if currentLeaflet is already there
  if grep -q "tag:" "$lang_file" && grep -q "offers:" "$lang_file" && ! grep -q "currentLeaflet:" "$lang_file"; then
    # Find the end of the tag.offers section
    line_num=$(grep -n "offers:" "$lang_file" | cut -d: -f1)
    if [ -n "$line_num" ]; then
      # Find the next section after tag.offers
      next_section=$(tail -n +$line_num "$lang_file" | grep -n "^[a-z]" | head -1 | cut -d: -f1)
      if [ -n "$next_section" ]; then
        insert_line=$((line_num + next_section - 2))
        
        # Check if the last line of the offers section has a translation
        last_line=$(sed -n "$insert_line p" "$lang_file")
        if [[ "$last_line" == *":"* && ! "$last_line" == *"currentLeaflet:"* ]]; then
          # Add the translation
          sed -i '' "${insert_line}s/$/\n\t\t\tcurrentLeaflet: $translation/" "$lang_file"
          echo "Added translation to $lang_file"
        fi
      fi
    fi
  else
    echo "Either tag.offers section doesn't exist or currentLeaflet is already present in $lang_file"
  fi
done

echo "Translation update completed!"
