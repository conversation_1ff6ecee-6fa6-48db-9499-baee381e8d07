<div class="k-leaflets__item mb-5">
    <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" target="_blank" class="k-leaflets__link {if $leaflet->isExpired()}expired{/if} mb-3">
        {dump $leaflet->getId()}
        <picture n:if="$leaflet->getFirstPage()">
            <source data-srcset="{$leaflet->getFirstPage()->getImageUrl() |image:230,288,'exactTop','webp'}" type="image/webp">
            <img src="{$basePath}/images/placeholder-230x288.png" data-src="{$leaflet->getFirstPage()->getImageUrl() |image:230,288,'exactTop'}" width="230" height="288" alt="{$leaflet->getName()}" class="k-leaflets__image lazyload">
        </picture>
    </a>
    <div class="k-leaflets__title mt-0 mb-0">
        <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" target="_blank" class="color-black">
            {$leaflet->getName()}
        </a>
    </div>
    <p class="k-leaflets__date mt-0 mb-0" n:if="$leaflet->isChecked()">{$leaflet->getValidSince()|localDate} – {$leaflet->getValidTill()|localDate:'long'}</p>
</div>