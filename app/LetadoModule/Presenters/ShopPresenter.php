<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\LetadoModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Leaflets\Entities\Leaflet;
use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Offers\Entities\Offer;
use <PERSON><PERSON><PERSON>\Model\Offers\OfferFacade;
use <PERSON><PERSON><PERSON>\Model\Shops\ContentBlockFacade;
use Ka<PERSON>ino\Model\Shops\Entities\ContentBlock;
use Ka<PERSON>ino\Model\Shops\Entities\Shop;
use Kaufino\Model\Shops\ShopFacade;
use Ka<PERSON>ino\Model\Websites\Entities\Website;
use Tracy\Debugger;

final class ShopPresenter extends BasePresenter
{
	/** @var LeafletFacade @inject */
	public $leafletFacade;

	/** @var OfferFacade @inject */
	public $offerFacade;

	/** @var ShopFacade @inject */
	public $shopFacade;

	/** @var ContentBlockFacade @inject */
	public $contentBlockFacade;

	public function actionShop(Shop $shop): void
	{
		if ($shop->isActiveLetado() === false && !$this->getUser()->isLoggedIn()) {
			$this->redirectPermanent(':Letado:Homepage:default');
		}

		$this->responseCacheTags[] = 'shop/' . $shop->getId();

		if ($shop->isEshop()) {
			$this->setView('shopEshop');
		}

		$leaflets = $this->leafletFacade->findLeafletsByShop($shop, 60, true, true);

		$contentBlocks = $this->contentBlockFacade->findContentBlocksByShop($shop, Website::MODULE_LETADO);
		$this->template->contentBlocks = $contentBlocks;

		$length = 0;
		/** @var ContentBlock $contentBlock */
		foreach ($contentBlocks as $contentBlock) {
			if ($contentBlock->getType() === 'legacy') {
				continue;
			}

			$length += $contentBlock->getContent() ? strlen($contentBlock->getContent()) : 0;

			if ($length >= 50) {
				break;
			}
		}

		$contentBlocksAllowed = $length > 50;
		$this->template->contentBlocksAllowed = $contentBlocksAllowed;

		$leafletsInTop = [];
		$leafletsInBottom = [];

		if ($contentBlocksAllowed === true) {
			/** @var Leaflet $leaflet */
			foreach ($leaflets as $leaflet) {
				if ($leaflet->isExpired()) {
					$leafletsInBottom[] = $leaflet;
				} else {
					$leafletsInTop[] = $leaflet;
				}
			}

			if (empty($leafletsInTop) && !empty($leafletsInBottom)) {
				$leafletsInTop = array_slice($leafletsInBottom, 0, 1);
				unset($leafletsInBottom[0]);
			}

			$countOfLeafletsInTop = count($leafletsInTop);

			if ($countOfLeafletsInTop > 0) {
				if ($countOfLeafletsInTop < 3) {
					$countOfRequiredLeaflets = 3 - count($leafletsInTop);
					$leafletsInTop = array_merge($leafletsInTop, array_slice($leafletsInBottom, 0, $countOfRequiredLeaflets));
				} else if ($countOfLeafletsInTop > 3) {
					$countOfLeafletsInTop = $countOfLeafletsInTop - 3;
					$rows = (int) ceil($countOfLeafletsInTop / 5);

					$countOfRequiredLeaflets = ($rows * 5) - $countOfLeafletsInTop;

					$leafletsInTop = array_merge($leafletsInTop, array_slice($leafletsInBottom, 0, $countOfRequiredLeaflets));
				}

				$leafletsInBottom = array_slice($leafletsInBottom, $countOfRequiredLeaflets ?? 0);
			}
		}


		$this->template->leafletsInTop = $leafletsInTop ?: array_slice($leaflets, 0, 40);
		$this->template->leafletsInBottom = $leafletsInBottom;

		$this->template->shop = $shop;
		$this->template->leaflets = $this->leafletFacade->findLeafletsByShop($shop, 40, false, true);
		$this->template->offers = $this->offerFacade->findOffersByShop($shop, 20, true, Offer::TYPE_LEAFLET);
		$this->template->coupons = $this->offerFacade->findOffersByShop($shop, 20, false);
		$this->template->faqContentBlocks = $this->contentBlockFacade->findFaqContentBlocks($shop, Website::MODULE_LETADO);

		if ($shop->getTag()) {
			$this->template->similarShops = $this->shopFacade->findLeafletShopsByTag($shop->getTag(), true, 10, Website::MODULE_LETADO);
		} else {
			$this->template->similarShops = $this->shopFacade->findTopLeafletShops($shop->getLocalization(), true, 10, Website::MODULE_LETADO);
		}

		if ($couponId = $this->getRequest()->getParameter('oid')) {
			$offer = $this->offerFacade->findOffer($couponId);

			if ($offer->isCoupon()) {
				$popupCoupon = $offer;
			}
		}

		$this->template->popupCoupon = isset($popupCoupon) ? $popupCoupon : null;

		$this->template->heading1 = $this->seoGenerator->generateShopHeading1($shop, $this->website);
		$this->template->metaTitle = $this->seoGenerator->generateShopMetaTitle($shop, $this->website);
		$this->template->metaDescription = $this->seoGenerator->generateShopMetaDescription($shop, $this->website);
	}
}
