<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\LetadoModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Shops\ShopFacade;
use <PERSON><PERSON><PERSON>\Model\Websites\Entities\Website;

final class SearchPresenter extends BasePresenter
{
	/** @var ShopFacade @inject */
	public $shopFacade;

	/** @var LeafletFacade @inject */
	public $leafletFacade;

	public function actionSearch(?string $q = null): void
	{
        $this->disableCachedResponse = true;

		$this->template->query = $q;
		$this->template->shops = $this->shopFacade->findLeafletShopsByFulltext($this->localization, $q, true, 10, Website::MODULE_LETADO);
		$this->template->leafletPages = $this->leafletFacade->findLeafletPagesByFulltext($q, $this->localization, $this->website->getModule());
	}
}
