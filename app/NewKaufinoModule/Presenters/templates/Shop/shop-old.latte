{var $currentLeaflet = $shop->getCurrentLeaflet()}
{var $nextLeaflet = $shop->getNextLeaflet()}

{var $parameters = [
'currentLeafletFromDate' => $currentLeaflet ? $currentLeaflet->getValidSince() : null,
'currentLeafletToDate' => $currentLeaflet ? $currentLeaflet->getValidTill() : null,
'nextLeafletFromDate' => $nextLeaflet ? $nextLeaflet->getValidSince() : null,
'shopName' => $shop->getName(),
]}

{block head}
{include parent}
<script n:syntax="double">
    window.dataLayer.push({
        'content_group' : 'Shop',
        'country' : {{$localization->getRegion()}}
    });
</script>
{/block}

{block title}{if $pageExtension && $pageExtension->getTitle()}{$seoGenerator->renderInSandbox($pageExtension->getTitle(), $parameters)}{else}{$metaTitle}{/if}{/block}
{block description}{if $pageExtension && $pageExtension->getDescription()}{$seoGenerator->renderInSandbox($pageExtension->getDescription(), $parameters)}{else}{$metaDescription}{/if}{/block}

{block breadcrumb}
<div class="k-breadcrumb__container mt-4">
	<p class="k-breadcrumb">
		<a n:href="Leaflets:leaflets" class="link">{_kaufino.navbar.leaflets}</a> |
		<a n:href="Tag:tag $shop->getTag()" class="link" n:if="$shop->getTag() !== null">{$shop->getTag()->getName()}</a>
	</p>
</div>
{/block}

{block scripts}
{include parent}

<script n:if="$faqContentBlocks" type="application/ld+json">
	{
		"@context": "https://schema.org",
		"@type": "FAQPage",
		"mainEntity": [
			{foreach $faqContentBlocks as $faq} {
				"@type": "Question",
				"name": {$faq->getHeading()},
				"acceptedAnswer": {
					"@type": "Answer",
					"text": {strip_tags($faq->getContent())}
				}
			}{sep},{/sep}
			{/foreach}
		]
	}
</script>
{/block}

{define cities}
<div n:if="$shop->isStore() && count($cities) > 0">
	<h2 class="fz-xl fw-regular mb-5 px-3 px-lg-0">
		{_kaufino.shop.city, [brand => $shop->getName()]}
	</h2>

	<p class="k-tag mb-1">
		{foreach $cities as $city}
		<span class="k-tag__inner {$iterator->counter > 6 ? 'hidden' : ''}">
                    <a n:href="City:shop $city, $shop" class="k-tag__item">{$shop->getName()} {$city->getName()}</a>
                </span>
		{/foreach}
	</p>

	<p n:if="$shop->isStore() && count($cities) > 5" class="d-flex mt-3 mb-5">
		<button class="link ml-auto k-show-more-button js-show-tag js-show-all-btn">{_'kaufino.showMore.cities'} »</button>
		<a n:href="Cities:cities" class="link ml-auto hidden k-show-more-button js-all-btn">{_'kaufino.showMore.allCities'} »</a>
	</p>
</div>
{/define}

{block content}
{dump $articles}
<div class="leaflet k-lf-layout k-lf-layout--fixed-container">
	<div class="container">
		<div class="leaflet__content">
			<div class="w100">

				<div class="k-profile-header k-profile-header--sm-center">
					<div class="k-profile-header__logo-wrapper k-profile-header__logo-wrapper--smaller">
						<picture>
							<source
											srcset="
                                    {$shop->getLogoUrl() |image:80,80,'fit','webp'} 1x,
                                    {$shop->getLogoUrl() |image:160,160,'fit','webp'} 2x
                                "
											type="image/webp"
							>
							<img
											src="{$basePath}/images/placeholder-80x70.png"
											srcset="
                                    {$shop->getLogoUrl() |image:80,80} 1x,
                                    {$shop->getLogoUrl() |image:160,160} 2x
                                "
											width="80"
											height="80"
											alt="{$shop->getName()}"
											class="k-profile-header__logo"
							>
						</picture>
					</div>

					<div class="k-profile-header__content">
						<h1 class="k-profile-header__title">
							{if $pageExtension && $pageExtension->getHeading()}
							{var $heading = $getHeadingFromPageExtension($pageExtension)}
							{$heading}
							{else}
							{_kaufino.shop.title, [brand => $shop->getName()]|noescape}
							{/if}
						</h1>
					</div>
				</div>

				{* Letáky *}
				{if count($leafletsInTop) > 0}
				<div class="k-leaflets__wrapper mt-3">
					{foreach $leafletsInTop as $leaflet}

					{if false && $iterator->counter == 1}
					<div class="k-leaflets__item k-leaflets__item--first mb-3">
						<!-- Vypis brandu - Responsive - 2 -->
						<ins class="adsbygoogle adslot-1" style="display:block" data-ad-client="ca-pub-4233432057183172" data-ad-slot="7326232159" data-ad-format="auto" data-full-width-responsive="true"></ins>

						<script>
                (adsbygoogle = window.adsbygoogle || []).push({});
						</script>
					</div>
					{/if}

					{if $leaflet->isExpired() === false && $iterator->counter == 1}
					<div class="k-leaflets__large-wrapper mb-3 mb-sm-0" n:attr="data-brochure-id: $leaflet->getOfferistaBrochureId()">
						<div class="k-leaflets__large">
							{if $user->isLoggedIn()}
							<div class="k-leaflets__bubble">
								{if $leaflet->hasOfferistaId()}<span class="k-leaflets__bubble-top">Offerista: {$leaflet->getOfferistaBrochureId()}</span>{/if}
								{if $leaflet->isTop()}<span class="k-leaflets__bubble-top">TOP</span>{/if}
								{if $leaflet->isPrimary()}<span class="k-leaflets__bubble-primary">Primary</span>{/if}
								<small>{$leaflet->getPriority()}</small>
							</div>
							{/if}
							<a n:href="Leaflet:leaflet $shop, $leaflet" class="k-leaflets__large-thumbnail">
								<div class="img">
									<picture>
										<source
														srcset="
                                                            {$leaflet->getFirstPage()->getImageUrl() |image:242,336,'exactTop','webp'} 1x,
                                                            {$leaflet->getFirstPage()->getImageUrl() |image:484,672,'exactTop','webp'} 2x
                                                        "
														type="image/webp"
														importance="high"
										>
										<img
														src="{$leaflet->getFirstPage()->getImageUrl() |image:242,336,'exactTop'}"
														srcset="
                                                            {$leaflet->getFirstPage()->getImageUrl() |image:242,336,'exactTop'} 1x,
                                                            {$leaflet->getFirstPage()->getImageUrl() |image:484,672,'exactTop'} 2x
                                                        "
														width="242"
														height="336"
														alt="{if strtolower($leaflet->getName()) === strtolower($shop->getName())}{_'kaufino.leaflet.brandLeafletFrom', [brand => $shop->getName()]} {_'app.day.' . $leaflet->getValidSince()->format('w') . '.genitive'} {$leaflet->getValidSince()|localDate:'short'}{else}{$leaflet->getName()}{/if}"
														importance="high"
										>
									</picture>

								</div>
								<div class="img" n:if="$secondPage = $leaflet->getPageByNumber(2)">
									<picture>
										<source
														srcset="
                                                            {$secondPage->getImageUrl() |image:242,336,'exactTop','webp'} 1x,
                                                            {$secondPage->getImageUrl() |image:484,672,'exactTop','webp'} 2x
                                                        "
														type="image/webp"
														importance="high"
										>
										<img
														src="{$secondPage->getImageUrl() |image:242,336,'exactTop'}"
														srcset="
                                                            {$secondPage->getImageUrl() |image:242,336,'exactTop'} 1x,
                                                            {$secondPage->getImageUrl() |image:484,672,'exactTop'} 2x
                                                        "
														width="242"
														height="336"
														alt="{if strtolower($leaflet->getName()) === strtolower($shop->getName())}{_'kaufino.leaflet.brandLeafletFrom', [brand => $shop->getName()]} {_'app.day.' . $leaflet->getValidSince()->format('w') . '.genitive'} {$leaflet->getValidSince()|localDate:'short'}{else}{$leaflet->getName()}{/if}"
														importance="high"
										>
									</picture>
								</div>
								<img class="chevron-right" src="{$basePath}/images/icons/chevron_right.svg" alt="">
							</a>
							<span n:if="$leaflet->isValid()" class="k-leaflets__corner"><span>{_'kaufino.leaflet.valid'}</span></span>
							<div class="k-leaflets__large-detail">
								<div class="logo">
									<img src="{$shop->getLogoUrl() |image:80,80}" alt="logo" loading="lazy">
									<div class="k-leaflets__large-detail-title">
										<a n:href="Leaflet:leaflet $shop, $leaflet">
											{if $leaflet->isChecked() === false}
											{_kaufino.leaflet.titleUnChecked, [brand => $leaflet->getName()]|noescape}
											{else}
											{$leaflet->getName()}
											{/if}
										</a>
										<span class="note">
                                                        <p class="k-leaflets__date mt-0 mb-0" n:if="$leaflet->isChecked()">{if $localization->isHungarian()}{$leaflet->getValidSince()|localDate:'long'} – {$leaflet->getValidTill()|localDate}{else}{$leaflet->getValidSince()|localDate} – {$leaflet->getValidTill()|localDate:'long'}{/if}</p>
                                                    </span>
									</div>
								</div>

								<div class="mt-3 mt-md-0 ml-md-auto">
									<a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" class="k-leaflets__button ">{_oferto.shop.showLeaflet}</a>
								</div>

							</div>
						</div>
					</div>

					{continueIf $iterator->counter == 1}
					{/if}

					{include '../components/leaflet.latte', leaflet: $leaflet, validBadgeShow: true, buttonShow: true, offeristaId: $leaflet->getOfferistaBrochureId()}
					{/foreach}
				</div>
				{elseif (!$shop->isEshop())}
				<div class="alert alert-info mx-3">{_kaufino.shop.noLeaflets}</div>
				{/if}

				{* Kupony *}
				{if $shop->isEshop() && !$shop->isActive()}
				{foreach $coupons as $coupon}
				<div class="k-coupon">
					<div class="k-coupon__box {if false}k-coupon__box--sale{/if}">
						<strong class="k-coupon__box-value">{$coupon->getDiscountAmount()}{if $coupon->getDiscountType() == relative}%{/if}</strong>
						<small class="k-coupon__box-type">{_kaufino.coupon.type.sale}</small>
					</div>

					<div class="k-coupon__content">
						<small class="d-block mb-2">{_kaufino.coupon.valid}: {$coupon->getValidTill()|localDate:'long'}</small>
						<h3 class="mt-0 mb-2"><a href="{$presenter->link('Shop:shop', ['shop' => $coupon->getShop(), 'oid' => $coupon->getId()])}" target="_blank" data-popup-link="{$presenter->link('Exit:offer', $coupon)}" class="color-black td-hover-underline">{$coupon->getShop()->getName()}: {$coupon->getName()}</a></h3>
						{$coupon->getDescription()|noescape}
					</div>

					<a href="{$presenter->link('Shop:shop', ['shop' => $coupon->getShop(), 'oid' => $coupon->getId()])}" target="_blank" data-popup-link="{$presenter->link('Exit:offer', $coupon)}" class="k-coupon__button k-coupon__button--code js-click-link">
						<span class="k-coupon__button-label">{_kaufino.coupon.showCode}</span>
						<span class="k-coupon__button-code">{$coupon->getCode()}</span>
					</a>
				</div>
				{/foreach}
				{/if}

				<!-- Vypis brandu - Responsive - 1 -->
				<ins class="adsbygoogle" style="display:block" data-ad-client="ca-pub-4233432057183172" data-ad-slot="9065144021" data-ad-format="auto" data-full-width-responsive="true"></ins>

				<script>
            (adsbygoogle = window.adsbygoogle || []).push({});
				</script>

				<div n:if="count($offers) > 0">
					<h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_kaufino.shop.offers, [brand => $shop->getName()]}</h2>

					<div class="k-offers k-offers--4 mb-3">
						{foreach $offers as $offer}
						{continueIf !$offer->getLeafletPage()}
						{include '../components/offer-item.latte', offer => $offer, hideShop => true, cssClass => $iterator->counter > 4 ? 'hidden' : ''}
						{/foreach}
					</div>

					<p n:if="count($offers) > 3" class="d-flex mt-3 mb-5">
						<button class="link ml-auto k-show-more-button js-show-offer">{_'kaufino.showMore.offers'} »</button>
					</p>
				</div>

				{include cities}

				<p class="k-profile-header__text mw-800 mt-5 ml-0">
					{if $pageExtension && $pageExtension->getShortDescription()}
					{$pageExtension->getShortDescription()}
					{else}
					{*_kaufino.shop.text, [brand => $shop->getName()]|noescape*}
					{/if}
				</p>

				{if $contentBlocksAllowed}
				<div class="k-tabs k-content">
					<div class="k-tabs__buttons">
						<div class="k-tabs__tabLink {first}active{/first}" data-tab="tab{$iterator->counter}" n:foreach="$contentBlocks as $type => $contentBlock">
							{continueIf $contentBlock === null}

							<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24"><path d="M160-160q-33 0-56.5-23.5T80-240v-480q0-33 23.5-56.5T160-800h640q33 0 56.5 23.5T880-720v480q0 33-23.5 56.5T800-160H160Zm0-80h280v-480H160v480Zm360 0h280v-480H520v480Zm-320-80h200v-80H200v80Zm0-120h200v-80H200v80Zm0-120h200v-80H200v80Zm360 240h200v-80H560v80Zm0-120h200v-80H560v80Zm0-120h200v-80H560v80ZM440-240v-480 480Z"/></svg>
							{_'kaufino.tabs.' . $type}
						</div>
					</div>

					<div id="tab{$iterator->counter}" class="k-tabs_tab {first}active{/first}" n:foreach="$contentBlocks as $contentBlock">
						{continueIf $contentBlock === null}

						<h2 n:if="$contentBlock->getHeading()">{$contentBlock->getHeading()}</h2>

						{$contentBlock->getContent() |content|noescape}
					</div>
				</div>
				{else}
				<div class="k-content">
					{if $pageExtension && $pageExtension->getLongDescription()}
					{$pageExtension->getLongDescription() |content|noescape}
					{else}
					{cache md5($shop->getDescription()), expire => '20 minutes'}
					{$shop->getDescription()|content|noescape}
					{/cache}
					{/if}
				</div>
				{/if}

				{var $faqContentBlocks = $getFaqContentBlocks()}

				{if $faqContentBlocks}
				<div class="faq-section mb-5">
					{* <div class=faq-section-title>Frequetly asked questions about Checkers</div> *}

					<div class="faq-question" n:foreach="$faqContentBlocks as $contentBlock">
						<div class="faq-title">
							<div>{$contentBlock->getHeading()}</div>
							<svg class="arrow" width="22" height="12" viewBox="0 0 22 12" xmlns="http://www.w3.org/2000/svg">
								<path d="M10.9823 11.5239C10.8079 11.5239 10.6374 11.4905 10.4708 11.4239C10.3041 11.3572 10.1502 11.2534 10.0092 11.1124L0.916901 2.02001C0.778434 1.88155 0.711135 1.6963 0.715001 1.46426C0.718835 1.2322 0.789985 1.04693 0.928452 0.908462C1.10795 0.728995 1.2932 0.645029 1.4842 0.656562C1.67523 0.668096 1.85665 0.755913 2.02845 0.920013L10.9823 9.87386L19.9362 0.920013C20.0746 0.781546 20.2535 0.702062 20.4727 0.681562C20.6919 0.661062 20.8836 0.740546 21.0477 0.920013C21.2272 1.08411 21.3047 1.26745 21.2804 1.47001C21.256 1.67258 21.1746 1.85976 21.0362 2.03156L11.9554 11.1124C11.8144 11.2534 11.6669 11.3572 11.5131 11.4239C11.3592 11.4905 11.1823 11.5239 10.9823 11.5239Z"/>
							</svg>
						</div>
						<div class="faq-answer">
							{$contentBlock->getContent() |content|noescape}
						</div>
					</div>
				</div>
				{/if}

				<div n:if="count($similarShops)-1 > 0" class="">
					<h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_kaufino.shop.otherShops}</h2>
					<div class="k-shop">
						{foreach $similarShops as $similarShop}
						{continueIf $similarShop->getId() == $shop->getId()}
						{include '../components/shop-logo.latte', shop => $similarShop, cssClass => $iterator->counter > 13 ? 'hidden' : ''}
						{/foreach}
					</div>

					<p n:if="count($similarShops) > 12" class="d-flex mt-3 mb-5">
						<button class="link ml-auto k-show-more-button js-show-shop">{_'kaufino.showMore.shops'} »</button>
					</p>
				</div>

				{* Letáky *}
				{if count($topLeafletsBySimilarShops) > 0}
				<h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_kaufino.showMore.leaflets}</h2>
				<div class="k-leaflets__wrapper mt-3 mb-5">
					{foreach $topLeafletsBySimilarShops as $leaflet}
					{include '../components/leaflet.latte', leaflet: $leaflet, offeristaId: $leaflet->getOfferistaBrochureId()}
					{/foreach}
				</div>
				{/if}

				<div n:if="$shopInternationalVariants">
					<h2 class="fz-xl fw-regular mb-5 px-3 px-lg-0">
						{_kaufino.shop.internationalVariants, [brand => $shop->getName()]}
					</h2>

					<p class="k-tag mb-5">
						{foreach $shopInternationalVariants as $shopInternationalVariant}
						{var $shopInternationalVariantLocalization = $shopInternationalVariant->getLocalization()}

						<span class="">
                                <a n:href="//Shop:shop $shopInternationalVariant, region => $shopInternationalVariantLocalization->getRegion()" class="k-tag__item k-tag__item--no-border" title="{$shopInternationalVariantLocalization->getOriginalName()}">
                                    <img src="{$basePath}/images/circle-flags/{$shopInternationalVariantLocalization->getRegion()}.svg" alt="{$shopInternationalVariantLocalization->getOriginalName()}" width="48" height="48" loading="lazy">
                                </a>
                            </span>
						{/foreach}
					</p>
				</div>


				{if $popupCoupon}
				{include popup.latte, coupon => $popupCoupon}
				{/if}

				{* Pobočky *}
				{*
				<div class="row">
					<div class="col-6 col-sm-3">
						<h3>Prodejny Praha</h3>
						<ul>
							<li><a href="" class="color-grey td-underline td-hover-none">Prodejna Praha, Na Poříčí 1068/23</a></li>
							<li><a href="" class="color-grey td-underline td-hover-none">Prodejna Praha, Na Poříčí 1068/23</a></li>
							<li><a href="" class="color-grey td-underline td-hover-none">Prodejna Praha, Na Poříčí 1068/23</a></li>
						</ul>
						<p><a href="" class="color-grey td-underline td-hover-none">Zobrazit další prodejny</a></p>
					</div>

					<div class="col-6 col-sm-3">
						<h3>Prodejny Brno</h3>
						<ul>
							<li><a href="" class="color-grey td-underline td-hover-none">Prodejna Brno, Na Poříčí 1068/23</a></li>
							<li><a href="" class="color-grey td-underline td-hover-none">Prodejna Brno, Na Poříčí 1068/23</a></li>
							<li><a href="" class="color-grey td-underline td-hover-none">Prodejna Brno, Na Poříčí 1068/23</a></li>
						</ul>
						<p><a href="" class="color-grey td-underline td-hover-none">Zobrazit další prodejny</a></p>
					</div>

					<div class="col-6 col-sm-3">
						<h3>Prodejny Ostrava</h3>
						<ul>
							<li><a href="" class="color-grey td-underline td-hover-none">Prodejna Ostrava, Na Poříčí 1068/23</a></li>
							<li><a href="" class="color-grey td-underline td-hover-none">Prodejna Ostrava, Na Poříčí 1068/23</a></li>
							<li><a href="" class="color-grey td-underline td-hover-none">Prodejna Ostrava, Na Poříčí 1068/23</a></li>
						</ul>
						<p><a href="" class="color-grey td-underline td-hover-none">Zobrazit další prodejny</a></p>
					</div>
				</div>
				*}
			</div>
		</div>

	</div>

	<div class="float-wrapper__stop"></div>
</div>

<div class="container lg:p-0">
	<div class="mt-3 hidden md:flex flex-wrap gap-3 lg:gap-0 lg:justify-between mb-10">
		<div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Hypermarkety a supermarkety</div>
		<div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Elektro</div>
		<div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Nábytek</div>
		<div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Sport</div>
		<div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Bydlení a zahrada</div>
		<div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Drogerie a kosmetika</div>
		<div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Lékárny a zdraví</div>
		<div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Ostatní</div>
	</div>

	<div class="flex mt-5 items-center gap-[15px] mb-5 md:mb-[32px]">
		<img class="w-[60px] h-[60px] rounded-lg" src="https://k.klmcdn.com/k/upload/shops/logo/5ae2/80x80/fit/lidl-909.webp?v=13.1&from=png" alt="obchod">
		<div>
			<div class="text-[26px] md:text-[33px] leading-[39px] font-medium mb-1.5 md:h-[36px]">Lidl leták</div>
			<div class="hidden md:flex gap-[9px] items-center text-sm font-light text-[#646C7C]">
				<svg xmlns="http://www.w3.org/2000/svg" width="14" height="13" viewBox="0 0 14 13" fill="none">
					<path d="M2.6 7.54682V12H5.8V8.76132C5.8 8.54654 5.88429 8.34062 6.0343 8.18878C6.18432 8.03694 6.38784 7.95165 6.6 7.95165H7.4C7.61216 7.95165 7.81563 8.03694 7.96571 8.18878C8.11574 8.34062 8.2 8.54654 8.2 8.76132V12H11.4V7.54682M1 6.73715L6.43414 1.23733C6.50843 1.16209 6.59664 1.1024 6.69371 1.06168C6.79083 1.02096 6.89488 1 7 1C7.10512 1 7.20918 1.02096 7.30624 1.06168C7.40336 1.1024 7.49158 1.16209 7.56587 1.23733L13 6.73715" stroke="#80899C" stroke-linecap="round" stroke-linejoin="round"/>
				</svg>
				<svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
					<path d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z" fill="#646C7C"/>
				</svg>
				Letáky
			</div>
		</div>
	</div>

	<div class="grid grid-cols-2 md:grid-cols-5 gap-3">
		<div class="col-span-2 p-1.5 md:p-2 bg-green-light rounded-xl">
			<div class="flex mb-[13px] mb-[17px] relative" style="position: relative;">
				<div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
				<div class="w-full relative">
					<img class="w-full max-h-[297.66px] border rounded-l-lg" src="https://k.klmcdn.com/s/files/leaflets//212/212271/a8e8/242x336/exactTop/28b7b836215400d9.xqibjp8z23la.webp?v=13.1" alt="">
				</div>
				<div class="w-full relative">
					<img class="w-full max-h-[297.66px] border rounded-r-lg" src="https://k.klmcdn.com/s/files/leaflets//212/212271/2a49/242x336/exactTop/df9ab6d0d9ca5627.umxi4p82x5a8.webp?v=13.1" alt="">
				</div>
				<div class="flex gap-1 absolute bottom-1 right-1 z-10">
					<button class="rounded-md text-xs md:text-sm font-medium leading-[24.5px] text-white bg-green py-[3px] px-[7px] md:py-1 md:px-2.5">Aktuální leták</button>
					<button class="rounded-md flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] py-[3px] px-[7px] md:py-1 md:px-2.5 gap-2">
						Otevřít
						<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
							<path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
							<path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
						</svg>
					</button>
				</div>
			</div>
			<div class="flex items-center gap-[15px] pl-2 pb-[9px]">
				<img class="w-[36px] h-[36px] rounded-md" src="https://k.klmcdn.com/k/upload/shops/logo/5ae2/80x80/fit/lidl-909.webp?v=13.1&from=png" alt="obchod">
				<div>
					<div class="text-sm md:text-lg font-medium">Lidl</div>
					<div class="text-xs font-light">04.04. - 07.04.2024</div>
				</div>
			</div>
		</div>

		<div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
			<div class="p-1.5 md:p-2 bg-orange-light rounded-xl">
				<div class="flex mb-[13px] md:mb-[17px] relative" style="position: relative;">
					<div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
					<div class="w-full relative">
						<img class="rounded-lg w-full max-h-[297.66px]" src="https://k.klmcdn.com/s/files/leaflets//212/212271/a8e8/242x336/exactTop/28b7b836215400d9.xqibjp8z23la.webp?v=13.1" alt="letak">
					</div>
					<div class="flex flex-col lg:flex-row gap-1 absolute bottom-1 right-1 z-10">
						<button class="rounded-md text-xs md:text-sm font-medium leading-[24.5px] text-white bg-orange py-1 px-2.5">Nový leták</button>
						<button class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
							Otevřít
							<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
								<path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
								<path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
							</svg>
						</button>
					</div>
				</div>
				<div class="flex items-center gap-1.5 md:gap-[15px] pl-1 md:pl-2 pb-[7px] md:pb-[9px]">
					<img class="w-[36px] h-[36px] rounded-md" src="https://k.klmcdn.com/k/upload/shops/logo/5ae2/80x80/fit/lidl-909.webp?v=13.1&from=png" alt="obchod">
					<div class="leading-[21px]">
						<div class="text-xs md:text-lg font-medium">Lidl</div>
						<div class="text-xs font-light">04.04. - 07.04.2024</div>
					</div>
				</div>
			</div>
		</div>

		<div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
			<div class="p-1.5 md:p-2 bg-orange-light rounded-xl">
				<div class="flex mb-[13px] md:mb-[17px] relative" style="position: relative;">
					<div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
					<div class="w-full relative">
						<img class="rounded-lg w-full max-h-[297.66px]" src="https://k.klmcdn.com/s/files/leaflets//212/212271/a8e8/242x336/exactTop/28b7b836215400d9.xqibjp8z23la.webp?v=13.1" alt="letak">
					</div>
					<div class="flex flex-col lg:flex-row gap-1 absolute bottom-1 right-1 z-10">
						<button class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
							Otevřít
							<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
								<path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
								<path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
							</svg>
						</button>
					</div>
				</div>
				<div class="flex items-center gap-1.5 md:gap-[15px] pl-1 md:pl-2 pb-[7px] md:pb-[9px]">
					<img class="w-[36px] h-[36px] rounded-md" src="https://k.klmcdn.com/k/upload/shops/logo/5ae2/80x80/fit/lidl-909.webp?v=13.1&from=png" alt="obchod">
					<div class="leading-[21px]">
						<div class="text-xs md:text-lg font-medium">Lidl</div>
						<div class="text-xs font-light">04.04. - 07.04.2024</div>
					</div>
				</div>
			</div>
		</div>

		<div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
			<div class="p-1.5 md:p-2 bg-orange-light rounded-xl">
				<div class="flex mb-[13px] md:mb-[17px] relative" style="position: relative;">
					<div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
					<div class="w-full relative">
						<img class="rounded-lg w-full max-h-[297.66px]" src="https://k.klmcdn.com/s/files/leaflets//212/212271/a8e8/242x336/exactTop/28b7b836215400d9.xqibjp8z23la.webp?v=13.1" alt="letak">
					</div>
					<div class="flex flex-col lg:flex-row gap-1 absolute bottom-1 right-1 z-10">
						<button class="rounded-md text-xs md:text-sm font-medium leading-[24.5px] text-white bg-green py-1 px-2.5">Aktuální leták</button>
						<button class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
							Otevřít
							<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
								<path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
								<path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
							</svg>
						</button>
					</div>
				</div>
				<div class="flex items-center gap-1.5 md:gap-[15px] pl-1 md:pl-2 pb-[7px] md:pb-[9px]">
					<img class="w-[36px] h-[36px] rounded-md" src="https://k.klmcdn.com/k/upload/shops/logo/5ae2/80x80/fit/lidl-909.webp?v=13.1&from=png" alt="obchod">
					<div class="leading-[21px]">
						<div class="text-xs md:text-lg font-medium">Lidl</div>
						<div class="text-xs font-light">04.04. - 07.04.2024</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<div class="mt-3 grid grid-cols-2 md:grid-cols-5 gap-3">
		<div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
			<div class="p-1.5 md:p-2 bg-light-6 rounded-xl">
				<div class="flex mb-[13px] md:mb-[17px] relative" style="position: relative;">
					<div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
					<div class="w-full relative">
						<img class="rounded-lg w-full max-h-[297.66px]" src="https://k.klmcdn.com/s/files/leaflets//212/212271/a8e8/242x336/exactTop/28b7b836215400d9.xqibjp8z23la.webp?v=13.1" alt="letak">
					</div>
					<div class="flex flex-col lg:flex-row gap-1 absolute bottom-1 right-1 z-10">
						<button class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
							Otevřít
							<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
								<path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
								<path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
							</svg>
						</button>
					</div>
				</div>
				<div class="flex items-center gap-1.5 md:gap-[15px] pl-1 md:pl-2 pb-[7px] md:pb-[9px]">
					<img class="w-[36px] h-[36px] rounded-md" src="https://k.klmcdn.com/k/upload/shops/logo/5ae2/80x80/fit/lidl-909.webp?v=13.1&from=png" alt="obchod">
					<div class="leading-[21px]">
						<div class="text-xs md:text-lg font-medium">Lidl</div>
						<div class="text-xs font-light">04.04. - 07.04.2024</div>
					</div>
				</div>
			</div>
		</div>
		<div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
			<div class="p-1.5 md:p-2 bg-light-6 rounded-xl">
				<div class="flex mb-[13px] md:mb-[17px] relative" style="position: relative;">
					<div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
					<div class="w-full relative">
						<img class="rounded-lg w-full max-h-[297.66px]" src="https://k.klmcdn.com/s/files/leaflets//212/212271/a8e8/242x336/exactTop/28b7b836215400d9.xqibjp8z23la.webp?v=13.1" alt="letak">
					</div>
					<div class="flex flex-col lg:flex-row gap-1 absolute bottom-1 right-1 z-10">
						<button class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
							Otevřít
							<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
								<path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
								<path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
							</svg>
						</button>
					</div>
				</div>
				<div class="flex items-center gap-1.5 md:gap-[15px] pl-1 md:pl-2 pb-[7px] md:pb-[9px]">
					<img class="w-[36px] h-[36px] rounded-md" src="https://k.klmcdn.com/k/upload/shops/logo/5ae2/80x80/fit/lidl-909.webp?v=13.1&from=png" alt="obchod">
					<div class="leading-[21px]">
						<div class="text-xs md:text-lg font-medium">Lidl</div>
						<div class="text-xs font-light">04.04. - 07.04.2024</div>
					</div>
				</div>
			</div>
		</div>
		<div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
			<div class="p-1.5 md:p-2 bg-light-6 rounded-xl">
				<div class="flex mb-[13px] md:mb-[17px] relative" style="position: relative;">
					<div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
					<div class="w-full relative">
						<img class="rounded-lg w-full max-h-[297.66px]" src="https://k.klmcdn.com/s/files/leaflets//212/212271/a8e8/242x336/exactTop/28b7b836215400d9.xqibjp8z23la.webp?v=13.1" alt="letak">
					</div>
					<div class="flex flex-col lg:flex-row gap-1 absolute bottom-1 right-1 z-10">
						<button class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
							Otevřít
							<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
								<path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
								<path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
							</svg>
						</button>
					</div>
				</div>
				<div class="flex items-center gap-1.5 md:gap-[15px] pl-1 md:pl-2 pb-[7px] md:pb-[9px]">
					<img class="w-[36px] h-[36px] rounded-md" src="https://k.klmcdn.com/k/upload/shops/logo/5ae2/80x80/fit/lidl-909.webp?v=13.1&from=png" alt="obchod">
					<div class="leading-[21px]">
						<div class="text-xs md:text-lg font-medium">Lidl</div>
						<div class="text-xs font-light">04.04. - 07.04.2024</div>
					</div>
				</div>
			</div>
		</div>
		<div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
			<div class="p-1.5 md:p-2 bg-light-6 rounded-xl">
				<div class="flex mb-[13px] md:mb-[17px] relative" style="position: relative;">
					<div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
					<div class="w-full relative">
						<img class="rounded-lg w-full max-h-[297.66px]" src="https://k.klmcdn.com/s/files/leaflets//212/212271/a8e8/242x336/exactTop/28b7b836215400d9.xqibjp8z23la.webp?v=13.1" alt="letak">
					</div>
					<div class="flex flex-col lg:flex-row gap-1 absolute bottom-1 right-1 z-10">
						<button class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
							Otevřít
							<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
								<path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
								<path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
							</svg>
						</button>
					</div>
				</div>
				<div class="flex items-center gap-1.5 md:gap-[15px] pl-1 md:pl-2 pb-[7px] md:pb-[9px]">
					<img class="w-[36px] h-[36px] rounded-md" src="https://k.klmcdn.com/k/upload/shops/logo/5ae2/80x80/fit/lidl-909.webp?v=13.1&from=png" alt="obchod">
					<div class="leading-[21px]">
						<div class="text-xs md:text-lg font-medium">Lidl</div>
						<div class="text-xs font-light">04.04. - 07.04.2024</div>
					</div>
				</div>
			</div>
		</div>
		<div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
			<div class="p-1.5 md:p-2 bg-light-6 rounded-xl">
				<div class="flex mb-[13px] md:mb-[17px] relative" style="position: relative;">
					<div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
					<div class="w-full relative">
						<img class="rounded-lg w-full max-h-[297.66px]" src="https://k.klmcdn.com/s/files/leaflets//212/212271/a8e8/242x336/exactTop/28b7b836215400d9.xqibjp8z23la.webp?v=13.1" alt="letak">
					</div>
					<div class="flex flex-col lg:flex-row gap-1 absolute bottom-1 right-1 z-10">
						<button class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
							Otevřít
							<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
								<path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
								<path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
							</svg>
						</button>
					</div>
				</div>
				<div class="flex items-center gap-1.5 md:gap-[15px] pl-1 md:pl-2 pb-[7px] md:pb-[9px]">
					<img class="w-[36px] h-[36px] rounded-md" src="https://k.klmcdn.com/k/upload/shops/logo/5ae2/80x80/fit/lidl-909.webp?v=13.1&from=png" alt="obchod">
					<div class="leading-[21px]">
						<div class="text-xs md:text-lg font-medium">Lidl</div>
						<div class="text-xs font-light">04.04. - 07.04.2024</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<div class="flex gap-3 md:gap-4 items-center mt-10">
		<img class="w-[47px] h-[47px] rounded-md" src="https://k.klmcdn.com/k/upload/shops/logo/5ae2/80x80/fit/lidl-909.webp?v=13.1&from=png" alt="obchod">
		<div class="text-[20px] md:text-[26px] leading-[35px] md:leading-[39px] font-medium">Nabídky z letáků Lidl</div>
	</div>

	<div class="mt-5 grid grid-cols-2 md:grid-cols-5 gap-3 md:mt-[32px]">
		<div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
			<div class="p-1.5 md:p-2 bg-light-6 rounded-xl">
				<div class="flex mb-[14px] md:mb-[17px] relative">
					<div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
					<div class="w-full">
						<img class="w-full cover max-h-[231px] rounded-lg" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/K12FOXvBLrp_ZRPNkmP_jX_tDGco0QxC-FrgobpR4uY/resize:fit:150:150:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvb2ZmZXJzL2ltYWdlLzY2YjlmNTk4YWI1NTktNTAxLmpwZw.webp" alt="letak">
					</div>
					<div class="absolute bottom-1 right-1 z-10">
						<button class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
							Otevřít
							<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
								<path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
								<path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
							</svg>
						</button>
					</div>
				</div>
				<div class="pl-1 md:pl-2 pb-[2px] md:pb-1.5">
					<div class="mb-2 md:mb-0 inline-flex text-xs font-light pt-[3px] pb-1 px-2 border border-light-3 rounded">Vejce</div>
					<div>
						<div class="text-sm md:text-base font-light leading-[24.5px] md:leading-7">Pštrosí Vejce</div>
						<div class="md:text-lg font-semibold leading-7 md:leading-[31.5px] text-primary">99,90 Kč</div>
					</div>
				</div>
			</div>
		</div>
		<div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
			<div class="p-1.5 md:p-2 bg-light-6 rounded-xl">
				<div class="flex mb-[14px] md:mb-[17px] relative">
					<div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
					<div class="w-full">
						<img class="w-full cover max-h-[231px] rounded-lg" src="https://k.klmcdn.com/s/files/leaflets//212/212271/a8e8/242x336/exactTop/28b7b836215400d9.xqibjp8z23la.webp?v=13.1" alt="letak">
					</div>
					<div class="absolute bottom-1 right-1 z-10">
						<button class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
							Otevřít
							<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
								<path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
								<path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
							</svg>
						</button>
					</div>
				</div>
				<div class="pl-1 md:pl-2 pb-[2px] md:pb-1.5">
					<div class="mb-2 md:mb-0 inline-flex text-xs font-light pt-[3px] pb-1 px-2 border border-light-3 rounded">Vejce</div>
					<div>
						<div class="text-sm md:text-base font-light leading-[24.5px] md:leading-7">Pštrosí Vejce</div>
						<div class="md:text-lg font-semibold leading-7 md:leading-[31.5px] text-primary">99,90 Kč</div>
					</div>
				</div>
			</div>
		</div>
		<div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
			<div class="p-1.5 md:p-2 bg-light-6 rounded-xl">
				<div class="flex mb-[14px] md:mb-[17px] relative">
					<div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
					<div class="w-full">
						<img class="w-full cover max-h-[231px] rounded-lg" src="https://k.klmcdn.com/s/files/leaflets//212/212271/a8e8/242x336/exactTop/28b7b836215400d9.xqibjp8z23la.webp?v=13.1" alt="letak">
					</div>
					<div class="absolute bottom-1 right-1 z-10">
						<button class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
							Otevřít
							<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
								<path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
								<path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
							</svg>
						</button>
					</div>
				</div>
				<div class="pl-1 md:pl-2 pb-[2px] md:pb-1.5">
					<div class="mb-2 md:mb-0 inline-flex text-xs font-light pt-[3px] pb-1 px-2 border border-light-3 rounded">Vejce</div>
					<div>
						<div class="text-sm md:text-base font-light leading-[24.5px] md:leading-7">Pštrosí Vejce</div>
						<div class="md:text-lg font-semibold leading-7 md:leading-[31.5px] text-primary">99,90 Kč</div>
					</div>
				</div>
			</div>
		</div>
		<div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
			<div class="p-1.5 md:p-2 bg-light-6 rounded-xl">
				<div class="flex mb-[14px] md:mb-[17px] relative">
					<div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
					<div class="w-full">
						<img class="w-full cover max-h-[231px] rounded-lg" src="https://k.klmcdn.com/s/files/leaflets//212/212271/a8e8/242x336/exactTop/28b7b836215400d9.xqibjp8z23la.webp?v=13.1" alt="letak">
					</div>
					<div class="absolute bottom-1 right-1 z-10">
						<button class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
							Otevřít
							<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
								<path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
								<path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
							</svg>
						</button>
					</div>
				</div>
				<div class="pl-1 md:pl-2 pb-[2px] md:pb-1.5">
					<div class="mb-2 md:mb-0 inline-flex text-xs font-light pt-[3px] pb-1 px-2 border border-light-3 rounded">Vejce</div>
					<div>
						<div class="text-sm md:text-base font-light leading-[24.5px] md:leading-7">Pštrosí Vejce</div>
						<div class="md:text-lg font-semibold leading-7 md:leading-[31.5px] text-primary">99,90 Kč</div>
					</div>
				</div>
			</div>
		</div>
		<div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
			<div class="p-1.5 md:p-2 bg-light-6 rounded-xl">
				<div class="flex mb-[14px] md:mb-[17px] relative">
					<div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
					<div class="w-full">
						<img class="w-full cover max-h-[231px] rounded-lg" src="https://k.klmcdn.com/s/files/leaflets//212/212271/a8e8/242x336/exactTop/28b7b836215400d9.xqibjp8z23la.webp?v=13.1" alt="letak">
					</div>
					<div class="absolute bottom-1 right-1 z-10">
						<button class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
							Otevřít
							<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
								<path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
								<path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
							</svg>
						</button>
					</div>
				</div>
				<div class="pl-1 md:pl-2 pb-[2px] md:pb-1.5">
					<div class="mb-2 md:mb-0 inline-flex text-xs font-light pt-[3px] pb-1 px-2 border border-light-3 rounded">Vejce</div>
					<div>
						<div class="text-sm md:text-base font-light leading-[24.5px] md:leading-7">Pštrosí Vejce</div>
						<div class="md:text-lg font-semibold leading-7 md:leading-[31.5px] text-primary">99,90 Kč</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<div class="text-center mt-[32px] mb-10">
		<a href="#" class="text-sm leading-[24.5px] underline">Načíst další produkty</a>
	</div>

	<div class="flex gap-3 md:gap-4 items-center mb-5 md:mb-[32px]">
		<img class="w-[47px] h-[47px] rounded-md" src="https://k.klmcdn.com/k/upload/shops/logo/5ae2/80x80/fit/lidl-909.webp?v=13.1&from=png" alt="obchod">
		<div class="text-[20px] md:text-[26px] leading-[35px] md:leading-[39px] font-medium">Letáky Lidl i ve vašem městě</div>
	</div>

	<div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
		<div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer flex items-center justify-between text-lg font-light leading-7 pt-[14px] pb-[15px] px-4 bg-light-6 rounded-xl">
			<div>
				Lidl <span class="font-bold">Praha</span>
			</div>
			<svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none">
				<path d="M3.53562 11.3891L10.6067 4.31799" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
				<path d="M11.3139 10.6819L11.3078 3.60477L4.25505 3.59878" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
			</svg>
		</div>
		<div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer flex items-center justify-between text-lg font-light leading-7 pt-[14px] pb-[15px] px-4 bg-light-6 rounded-xl">
			<div>
				Lidl <span class="font-bold">Brno</span>
			</div>
			<svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none">
				<path d="M3.53562 11.3891L10.6067 4.31799" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
				<path d="M11.3139 10.6819L11.3078 3.60477L4.25505 3.59878" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
			</svg>
		</div>
		<div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer flex items-center justify-between text-lg font-light leading-7 pt-[14px] pb-[15px] px-4 bg-light-6 rounded-xl">
			<div>
				Lidl <span class="font-bold">Ostrava</span>
			</div>
			<svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none">
				<path d="M3.53562 11.3891L10.6067 4.31799" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
				<path d="M11.3139 10.6819L11.3078 3.60477L4.25505 3.59878" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
			</svg>
		</div>
		<div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer flex items-center justify-between text-lg font-light leading-7 pt-[14px] pb-[15px] px-4 bg-light-6 rounded-xl">
			<div>
				Lidl <span class="font-bold">Plzeň</span>
			</div>
			<svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none">
				<path d="M3.53562 11.3891L10.6067 4.31799" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
				<path d="M11.3139 10.6819L11.3078 3.60477L4.25505 3.59878" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
			</svg>
		</div>
		<div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer flex items-center justify-between text-lg font-light leading-7 pt-[14px] pb-[15px] px-4 bg-light-6 rounded-xl">
			<div>
				Lidl <span class="font-bold">Liberec</span>
			</div>
			<svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none">
				<path d="M3.53562 11.3891L10.6067 4.31799" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
				<path d="M11.3139 10.6819L11.3078 3.60477L4.25505 3.59878" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
			</svg>
		</div>
	</div>

	<div class="text-center mt-5 md:mt-[32px] mb-10">
		<a href="#" class="text-sm leading-[24.5px] underline">Načíst další města</a>
	</div>

	<div class="flex gap-4 items-center mb-[32px]">
		<img class="w-[47px] h-[47px] rounded-md" src="https://k.klmcdn.com/k/upload/shops/logo/5ae2/80x80/fit/lidl-909.webp?v=13.1&from=png" alt="obchod">
		<div class="text-[20px] md:text-[26px] leading-[35px] md:leading-[39px] font-medium">Lidl Informácie</div>
	</div>

	<div class="flex flex-row items-center gap-3 justify-between mb-5 md:mb-[35px] overflow-x-auto mr-[-20px] md:mr-0">
		<div onclick="showTab(1)" class="px-3 md:px-0 tab-item flex items-center justify-center pb-[5px] gap-2.5 font-medium leading-7 w-full hover:cursor-pointer text-current hover:text-primary transition-colors duration-200 hover:pb-[3px] hover:border-b-2 hover:border-b-primary group">
			<svg class="text-current group-hover:text-primary transition-colors duration-200" xmlns="http://www.w3.org/2000/svg" width="19" height="18" viewBox="0 0 19 18" fill="none">
				<path d="M9.50078 17V3.24709C9.50078 3.24709 7.64605 1.09329 1.39287 1.00641C1.34133 1.00588 1.29019 1.01609 1.24246 1.03642C1.19473 1.05676 1.15135 1.08684 1.11485 1.12488C1.04125 1.20268 0.999987 1.3077 1.00001 1.41711V14.3494C0.999175 14.4559 1.03823 14.5585 1.10883 14.6352C1.17942 14.7119 1.27595 14.7568 1.37776 14.7601C7.64454 14.8446 9.50078 17 9.50078 17ZM9.50078 17C9.50078 17 11.357 14.8446 17.6222 14.7601C17.7241 14.7568 17.8206 14.7119 17.8912 14.6352C17.9618 14.5585 18.0008 14.4559 18 14.3494V1.4171C18 1.30768 17.9587 1.20266 17.8852 1.12487C17.8488 1.08701 17.8057 1.05705 17.7582 1.03671C17.7108 1.01638 17.6599 1.00607 17.6086 1.0064C16.9363 1.01588 13.0878 0.729878 9.68813 3.0993M7.23444 8.35548C5.94879 7.90348 4.61353 7.62349 3.26057 7.52224M7.23444 11.6687C5.94851 11.2185 4.61331 10.9398 3.26057 10.8394M11.7672 8.35548C13.0528 7.90348 14.3881 7.62349 15.7411 7.52224M11.7672 11.6687C13.0531 11.2185 14.3883 10.9398 15.7411 10.8394" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
			</svg>
			Letáky
		</div>

		<div onclick="showTab(2)" class="px-3 md:px-0 tab-item flex items-center justify-center pb-[5px] gap-2.5 font-medium leading-7 w-full hover:cursor-pointer text-current hover:text-primary transition-colors duration-200 hover:pb-[3px] hover:border-b-2 hover:border-b-primary group">
			<svg class="text-current group-hover:text-primary transition-colors duration-200" xmlns="http://www.w3.org/2000/svg" width="19" height="18" viewBox="0 0 19 18" fill="none">
				<path d="M16.7774 7.4V15.4C16.7774 16.2837 16.0525 17 15.1584 17H3.82503C2.93086 17 2.20599 16.2837 2.20599 15.4V7.4M3.0481 1C2.88731 1.00648 2.73211 1.06017 2.60235 1.15421C2.47257 1.24826 2.37411 1.37839 2.31953 1.528L1.09715 4.952C0.967617 5.32259 0.967617 5.72541 1.09715 6.096C1.22872 6.52073 1.49149 6.89412 1.84862 7.16383C2.20574 7.43354 2.63933 7.58606 3.08858 7.6C3.37992 7.58969 3.66635 7.52271 3.93149 7.40291C4.19662 7.28311 4.43525 7.11284 4.63374 6.90182C4.83224 6.69081 4.98669 6.44319 5.08826 6.17314C5.18984 5.9031 5.23655 5.6159 5.22572 5.328C5.20382 5.90812 5.41569 6.47314 5.81483 6.89908C6.21397 7.32502 6.76778 7.5771 7.35475 7.6C7.6461 7.58969 7.93251 7.52271 8.19771 7.40291C8.46283 7.28311 8.70148 7.11284 8.89997 6.90182C9.09839 6.69081 9.25285 6.44319 9.35444 6.17314C9.45604 5.9031 9.50275 5.6159 9.4919 5.328C9.48105 5.6159 9.52776 5.9031 9.62936 6.17314C9.73095 6.44319 9.88541 6.69081 10.0839 6.90182C10.2824 7.11284 10.521 7.28311 10.7862 7.40291C11.0513 7.52271 11.3377 7.58969 11.629 7.6C12.2132 7.57299 12.7629 7.31913 13.1586 6.89364C13.5543 6.46815 13.7639 5.90547 13.7419 5.328C13.7311 5.61721 13.7783 5.90567 13.881 6.17674C13.9835 6.44781 14.1394 6.69612 14.3396 6.90734C14.5398 7.11857 14.7804 7.28852 15.0473 7.40741C15.3143 7.52629 15.6025 7.59174 15.8952 7.6C16.3473 7.58937 16.7845 7.43837 17.1448 7.16842C17.5052 6.89846 17.7704 6.52326 17.9029 6.096C18.0324 5.72541 18.0324 5.32259 17.9029 4.952L16.6643 1.528C16.6097 1.37839 16.5113 1.24826 16.3815 1.15421C16.2517 1.06017 16.0965 1.00648 15.9357 1H3.0481Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
			</svg>
			Obchod
		</div>

		<div onclick="showTab(3)" class="px-3 md:px-0 tab-item flex items-center justify-center pb-[5px] gap-2.5 font-medium leading-7 w-full hover:cursor-pointer text-current hover:text-primary transition-colors duration-200 hover:pb-[3px] hover:border-b-2 hover:border-b-primary group">
			<svg class="text-current group-hover:text-primary transition-colors duration-200" xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 19 19" fill="none">
				<path d="M2.02263 13.3117C3.50804 14.8389 5.53916 15.7136 7.66941 15.7437C9.79964 15.7737 11.8546 14.9565 13.3825 13.4718C14.8671 11.9438 15.6842 9.88875 15.654 7.75851C15.6239 5.62827 14.7489 3.59719 13.2216 2.11187M11.7669 7.79902H12.8983M10.0661 10.0648L11.199 11.1977M7.8003 11.7641V12.8955M1.34289 13.9884C1.22628 14.105 1.13562 14.245 1.07686 14.399C1.01809 14.5531 0.992539 14.7179 1.00188 14.8825C1.01122 15.0472 1.05524 15.208 1.13106 15.3544C1.20688 15.5009 1.31278 15.6296 1.44183 15.7323C3.40212 17.3072 5.87129 18.1096 8.38292 17.988C10.8945 17.8664 13.2746 16.8291 15.0735 15.0722C16.8296 13.2735 17.8663 10.8939 17.9879 8.38306C18.1095 5.87214 17.3076 3.4036 15.7336 1.44352C15.6312 1.31426 15.5026 1.20811 15.3562 1.13202C15.2099 1.05593 15.0492 1.01162 14.8845 1.002C14.7199 0.992369 14.5551 1.01764 14.4008 1.07616C14.2466 1.13468 14.1065 1.22512 13.9897 1.34156L1.34289 13.9884Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
			</svg>
			Produkty
		</div>

		<div onclick="showTab(4)" class="px-3 md:px-0 tab-item flex items-center justify-center pb-[5px] gap-2.5 font-medium leading-7 w-full hover:cursor-pointer text-current hover:text-primary transition-colors duration-200 hover:pb-[3px] hover:border-b-2 hover:border-b-primary group">
			<svg class="text-current group-hover:text-primary transition-colors duration-200" xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 19 19" fill="none">
				<path d="M18 9.5H9.4623M18 9.5L14.8264 6.28889M18 9.5L14.8264 12.7111M1.5387 15.3556C1.91647 16.9422 3.27646 18 4.86311 18H5.54311C6.14755 18 6.67643 17.4711 6.67643 16.8667V13.9956C6.67643 13.3911 6.14755 12.8622 5.54311 12.8622C4.93867 12.8622 4.40978 12.3333 4.40978 11.7289V7.19556C4.40978 6.59111 4.93867 6.06222 5.54311 6.06222C6.14755 6.06222 6.67643 5.53333 6.67643 4.92889V2.13333C6.67643 1.52889 6.14755 1 5.54311 1H4.86311C3.27646 1 1.91647 2.13333 1.5387 3.64444C0.858708 6.96889 0.783153 11.9556 1.5387 15.3556Z" stroke="currentColor" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
			</svg>
			Kontakt
		</div>
	</div>

	<div id="tab-content-1" class="tab-content hidden">
		<div class="text-[20px] font-medium leading-[35px]">Lidl leták 1</div>
		<div>Na nový leták Lidl se mohou kupující těšit každý týden, a to hned dvakrát. První Lidl leták CZ platí od pondělí do neděle a druhý od čtvrtka do neděle. Aktualizovaný je na Kaufinu nejen akční leták Lidl, ale i konkurenční letáky, například Billa leták, který zájemcům nabízí obdobné možnosti výhodných nákupů. Lidl leták však není jedinou možností.
		</div>
	</div>
	<div id="tab-content-2" class="tab-content hidden">
		<div class="text-[20px] font-medium leading-[35px]">Lidl leták 2</div>
		<div>Na nový leták Lidl se mohou kupující těšit každý týden, a to hned dvakrát. První Lidl leták CZ platí od pondělí do neděle a druhý od čtvrtka do neděle. Aktualizovaný je na Kaufinu nejen akční leták Lidl, ale i konkurenční letáky, například Billa leták, který zájemcům nabízí obdobné možnosti výhodných nákupů. Lidl leták však není jedinou možností.
		</div>
	</div>
	<div id="tab-content-3" class="tab-content hidden">
		<div class="text-[20px] font-medium leading-[35px]">Lidl leták 3</div>
		<div>Na nový leták Lidl se mohou kupující těšit každý týden, a to hned dvakrát. První Lidl leták CZ platí od pondělí do neděle a druhý od čtvrtka do neděle. Aktualizovaný je na Kaufinu nejen akční leták Lidl, ale i konkurenční letáky, například Billa leták, který zájemcům nabízí obdobné možnosti výhodných nákupů. Lidl leták však není jedinou možností.
		</div>
	</div>
	<div id="tab-content-4" class="tab-content hidden">
		<div class="text-[20px] font-medium leading-[35px]">Lidl leták 4</div>
		<div>Na nový leták Lidl se mohou kupující těšit každý týden, a to hned dvakrát. První Lidl leták CZ platí od pondělí do neděle a druhý od čtvrtka do neděle. Aktualizovaný je na Kaufinu nejen akční leták Lidl, ale i konkurenční letáky, například Billa leták, který zájemcům nabízí obdobné možnosti výhodných nákupů. Lidl leták však není jedinou možností.
		</div>
	</div>

	<div class="w-full h-px bg-light-4 my-[26px] md:my-[32px]"></div>

	<div class="grid grid-cols-1 md:grid-cols-3 gap-3 mb-3 pb-10 md:pb-[86px]">
		<div class="faq-item active" onclick="toggleContent(this)">
			<div class="bg-light-6 rounded-xl pt-[14px] pb-[15px] px-4 hover:cursor-pointer">
				<div class="title flex items-center justify-between leading-7">
					<div>
						Kdy má Lidl čerstvé květiny?
					</div>
					<svg class="icon-plus" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
						<path d="M7.00009 1L7.00009 13M13 7H1" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
					</svg>
					<svg class="icon-minus hidden" xmlns="http://www.w3.org/2000/svg" width="14" height="2" viewBox="0 0 14 2" fill="none">
						<path d="M13 1H1" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
					</svg>
				</div>
			</div>
			<div class="content mt-3 pt-[14px] pb-[15px] pl-4 pr-[32px] bg-light-6 rounded-lg hidden">
				Lidl má čerstvé květiny obvykle každý den ráno. Lidl má čerstvé květiny obvykle každý den ráno.
			</div>
		</div>

		<div class="faq-item" onclick="toggleContent(this)">
			<div class="bg-light-6 rounded-xl pt-[14px] pb-[15px] px-4 hover:cursor-pointer">
				<div class="title flex items-center justify-between leading-7">
					<div>
						Kdo vlastní Lidl?
					</div>
					<svg class="icon-plus" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
						<path d="M7.00009 1L7.00009 13M13 7H1" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
					</svg>
					<svg class="icon-minus hidden" xmlns="http://www.w3.org/2000/svg" width="14" height="2" viewBox="0 0 14 2" fill="none">
						<path d="M13 1H1" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
					</svg>
				</div>
			</div>
			<div class="content mt-3 pt-[14px] pb-[15px] pl-4 pr-[32px] bg-light-6 rounded-lg hidden">
				Od roku 2023 jsou bedýnky na prodejnách Lidl nahrazené papírovými taškami, které mají zákazníkům zajistit o to větší komfort.Umisťovány jsou na pobočky nahodile, vždy, když je k dispozici potřebné ovoce či zelenina.
			</div>
		</div>

		<div class="faq-item" onclick="toggleContent(this)">
			<div class="bg-light-6 rounded-xl pt-[14px] pb-[15px] px-4 hover:cursor-pointer">
				<div class="title flex items-center justify-between leading-7">
					<div>
						Kdy jsou k dostání Lidl bedýnky?
					</div>
					<svg class="icon-plus" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
						<path d="M7.00009 1L7.00009 13M13 7H1" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
					</svg>
					<svg class="icon-minus hidden" xmlns="http://www.w3.org/2000/svg" width="14" height="2" viewBox="0 0 14 2" fill="none">
						<path d="M13 1H1" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
					</svg>
				</div>
			</div>
			<div class="content mt-3 pt-[14px] pb-[15px] pl-4 pr-[32px] bg-light-6 rounded-lg hidden">
				Lidl bedýnky jsou k dostání každou středu a sobotu.
			</div>
		</div>
	</div>
</div>

<div class="bg-light-6">
	<div class="container p-0">
		<div class="text-[20px] md:text-[26px] leading-[35px] md:leading-[39px] font-medium pt-6 md:pt-[43px] pb-[25px] md:pb-0 px-5 md:p-0">Další obchody</div>

		<div class="hidden md:flex items-center gap-8 justify-end mb-[28px]">
			<div class="custom-prev cursor-pointer">
				<svg class="active-svg" width="17" height="33" viewBox="0 0 17 33" fill="none" xmlns="http://www.w3.org/2000/svg">
					<path d="M16 1L2.2205 13.7601C0.593167 15.267 0.593167 17.733 2.2205 19.2399L16 32" stroke="#080B10" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
				</svg>
				<svg class="disabled-svg hidden" width="12" height="23" viewBox="0 0 12 23" fill="none" xmlns="http://www.w3.org/2000/svg">
					<path opacity="0.3" d="M11 21.3636L1.81367 13.2436C0.728777 12.2846 0.728777 10.7154 1.81367 9.7564L11 1.63636" stroke="#292D32" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
				</svg>
			</div>
			<div class="custom-next cursor-pointer">
				<svg class="active-svg" width="17" height="33" viewBox="0 0 17 33" fill="none" xmlns="http://www.w3.org/2000/svg">
					<path d="M1 32L14.7795 19.2399C16.4068 17.733 16.4068 15.267 14.7795 13.7601L1 1" stroke="#080B10" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
				</svg>
				<svg class="disabled-svg hidden" width="12" height="23" viewBox="0 0 12 23" fill="none" xmlns="http://www.w3.org/2000/svg">
					<path opacity="0.3" d="M1 1.63636L10.1863 9.75643C11.2712 10.7154 11.2712 12.2846 10.1863 13.2436L1 21.3636" stroke="#292D32" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
				</svg>
			</div>
		</div>

		<div class="pl-5 md:pl-0 swiper w-full h-[87px] md:h-[140px]">
			<div class="swiper-wrapper cursor-grab">
				<div class="swiper-slide">
					<img class="h-[87px] md:h-[140px] w-[87px] md:w-[140px] rounded-xl" src="https://k.klmcdn.com/k/upload/shops/logo/ebce/80x80/fit/penny-market-619.webp?v=13.1&from=png" alt="obchod">
				</div>
				<div class="swiper-slide">
					<img class="h-[87px] md:h-[140px] w-[87px] md:w-[140px] rounded-xl" src="https://k.klmcdn.com/k/upload/shops/logo/ebce/80x80/fit/penny-market-619.webp?v=13.1&from=png" alt="obchod">
				</div>
				<div class="swiper-slide">
					<img class="h-[87px] md:h-[140px] w-[87px] md:w-[140px] rounded-xl" src="https://k.klmcdn.com/k/upload/shops/logo/ebce/80x80/fit/penny-market-619.webp?v=13.1&from=png" alt="obchod">
				</div>
				<div class="swiper-slide">
					<img class="h-[87px] md:h-[140px] w-[87px] md:w-[140px] rounded-xl" src="https://k.klmcdn.com/k/upload/shops/logo/ebce/80x80/fit/penny-market-619.webp?v=13.1&from=png" alt="obchod">
				</div>
				<div class="swiper-slide">
					<img class="h-[87px] md:h-[140px] w-[87px] md:w-[140px] rounded-xl" src="https://k.klmcdn.com/k/upload/shops/logo/ebce/80x80/fit/penny-market-619.webp?v=13.1&from=png" alt="obchod">
				</div>
				<div class="swiper-slide">
					<img class="h-[87px] md:h-[140px] w-[87px] md:w-[140px] rounded-xl" src="https://k.klmcdn.com/k/upload/shops/logo/ebce/80x80/fit/penny-market-619.webp?v=13.1&from=png" alt="obchod">
				</div>
				<div class="swiper-slide">
					<img class="h-[87px] md:h-[140px] w-[87px] md:w-[140px] rounded-xl" src="https://k.klmcdn.com/k/upload/shops/logo/ebce/80x80/fit/penny-market-619.webp?v=13.1&from=png" alt="obchod">
				</div>
				<div class="swiper-slide">
					<img class="h-[87px] md:h-[140px] w-[87px] md:w-[140px] rounded-xl" src="https://k.klmcdn.com/k/upload/shops/logo/ebce/80x80/fit/penny-market-619.webp?v=13.1&from=png" alt="obchod">
				</div>
				<div class="swiper-slide">
					<img class="h-[87px] md:h-[140px] w-[87px] md:w-[140px] rounded-xl" src="https://k.klmcdn.com/k/upload/shops/logo/ebce/80x80/fit/penny-market-619.webp?v=13.1&from=png" alt="obchod">
				</div>
				<div class="swiper-slide">
					<img class="h-[87px] md:h-[140px] w-[87px] md:w-[140px] rounded-xl" src="https://k.klmcdn.com/k/upload/shops/logo/ebce/80x80/fit/penny-market-619.webp?v=13.1&from=png" alt="obchod">
				</div>
			</div>
		</div>

		<div class="px-5 md:px-0">
			<div class="w-full h-px bg-light-4 mt-[35px] md:mt-[50px] mb-[25px] md:mb-10"></div>
		</div>

		<div class="text-[20px] md:text-[26px] leading-[35px] md:leading-[39px] font-medium md:pb-0 px-5 md:px-0">Další letáky</div>

		<div class="mt-5 grid grid-cols-2 md:grid-cols-5 gap-3 md:mt-[32px] pb-10 md:pb-[51px] px-5 md:pb-0 md:px-0">
			<div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
				<div class="p-1.5 md:p-2 bg-white rounded-xl">
					<div class="flex mb-[14px] md:mb-[17px] relative">
						<div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
						<div class="w-full">
							<img class="w-full cover max-h-[231px] rounded-lg" src="https://k.klmcdn.com/s/files/leaflets//212/212271/a8e8/242x336/exactTop/28b7b836215400d9.xqibjp8z23la.webp?v=13.1" alt="letak">
						</div>
						<div class="absolute bottom-1 right-1 z-10">
							<button class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
								Otevřít
								<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
									<path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
									<path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
								</svg>
							</button>
						</div>
					</div>
					<div class="pl-1 md:pl-2 pb-[2px] md:pb-1.5">
						<div class="mb-2 md:mb-0 inline-flex text-xs font-light pt-[3px] pb-1 px-2 border border-light-3 rounded">Vejce</div>
						<div>
							<div class="text-sm md:text-base font-light leading-[24.5px] md:leading-7">Pštrosí Vejce</div>
							<div class="md:text-lg font-semibold leading-7 md:leading-[31.5px] text-primary">99,90 Kč</div>
						</div>
					</div>
				</div>
			</div>
			<div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
				<div class="p-1.5 md:p-2 bg-white rounded-xl">
					<div class="flex mb-[14px] md:mb-[17px] relative">
						<div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
						<div class="w-full">
							<img class="w-full cover max-h-[231px] rounded-lg" src="https://k.klmcdn.com/s/files/leaflets//212/212271/a8e8/242x336/exactTop/28b7b836215400d9.xqibjp8z23la.webp?v=13.1" alt="letak">
						</div>
						<div class="absolute bottom-1 right-1 z-10">
							<button class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
								Otevřít
								<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
									<path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
									<path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
								</svg>
							</button>
						</div>
					</div>
					<div class="pl-1 md:pl-2 pb-[2px] md:pb-1.5">
						<div class="mb-2 md:mb-0 inline-flex text-xs font-light pt-[3px] pb-1 px-2 border border-light-3 rounded">Vejce</div>
						<div>
							<div class="text-sm md:text-base font-light leading-[24.5px] md:leading-7">Pštrosí Vejce</div>
							<div class="md:text-lg font-semibold leading-7 md:leading-[31.5px] text-primary">99,90 Kč</div>
						</div>
					</div>
				</div>
			</div>
			<div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
				<div class="p-1.5 md:p-2 bg-white rounded-xl">
					<div class="flex mb-[14px] md:mb-[17px] relative">
						<div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
						<div class="w-full">
							<img class="w-full cover max-h-[231px] rounded-lg" src="https://k.klmcdn.com/s/files/leaflets//212/212271/a8e8/242x336/exactTop/28b7b836215400d9.xqibjp8z23la.webp?v=13.1" alt="letak">
						</div>
						<div class="absolute bottom-1 right-1 z-10">
							<button class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
								Otevřít
								<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
									<path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
									<path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
								</svg>
							</button>
						</div>
					</div>
					<div class="pl-1 md:pl-2 pb-[2px] md:pb-1.5">
						<div class="mb-2 md:mb-0 inline-flex text-xs font-light pt-[3px] pb-1 px-2 border border-light-3 rounded">Vejce</div>
						<div>
							<div class="text-sm md:text-base font-light leading-[24.5px] md:leading-7">Pštrosí Vejce</div>
							<div class="md:text-lg font-semibold leading-7 md:leading-[31.5px] text-primary">99,90 Kč</div>
						</div>
					</div>
				</div>
			</div>
			<div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
				<div class="p-1.5 md:p-2 bg-white rounded-xl">
					<div class="flex mb-[14px] md:mb-[17px] relative">
						<div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
						<div class="w-full">
							<img class="w-full cover max-h-[231px] rounded-lg" src="https://k.klmcdn.com/s/files/leaflets//212/212271/a8e8/242x336/exactTop/28b7b836215400d9.xqibjp8z23la.webp?v=13.1" alt="letak">
						</div>
						<div class="absolute bottom-1 right-1 z-10">
							<button class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
								Otevřít
								<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
									<path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
									<path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
								</svg>
							</button>
						</div>
					</div>
					<div class="pl-1 md:pl-2 pb-[2px] md:pb-1.5">
						<div class="mb-2 md:mb-0 inline-flex text-xs font-light pt-[3px] pb-1 px-2 border border-light-3 rounded">Vejce</div>
						<div>
							<div class="text-sm md:text-base font-light leading-[24.5px] md:leading-7">Pštrosí Vejce</div>
							<div class="md:text-lg font-semibold leading-7 md:leading-[31.5px] text-primary">99,90 Kč</div>
						</div>
					</div>
				</div>
			</div>
			<div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
				<div class="p-1.5 md:p-2 bg-white rounded-xl">
					<div class="flex mb-[14px] md:mb-[17px] relative">
						<div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
						<div class="w-full">
							<img class="w-full cover max-h-[231px] rounded-lg" src="https://k.klmcdn.com/s/files/leaflets//212/212271/a8e8/242x336/exactTop/28b7b836215400d9.xqibjp8z23la.webp?v=13.1" alt="letak">
						</div>
						<div class="absolute bottom-1 right-1 z-10">
							<button class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
								Otevřít
								<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
									<path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
									<path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
								</svg>
							</button>
						</div>
					</div>
					<div class="pl-1 md:pl-2 pb-[2px] md:pb-1.5">
						<div class="mb-2 md:mb-0 inline-flex text-xs font-light pt-[3px] pb-1 px-2 border border-light-3 rounded">Vejce</div>
						<div>
							<div class="text-sm md:text-base font-light leading-[24.5px] md:leading-7">Pštrosí Vejce</div>
							<div class="md:text-lg font-semibold leading-7 md:leading-[31.5px] text-primary">99,90 Kč</div>
						</div>
					</div>
				</div>
			</div>
			<div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
				<div class="p-1.5 md:p-2 bg-white rounded-xl">
					<div class="flex mb-[14px] md:mb-[17px] relative">
						<div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
						<div class="w-full">
							<img class="w-full cover max-h-[231px] rounded-lg" src="https://k.klmcdn.com/s/files/leaflets//212/212271/a8e8/242x336/exactTop/28b7b836215400d9.xqibjp8z23la.webp?v=13.1" alt="letak">
						</div>
						<div class="absolute bottom-1 right-1 z-10">
							<button class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
								Otevřít
								<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
									<path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
									<path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
								</svg>
							</button>
						</div>
					</div>
					<div class="pl-1 md:pl-2 pb-[2px] md:pb-1.5">
						<div class="mb-2 md:mb-0 inline-flex text-xs font-light pt-[3px] pb-1 px-2 border border-light-3 rounded">Vejce</div>
						<div>
							<div class="text-sm md:text-base font-light leading-[24.5px] md:leading-7">Pštrosí Vejce</div>
							<div class="md:text-lg font-semibold leading-7 md:leading-[31.5px] text-primary">99,90 Kč</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>


</div>

<!--<div class="container">
    <div class="flex gap-3 md:gap-4 items-center mb-5 md:mb-[32px] mt-10 md:mt-[73px]">
        <img class="w-[47px] h-[47px] rounded-md" src="https://k.klmcdn.com/k/upload/shops/logo/5ae2/80x80/fit/lidl-909.webp?v=13.1&amp;from=png" alt="obchod">
        <div class="text-[20px] md:text-[26px] leading-[35px] md:leading-[39px] font-medium">Letáky Lidl i ve vašem městě</div>
    </div>


    <div class="flex flex-wrap gap-1.5 md:gap-3 pb-10 md:pb-[55px]">
        {foreach range(1, 17) as $i}
            <div class="transition-transform duration-200 transform hover:scale-[105%] cursor-pointer rounded-lg flex items-center gap-[5px] md:gap-[11px] p-1.5 md:p-3 text-xs md:text-sm leading-[21px] md:leading-[24.5px] font-light bg-light-6">
                <svg class="w-[15px] h-[15px] md:w-[33px] md:h-[33px]" xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none">
                    <path d="M10.1086 0.466494C9.29607 0.165029 8.41728 0 7.49987 0C6.58247 0 5.70368 0.165029 4.89119 0.466494L4.23901 7.5L4.89119 14.5335C5.70368 14.835 6.58247 15 7.49987 15C8.41728 15 9.29607 14.835 10.1086 14.5335L10.7607 7.5L10.1086 0.466494Z" fill="#FFDA44"/>
                    <path d="M15.0002 7.50003C15.0002 4.27532 12.9649 1.52622 10.1089 0.466553V14.5336C12.9649 13.4738 15.0002 10.7248 15.0002 7.50003Z" fill="#D80027"/>
                    <path d="M0 7.50003C0 10.7248 2.03531 13.4738 4.89132 14.5336V0.466553C2.03531 1.52622 0 4.27532 0 7.50003Z" fill="black"/>
                </svg>
                Belgium
            </div>
        {/foreach}
    </div>
</div>-->

<script type="application/ld+json" n:if="count($leafletsInTop)">
	{
		"@context": "http://schema.org",
		"itemListElement": [
			{foreach $leafletsInTop as $leaflet}
			{continueIf $leaflet->isChecked() === false}
			{
				"endDate": {$leaflet->getValidTill()->format('Y-m-d')},
				"startDate": {$leaflet->getValidSince()->format('Y-m-d')},
				"location": {
					"address": {
						"name": {$leaflet->getName()},
						"@type": "PostalAddress"
					},
					"url": {link //:Kaufino:Shop:shop $leaflet->getShop()},
						"image": {$shop->getLogoUrl() |image:160,140,'fit','webp'},
						"name": {$leaflet->getShop()->getName()},
						"@type": "Place"
					},
					"performer": {
						"name": {$leaflet->getShop()->getName()},
						"@type": "Organization"
					},
					"image": {$leaflet->getFirstPage() ? $leaflet->getFirstPage()->getImageUrl() |image:60,84,'exactTop','webp' : ""},
					"name": {$leaflet->getName()},
					"url": {link //:Kaufino:Leaflet:leaflet, $leaflet->getShop(), $leaflet},
						"description": "",
						"eventAttendanceMode": "https://schema.org/OfflineEventAttendanceMode",
						"eventStatus": "https://schema.org/EventScheduled",
						"organizer": {
							"@type": "Organization",
							"name": {$leaflet->getShop()->getName()},
							"url": {link //:Kaufino:Shop:shop $leaflet->getShop()}
							},
							"@type": "SaleEvent"
						}{sep},{/sep}
			{/foreach}
		],
		"@type": "OfferCatalog"
	}
</script>
<script>
    function checkCookie(name) {
        var cookies = document.cookie.split(';');
        for (var i = 0; i < cookies.length; i++) {
            var cookie = cookies[i].trim();
            if (cookie.indexOf(name + '=') === 0) {
                return true;
            }
        }
        return false;
    }

    (function() {
        const elementsWithBrochureId = document.querySelectorAll('[data-brochure-id]');

        if (elementsWithBrochureId.length > 0) {
            if (checkCookie('userLocation') === false && navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(function(position) {
                    let latitude = position.coords.latitude;
                    let longitude = position.coords.longitude;

                    let locationData = {
                        latitude: latitude,
                        longitude: longitude,
                        strategy: 'DEVICE'
                    };

                    let locationJson = JSON.stringify(locationData);

                    document.cookie = "userLocation=" + locationJson + "; expires=Thu, 18 Dec 2050 12:00:00 UTC; path=/";
                }, function(error) {

                });
            }
        }

        elementsWithBrochureId.forEach(element => {
            const brochureId = element.dataset.brochureId;

            element.addEventListener('click', function(event) {
                const xhr = new XMLHttpRequest();
                xhr.open('POST', {link :Api:Offerista:brochureClick, websiteId: $presenter->website->getId()}, true);
                xhr.setRequestHeader('Content-Type', 'application/json');

                xhr.send(JSON.stringify({ brochureId: brochureId }));
            });

            const xhr = new XMLHttpRequest();
            xhr.open('POST', {link :Api:Offerista:brochureImpression, websiteId: $presenter->website->getId()}, true);
            xhr.setRequestHeader('Content-Type', 'application/json');

            xhr.send(JSON.stringify({ brochureId: brochureId }));
        });
    })();
</script>
