{block scripts}
    {include parent}

    <script type="application/ld+json" n:if="count($leaflets)">
        {
            "@context": "https://schema.org",
            "@type": "OfferCatalog",
            "name": "{_"$websiteType.homepage.h1"}",
        "url": {$presenter->link('//this')},
        "itemListElement": [
            {foreach $leaflets as $key => $leaflet}
                {
                    "@type": "SaleEvent",
                    "name": {$leaflet->getName()},
                    "url": {$presenter->link('Leaflet:leaflet', $leaflet->getShop(), $leaflet)},
                    "startDate": {$leaflet->getValidSince()|date:'Y-m-d'},
                    "endDate": {$leaflet->getValidTill()|date:'Y-m-d'},
                    "location": {
                        "@type": "Place",
                        "name": {$leaflet->getShop()->getName()},
                        "url": {$presenter->link('Shop:shop', $leaflet->getShop())}
                    }
                }{sep},{/sep}
        {/foreach}
        ]
    }
    </script>
{/block}

{block title}{if $website->hasAdSense()}{_"$websiteType.homepage.title"}{else}{_"$websiteType.homepage.article.title"}{/if}{/block}

{block description}{_"$websiteType.homepage.text"}{/block}

{block hreflang}
    {var $otherWebsites = $footerWebsites()}

    {foreach $otherWebsites as $otherWebsite}
        {continueIf $otherWebsite->getType() !== 'oferto'}

        <link rel="alternate" n:attr="hreflang: $otherWebsite->getLocalization()->getFullLocale('-')" href={$otherWebsite->getDomain()} />
    {/foreach}
{/block}

{block content}
<div class="container">
    
    <div
        class="swiper k-hp-swiper mt-[27px] mb-[22px] md:mt-0 md:mb-[50px]"
        style="mask-image: linear-gradient(to right, rgba(0, 0, 0, 1) 60%, rgba(0, 0, 0, 0) 101%)"
    >
        <div class="swiper-wrapper">
            {foreach $shops as $shop}                
                <a n:href="Shop:shop $shop" title="{$shop->getName()}" class="swiper-slide" style="width: 94.2px; margin-right: 16px;" role="group" aria-label="6 / 18">
                    <picture class="flex justify-center items-center rounded-full w-[64px] h-[64px] md:w-[90px] md:h-[90px] shadow-md transition-shadow duration-200 ease-in-out border-2 border-primary overflow-hidden">
                        <source 
                            srcset="{$shop->getLogoUrl() |image:80,70,'fit','webp'}" 
                            type="image/webp"
                        >
                        <img 
                            src="{$shop->getLogoUrl() |image:80,70}" 
                            data-sizes="auto"
                            width="80"
                            height="70"
                            alt="{$shop->getName()}"
                            class="max-h-[40px] max-w-[40px] md:max-w-[50px]"
                            loading="lazy"
                        >
                    </picture>                                    
                </a>
            {/foreach}
        </div>
    </div>

    <h1 class="text-[24px] leading-[39px] md:text-[33px] font-medium mb-1.5">{if $localization->isCzech()}{_"$websiteType.homepage.h1"}{else}{_"$websiteType.homepage.title"}{/if}</h1>
    <p class="hidden md:block text-sm font-light text-[#646C7C] mb-[32px]">{_"$websiteType.homepage.article.text"}</p>

    <div class="grid grid-cols-2 md:grid-cols-5 gap-3 mb-5">
        {foreach $leaflets as $leaflet}
            {if $iterator->first}
                {include '../components/leaflet-big.latte', leaflet => $leaflet}
            {else}
                {include '../components/leaflet.latte', leaflet => $leaflet}
            {/if}
        {/foreach}                
    </div>    

    <div class="flex justify-center mt-[32px] mb-[41px]">
        <a href="{link Leaflets:leaflets}" class="flex justify-center items-center text-white gap-[9px] py-[17px] w-full max-w-[325px] bg-primary rounded-xl hover:bg-primary-hover transition duration-200">
            {_"$websiteType.homepage.allLeaflets"}
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
                <path d="M4.43611 13.9086L12.876 5.46875" stroke="white" stroke-width="1.68907" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M13.6439 12.3741L13.6373 4.69494L5.98463 4.68844" stroke="white" stroke-width="1.68907" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
        </a>
    </div>

    <div>
        <div class="flex items-center md:gap-[5px] text-[24px] leading-[35px] md:leading-[39px] text-[20px] md:text-[26px] font-medium mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40" fill="none">
                <path d="M0 2.99902C0 1.34217 1.34315 -0.000976562 3 -0.000976562H37C38.6569 -0.000976562 40 1.34217 40 2.99902V36.999C40 38.6559 38.6569 39.999 37 39.999H3C1.34315 39.999 0 38.6559 0 36.999V2.99902Z" fill="white"/>
                <path d="M15.2135 14.4801C15.2135 13.6631 14.593 8.38105 19.5446 8.38105C24.48 8.38105 23.8802 13.774 23.8802 14.4801M21.4534 18.8126H19.1042C18.7442 18.8128 18.3955 18.9383 18.1179 19.1676C17.8402 19.3968 17.6511 19.7155 17.5828 20.069C17.5144 20.4225 17.5712 20.7888 17.7434 21.105C17.9156 21.4211 18.1924 21.6676 18.5264 21.802L20.9115 22.7553C21.2462 22.8892 21.5239 23.1356 21.6967 23.4521C21.8694 23.7685 21.9265 24.1353 21.8581 24.4893C21.7897 24.8433 21.6002 25.1625 21.3219 25.3917C21.0437 25.6211 20.6943 25.7464 20.3337 25.7459H17.9868M19.7202 18.8126V17.9459M19.7202 26.6126V25.7459M6.54688 11.0126V9.27923C6.54688 8.81953 6.72949 8.37864 7.05456 8.05358C7.37961 7.72852 7.82051 7.5459 8.28021 7.5459H10.0135M32.5469 11.0126V9.27923C32.5469 8.81953 32.3643 8.37864 32.0392 8.05358C31.7142 7.72852 31.2732 7.5459 30.8135 7.5459H29.0802M6.54688 30.0792V31.8126C6.54688 32.2722 6.72949 32.7132 7.05456 33.0383C7.37961 33.3633 7.82051 33.5459 8.28021 33.5459H10.0135M32.5469 30.0792V31.8126C32.5469 32.2722 32.3643 32.7132 32.0392 33.0383C31.7142 33.3633 31.2732 33.5459 30.8135 33.5459H29.0802M27.1181 15.9491C27.1086 15.5732 26.9594 15.2143 26.6996 14.9425C26.4398 14.6708 26.088 14.5056 25.7129 14.4792H13.3797C13.0047 14.5056 12.653 14.6708 12.3934 14.9426C12.1338 15.2144 11.9848 15.5733 11.9757 15.9491L10.0216 28.265C9.98695 28.4774 9.99635 28.6946 10.0492 28.9032C10.1021 29.1118 10.1974 29.3072 10.3291 29.4774C10.4608 29.6476 10.6261 29.7888 10.8148 29.8924C11.0034 29.9959 11.2113 30.0595 11.4256 30.0792H27.667C27.8813 30.0597 28.0894 29.9961 28.2781 29.8927C28.467 29.7893 28.6324 29.648 28.7643 29.4779C28.896 29.3077 28.9915 29.1121 29.0444 28.9035C29.0973 28.6948 29.1068 28.4775 29.0721 28.265L27.1181 15.9491Z" stroke="black" stroke-width="1.84889" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            Co je v akci
        </div>

        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
            {foreach $topOffers as $offer}
                <div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
                <div class="p-1.5 md:p-2 bg-light-6 rounded-xl">
                    <div class="flex mb-[14px] md:mb-[17px] relative">
                        <div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
                        <div class="w-full">
                            <img class="w-full cover max-h-[231px] rounded-lg" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/sq0HjsGeRgZ9MBEFAzuTmYnNp9rOoqw3eDosAFaSDuw/resize:fit:150:150:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvb2ZmZXJzL2ltYWdlLzY3N2VlNjcyY2VjOWEtODY2LmpwZw.webp" alt="letak">
                        </div>
                        <div class="absolute bottom-1 right-1 z-10">
                            <button class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
                                Otevřít
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                    <path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                    <path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="pl-1 md:pl-2 pb-[2px] md:pb-1.5">
                        <div class="mb-2 md:mb-0 inline-flex text-xs font-light pt-[3px] pb-1 px-2 border border-light-3 rounded" n:if="$offer->getFirstTag()">{$offer->getFirstTag()->getName()}</div>
                        <div>
                            <div class="text-sm md:text-base font-light leading-[24.5px] md:leading-7">{$offer->getName()}</div>
                            <div class="md:text-lg font-semibold leading-7 md:leading-[31.5px] text-primary">{$offer->getCurrentPrice()|price:$offer->getLocalization()}</div>
                        </div>
                    </div>
                </div>
            </div>
            {/foreach}
        </div>
    </div>

    <div class="flex justify-center mt-[32px] mb-[41px]">
        <button class="flex justify-center items-center text-primary gap-[9px] py-[17px] w-full max-w-[325px] border border-primary rounded-xl hover:bg-primary-light-hover transition duration-200">
            Další produkty
            <svg xmlns="http://www.w3.org/2000/svg" width="17" height="18" viewBox="0 0 17 18" fill="none">
                <path d="M3.8365 13.6033L12.2764 5.16345" stroke="currentColor" stroke-width="1.68907" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                <path d="M13.0443 12.0688L13.0377 4.38964L5.38502 4.38314" stroke="currentColor" stroke-width="1.68907" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
        </button>
    </div>

    <div>
        <h2 class="flex items-center md:gap-[5px] text-[24px] leading-[35px] md:leading-[39px] text-[20px] md:text-[26px] font-medium mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40" fill="none">
                <path d="M0 2.99902C0 1.34217 1.34315 -0.000976562 3 -0.000976562H37C38.6569 -0.000976562 40 1.34217 40 2.99902V36.999C40 38.6559 38.6569 39.999 37 39.999H3C1.34315 39.999 0 38.6559 0 36.999V2.99902Z" fill="white"/>
                <path d="M12.9638 14.9171H14.6469C15.1087 14.9171 15.5516 15.1006 15.8782 15.4271C16.2047 15.7536 16.3882 16.1965 16.3882 16.6583V17.4709C16.3882 17.7575 16.459 18.0397 16.5942 18.2924C16.7295 18.5452 16.925 18.7606 17.1636 18.9196L18.2756 19.6613C18.4987 19.8096 18.6818 20.0107 18.8084 20.247C18.935 20.4831 19.0012 20.7469 19.0012 21.0148C19.0012 21.2828 18.935 21.5465 18.8084 21.7827C18.6818 22.0188 18.4987 22.22 18.2756 22.3683L15.3376 24.3231M25.0361 14.9171H24.2236C23.9267 14.917 23.6348 14.9928 23.3755 15.1374C23.1162 15.2819 22.898 15.4903 22.7419 15.7428C22.5859 15.9953 22.497 16.2835 22.4836 16.5801C22.4703 16.8767 22.533 17.1717 22.6658 17.4372C23.1614 18.4286 23.2752 18.4761 23.5828 18.6607L25.7883 19.9817M12.0351 18.3984C12.0351 20.2456 12.7689 22.0171 14.0751 23.3232C15.3812 24.6295 17.1528 25.3632 19 25.3632C20.8471 25.3632 22.6186 24.6295 23.9248 23.3232C25.231 22.0171 25.9648 20.2456 25.9648 18.3984C25.9648 16.5512 25.231 14.7797 23.9248 13.4735C22.6186 12.1674 20.8471 11.4336 19 11.4336C17.1528 11.4336 15.3812 12.1674 14.0751 13.4735C12.7689 14.7797 12.0351 16.5512 12.0351 18.3984ZM29.4472 18.3984C29.4472 27.5687 21.4005 32.7122 19.4306 33.8371C19.2992 33.9118 19.1506 33.9512 18.9994 33.9512C18.8481 33.9512 18.6995 33.9118 18.5681 33.8371C16.5983 32.7111 8.55273 27.5664 8.55273 18.3984C8.55273 15.6276 9.65342 12.9703 11.6127 11.0111C13.5719 9.05186 16.2292 7.95117 19 7.95117C21.7707 7.95117 24.428 9.05186 26.3873 11.0111C28.3465 12.9703 29.4472 15.6276 29.4472 18.3984Z" stroke="black" stroke-width="1.85728" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            {_"$websiteType.homepage.city"}
        </h2>

        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
            {foreach $cities as $city}                                        
                <a n:href="City:city $city" class="flex items-center justify-between font-light leading-7 pt-[14px] pb-[15px] px-4 bg-light-6 rounded-xl transition-transform duration-200 transform hover:scale-[103%] cursor-pointer">
                    {$city->getName()}
                    <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none">
                        <path d="M3.53538 11.3891L10.6064 4.31799" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M11.3139 10.6819L11.3078 3.60477L4.25505 3.59878" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </a>
            {/foreach}            
        </div>
    </div>

    <div class="flex justify-center mt-[32px] mb-[41px]">
        <a n:href="Cities:cities" class="flex justify-center items-center text-primary gap-[9px] py-[17px] w-full max-w-[325px] border border-primary rounded-xl hover:bg-primary-light-hover transition duration-200">
            {_'kaufino.showMore.cities'}
            <svg xmlns="http://www.w3.org/2000/svg" width="17" height="18" viewBox="0 0 17 18" fill="none">
                <path d="M3.8365 13.6033L12.2764 5.16345" stroke="currentColor" stroke-width="1.68907" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                <path d="M13.0443 12.0688L13.0377 4.38964L5.38502 4.38314" stroke="currentColor" stroke-width="1.68907" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
        </a>
    </div>
</div>



