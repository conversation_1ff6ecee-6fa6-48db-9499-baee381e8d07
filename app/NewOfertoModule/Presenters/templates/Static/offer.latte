
{block title}
title
{/block}

{block description}
desc
{/block}

{block robots}noindex,nofollow{/block}

{block head}    
    
{/block}

{block content}

<div class="leaflet k-lf-layout o-optimize-0">
    <div class="container">
		<div class="leaflet__content">
			<div class="d-block overflow-hidden">
				<div class="page-header leaflet__detail-header leaflet__detail-header--mobile-row">
					<div class="leaflet__detail-header-content">
                        <h1 class="page-header__title">
                            h1
                            <span class="leaflet__date">01.01.2023 - 31.01.2023</span>
                        </h1>                        

						<p class="page-header__text mw-600">
                            text
                        </p>
					</div>

					<div class="leaflet__detail-header-side">
						
					</div>
				</div>

                
                <div class="">                                        
                    
                </div>                

                
                {*include 'paginator.latte', shop => $shop, leaflet => $leaflet, currentPage => $currentPage*}                
                <div class="k-paginator__wrapper">
                    <a href="/lidl/lidl-48" class="active k-paginator__item">1</a>
                    <a href="/lidl/lidl-48?page=2" class="k-paginator__item">2</a>
                    <a href="/lidl/lidl-48?page=3" class="k-paginator__item">3</a>
                    <a href="/lidl/lidl-48?page=4" class="k-paginator__item">4</a>
                    <small class="k-paginator__separator">...</small>
                    <a class="k-paginator__item" href="/lidl/lidl-48?page=57">57</a>
                    <a class="k-paginator__button" href="/lidl/lidl-48?page=2">»</a>
                </div>

                <div>
                    
                </div>                

                <div class="leaflet-preview o-leaflet-preview mb-5">						                    
                    <picture id="" data-expand="100">                            
                        <source data-srcset="
                                https://s.kaufino.com/k/upload/offers/image/a468/300x300/fit/6421e997a8037-951.jpg?v=12.3 768w,
                                https://s.kaufino.com/k/upload/offers/image/a468/300x300/fit/6421e997a8037-951.jpg?v=12.3 1740w
                            " type="image/webp" sizes="645px" srcset="
                                https://s.kaufino.com/k/upload/offers/image/a468/300x300/fit/6421e997a8037-951.jpg?v=12.3 768w,
                                https://s.kaufino.com/k/upload/offers/image/a468/300x300/fit/6421e997a8037-951.jpg?v=12.3 1740w
                            ">
                        <img src="/images/placeholder-870.png" data-srcset="
                                https://s.kaufino.com/k/upload/offers/image/a468/300x300/fit/6421e997a8037-951.jpg?v=12.3 768w,
                                https://s.kaufino.com/k/upload/offers/image/a468/300x300/fit/6421e997a8037-951.jpg?v=12.3 1740w
                            " data-sizes="auto" width="870" height="1190" alt="Lidl" class="lazyautosizes lazyloaded" sizes="645px" srcset="
                                https://s.kaufino.com/k/upload/offers/image/a468/300x300/fit/6421e997a8037-951.jpg?v=12.3 768w,
                                https://s.kaufino.com/k/upload/offers/image/a468/300x300/fit/6421e997a8037-951.jpg?v=12.3 1740w
                            ">
                    </picture>
                    {*
                    <picture id="" data-expand="100">                            
                        <source 
                            data-srcset="
                                {$leaflet->getPageByNumber($currentPage)->getImageUrl() |image:768,null,'fit','webp'} 768w,
                                {$leaflet->getPageByNumber($currentPage)->getImageUrl() |image:1740,null,'fit','webp'} 1740w
                            "                                 
                            type="image/webp"
                        >
                        <img                             
                            src="{$basePath}/images/placeholder-870.png"
                            data-srcset="
                                {$leaflet->getPageByNumber($currentPage)->getImageUrl() |image:768,null} 768w,
                                {$leaflet->getPageByNumber($currentPage)->getImageUrl() |image:1740,null} 1740w
                            "
                            data-sizes="auto"                                    
                            width="870"                                     
                            height="1190" 
                            alt="{$leaflet->getShop()->getName()}"                                 
                            class="lazyload"
                        >
                    </picture>  
                    *}                    
                </div>                                

                <div>        
                    
                </div>                  
                
                {*include 'paginator.latte', shop => $shop, leaflet => $leaflet, currentPage => $currentPage*}
                <div class="k-paginator__wrapper">
                    <a href="/lidl/lidl-48" class="active k-paginator__item">1</a>
                    <a href="/lidl/lidl-48?page=2" class="k-paginator__item">2</a>
                    <a href="/lidl/lidl-48?page=3" class="k-paginator__item">3</a>
                    <a href="/lidl/lidl-48?page=4" class="k-paginator__item">4</a>
                    <small class="k-paginator__separator">...</small>
                    <a class="k-paginator__item" href="/lidl/lidl-48?page=57">57</a>
                    <a class="k-paginator__button" href="/lidl/lidl-48?page=2">»</a>
                </div>
                
                <div>                
                    
                </div>

                
                <div class="px-3 px-lg-0">
                    <p class="color-grey fz-m lh-15 mb-3"><strong>strong</strong></p>
                    <p class="color-grey fz-m lh-15 mb-3">
                        text
                    </p>                    
                </div>                
            </div>        		            
        </div>

        <div class="leaflet__sidebar" style="height: auto !important;">
            <!-- oferto.com / halfpage1 -->
            {include "../components/halfpage1.latte"}                                           
            sidebar
            <div class="lf__box" n:if="isset($similarLeaflets) && count($similarLeaflets) > 0">
                <h3 class="lf__box-title mt-3 mt-md-0 px-3 px-lg-0">{_"$websiteType.leaflet.similarLeaflets", [brand => $leaflet->getShop()->getName()]}</h3>
                <div class="lf__box-wrapper">                    
                    <div n:foreach="$similarLeaflets as $similarLeaflet" class="lf__box-item lf__box-item--md-100 flex-direction-column flex-direction-lg-row mb-3">
                        <a class="lf__box-image-wrapper {if $similarLeaflet->isExpired()}expired{/if} lf__box-image--medium mb-3 mb-lg-0 mr-lg-3" n:href="Leaflet:leaflet $similarLeaflet->getShop(), $similarLeaflet">
                            <picture>
                                <source data-srcset="{$similarLeaflet->getFirstPage()->getImageUrl() |image:100,140,'exactTop','webp'}" type="image/webp">
                                <img src="{$basePath}/images/placeholder-100x140.png" data-src="{$similarLeaflet->getFirstPage()->getImageUrl() |image:100,140,'exactTop'}" width="100" height="140" alt="{$similarLeaflet->getName()}" class="img-responsive lazyload">
                            </picture> 
                        </a>
                        <p class="fz-xxs fz-sm-xs mb-0">
                            <a class="lf__box-link d-block color-black strong" n:href="Leaflet:leaflet $similarLeaflet->getShop(), $similarLeaflet">{$similarLeaflet->getName()}</a>
                            <small>{$similarLeaflet->getValidSince()|localDate} - {$similarLeaflet->getValidTill()|localDate:'long'}</small>
                        </p>
                    </div>
                </div>
            </div>                    

            <div class="lf__box lf__box-lg-border" n:if="isset($recommendedLeaflets)">
                <h3 class="lf__box-title px-3 px-lg-0">{_"$websiteType.leaflet.recommendedLeaflets"}</h3>
                <div class="lf__box-wrapper">
                    {foreach $recommendedLeaflets as $recommendedLeaflet}
                        {continueIf $recommendedLeaflet->getId() == $leaflet->getId()}
                        <div class="lf__box-item flex-direction-column flex-direction-lg-row mb-lg-3">
                            <a class="lf__box-image-wrapper {if $recommendedLeaflet->isExpired()} expired{/if} mb-3 mb-lg-0 mr-lg-3" n:href="Leaflet:leaflet $recommendedLeaflet->getShop(), $recommendedLeaflet">
                                <picture>
                                    <source data-srcset="{$recommendedLeaflet->getFirstPage()->getImageUrl() |image:100,140,'exactTop','webp'}" type="image/webp">
                                    <img src="{$basePath}/images/placeholder-100x140.png" data-src="{$recommendedLeaflet->getFirstPage()->getImageUrl() |image:100,140,'exactTop'}" width="100" height="140" alt="{$recommendedLeaflet->getName()}" class="img-responsive lazyload">
                                </picture>
                            </a>
                            <p class="fz-xxs fz-sm-xs mb-0">
                                <a class="lf__box-link d-block color-black strong" n:href="Leaflet:leaflet $recommendedLeaflet->getShop(), $recommendedLeaflet">{$recommendedLeaflet->getName()}</a>
                                <small>{$recommendedLeaflet->getValidSince()|localDate} - {$recommendedLeaflet->getValidTill()|localDate:'long'}</small>
                            </p>
                        </div>
                    {/foreach}
                </div>
            </div>                
            
            <div class="float-wrapper">                
                <!-- oferto.com / halfpage2 -->
                {include "../components/halfpage1.latte"}                                         
            </div>            
        </div>	     
    </div>	    

    <div class="float-wrapper__stop"></div>	
    
</div>


