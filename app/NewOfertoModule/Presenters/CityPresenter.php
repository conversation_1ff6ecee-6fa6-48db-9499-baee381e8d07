<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\NewOfertoModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Geo\Entities\City;
use <PERSON><PERSON><PERSON>\Model\Geo\GeoFacade;
use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Offers\Entities\Offer;
use <PERSON><PERSON><PERSON>\Model\Offers\OfferFacade;
use Ka<PERSON>ino\Model\Shops\Entities\Shop;
use Kaufino\Model\Shops\Entities\Store;
use Ka<PERSON>ino\Model\Shops\ShopFacade;
use Ka<PERSON>ino\Model\Shops\StoreFacade;
use Ka<PERSON>ino\Model\Websites\Entities\Website;

final class CityPresenter extends BasePresenter
{
	/** @var LeafletFacade @inject */
	public $leafletFacade;

	/** @var ShopFacade @inject */
	public $shopFacade;

	/** @var GeoFacade @inject */
	public $geoFacade;

    /** @var OfferFacade @inject */
    public $offerFacade;

    /** @var StoreFacade @inject */
    public $storeFacade;

	public function actionCity(City $city): void
	{
		if (!$city->isActiveOferto() && !$this->user->isLoggedIn()) {
            $this->redirectPermanent("Cities:cities");
		}

        $shops = $this->shopFacade->findLeafletShopsByCity($city, 50, true, Website::MODULE_OFERTO);

		$this->template->city = $city;
		$this->template->leaflets = $this->leafletFacade->findLeafletsByCity($city, null, 20);
		$this->template->shops = $shops;
		$this->template->nearestCities = $this->geoFacade->findNearestCitiesByCity($city, 36, Website::MODULE_OFERTO);

        $this->template->offers = $this->offerFacade->findOffersByShops($shops, 20, true, Offer::TYPE_LEAFLET);
	}

	public function actionShop(City $city, Shop $shop): void
	{
		if (!$shop->hasCity($city)) {
			$this->error('The shop is not located in the city.');
		}

        if ($shop->hasActiveCities() === false) {
            $this->redirect("City:city", ['city' => $city]);
        }

		if (!$city->isActiveOferto() && !$this->user->isLoggedIn()) {
			$this->redirectPermanent("Homepage:default");
		}

		if (!$shop->isActiveLeaflets()) {
			$this->redirectPermanent("City:city", ['city' => $city]);
		}

        if (!$shop->isStore()) {
			$this->redirectPermanent("Shop:shop", ['shop' => $shop]);
		}

        if ($shop->isActiveOferto() === false) {
            $this->redirect("City:city", ['city' => $city]);
        }

		$this->template->city = $city;
		$this->template->shop = $shop;
		$this->template->leaflets = $this->leafletFacade->findLeafletsByCity($city, $shop, 20);
		$this->template->shops = $this->shopFacade->findLeafletShopsByCity($city, 50, true, Website::MODULE_OFERTO);

		$nearestCities = $this->geoFacade->findNearestCitiesByCity($city, 50, Website::MODULE_OFERTO);
		$this->template->nearestCities = array_filter($nearestCities, static function (City $city) use ($shop) {
			return $city->isActiveBrandsOferto() && $shop->hasCity($city);
		});

        $similarLeaflets = null;
        if ($shop->getTag()) {
            $similarLeaflets = $this->leafletFacade->findLeafletsByTag($shop->getTag(), false, 30, Website::MODULE_OFERTO);
        }

        $this->template->similarLeaflets = array_filter($similarLeaflets ?? [], static function ($leaflet) use ($shop) {
            return $leaflet->getShop() !== $shop;
        });

        $this->template->cityStores = $this->geoFacade->findStoresByShopAndCity($shop, $city, 48, Website::MODULE_OFERTO);
	}

    public function renderStore(City $city, Shop $shop, ?Store $store = null): void
    {
        if ($store === null) {
            $this->redirect('City:shop', ['city' => $city, 'shop' => $shop]);
        }

        if ($store->getCity() !== $city) {
            $this->error('The store is not located in the city.');
        }

        if (!$city->isActive() && !$this->user->isLoggedIn()) {
            $this->redirectPermanent("Homepage:default");
        }

        if ($store->isActiveOferto() === false) {
            $this->redirectPermanent("City:shop", ['city' => $city, 'shop' => $shop]);
        }

        if ($shop->isActiveOferto() === false) {
            $this->redirect('City:city', ['city' => $city]);
        }

        $this->template->leaflets = $this->leafletFacade->findLeafletsByCity($city, $store->getShop(), 3);

        $this->template->city = $city;
        $this->template->store = $store;
        $this->template->shop = $shop;

        if ($shop->getTag()) {
            $similarShops = $this->shopFacade->findLeafletShopsByTag($shop->getTag(), $shop->isStore(), 18, $this->website->getModule());
            $similarLeaflets = $this->leafletFacade->findLeafletsByTag($shop->getTag(), false, 60, Website::MODULE_OFERTO);
        } else {
            $similarShops = $this->shopFacade->findTopLeafletShops($shop->getLocalization(), $shop->isStore(), 18, $this->website->getModule(), !$shop->isStore());
        }

        $this->template->similarShops = array_filter($similarShops, static function ($shop) use ($city) {
            return $shop->hasCity($city) && $city->isActiveBrandsOferto();
        });

        $this->template->similarLeaflets = array_filter($similarLeaflets ?? [], static function ($leaflet) use ($shop) {
            return $leaflet->getShop() !== $shop;
        });

        $this->template->nearestStores = $this->storeFacade->findNearestStores($store, 10, Website::MODULE_OFERTO);
        $this->template->nearestCities = $this->geoFacade->findNearestCitiesByCity($city, 10, Website::MODULE_OFERTO);

        $nearestCities = $this->geoFacade->findNearestCitiesByCity($city, 48, Website::MODULE_OFERTO);

        $this->template->cities = array_filter($nearestCities, static function (City $city) use ($shop) {
            return $shop->hasCity($city) && $city->isActiveBrandsOferto();
        });
    }
}
