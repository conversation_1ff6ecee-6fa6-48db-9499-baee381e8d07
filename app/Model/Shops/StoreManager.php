<?php

namespace <PERSON><PERSON><PERSON>\Model\Shops;

use <PERSON><PERSON><PERSON>\Model\EntityManager;
use Ka<PERSON>ino\Model\Geo\Entities\City;
use Ka<PERSON>ino\Model\Localization\Entities\Localization;
use Ka<PERSON>ino\Model\Shops\Entities\Shop;
use Ka<PERSON>ino\Model\Shops\Entities\Store;

class StoreManager
{
    private EntityManager $em;

    public function __construct(EntityManager $em)
    {
        $this->em = $em;
    }

    public function createStore(
        Localization $localization,
        Shop $shop,
        City $city,
        string $name,
        string $fullAddress,
        ?int $storeId,
        ?string $type,
        ?string $municipality,
        ?string $street,
        ?string $houseNumber,
        ?string $zipCode,
        ?string $lat,
        ?string $lng,
        ?string $slug
    ): Store
    {
        $store = new Store(
            $localization,
            $shop,
            $city,
            $name,
            $fullAddress,
            $storeId,
            $type,
            $municipality,
            $street,
            $houseNumber,
            $zipCode,
            $lat,
            $lng,
            $slug
        );

        return $this->saveStore($store);
    }

    public function saveStore(Store $store): Store
    {
        $this->em->persist($store);
        $this->em->flush();

        return $store;
    }
}
