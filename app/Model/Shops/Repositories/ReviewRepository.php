<?php

namespace <PERSON><PERSON><PERSON>\Model\Shops\Repositories;

use Doctrine\ORM\QueryBuilder;
use <PERSON><PERSON>ino\Model\EntityRepository;
use Kaufino\Model\Shops\Entities\Shop;

class ReviewRepository extends EntityRepository
{
	public function getReviews(): QueryBuilder
	{
		return $this->createQueryBuilder('r');
	}

	public function getReviewsByShop(Shop $shop): QueryBuilder
	{
		return $this->getReviews()
			->andWhere('r.shop = :shop')
			->setParameter('shop', $shop);
	}

	public function findCountOfShopReviews(Shop $shop)
	{
		return $this->getReviews()
			->andWhere('r.shop = :shop')
			->setParameter('shop', $shop)
			->select('COUNT(r.id)')
			->getQuery()
			->getSingleScalarResult();
	}

	public function findAverageShopReview(Shop $shop): ?float
	{
		$qb = $this->createQueryBuilder('r')
			->select('(sum(r.rate) / count(r.id)) AS averageReview')
			->andWhere('r.shop = :shop')
			->setParameter('shop', $shop);

		$result = $qb->getQuery()->getOneOrNullResult();

		return $result['averageReview'] ?? null;
	}
}