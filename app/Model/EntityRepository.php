<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model;

use Doctrine\ORM\EntityRepository as DoctrineEntityRepository;

abstract class EntityRepository extends DoctrineEntityRepository
{
	/**
	 * @param string $value
	 * @param string $key
	 * @return mixed[]
	 */
	public function findPairs($value, $key = 'id'): array
	{
		$select = [];
		$categories = $this->createQueryBuilder('e')
			->select('e.' . $key, 'e.' . $value)
			->getQuery()
			->getArrayResult();
		foreach ($categories as $category) {
			$select[$category[$key]] = $category[$value];
		}
		return $select;
	}
}
