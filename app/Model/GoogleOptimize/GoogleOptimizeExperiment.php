<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\GoogleOptimize;


class GoogleOptimizeExperiment
{
	/**
	 * @var string
	 */
	private $experimentId;

	/**
	 * @var int
	 */
	private $countOfVariants;

	/**
	 * @var int
	 */
	private $variant;

	public function __construct(string $experimentId, int $countOfVariants)
	{
		$this->experimentId = $experimentId;
		$this->countOfVariants = $countOfVariants;
	}

	/**
	 * @return string
	 */
	public function getExperimentId(): string
	{
		return $this->experimentId;
	}

	/**
	 * @return int
	 */
	public function getCountOfVariants(): int
	{
		return $this->countOfVariants;
	}

	/**
	 * @return int
	 */
	public function getVariant(): int
	{
		return $this->variant;
	}

	/**
	 * @param int $variant
	 */
	public function setVariant(int $variant): void
	{
		$this->variant = $variant;
	}
}
