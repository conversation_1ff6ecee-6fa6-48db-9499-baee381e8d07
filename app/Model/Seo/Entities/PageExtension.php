<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Seo\Entities;

use DateTime;
use <PERSON><PERSON>ino\Model\Localization\Entities\Localization;
use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON><PERSON>\Model\Log\Documentable;
use <PERSON><PERSON><PERSON>\Model\Websites\Entities\Website;

/**
 * @ORM\Entity(repositoryClass="Kaufino\Model\Seo\Repositories\PageExtensionRepository")
 * @ORM\Table(name="kaufino_seo_page_extension", uniqueConstraints={
 *     @ORM\UniqueConstraint(name="page_extension_unique", columns={"website_id", "slug"})}
 * )
 */
class PageExtension implements Documentable
{
	/**
	 * @var int
	 * @ORM\Column(type="integer", nullable=FALSE)
	 * @ORM\Id
	 * @ORM\GeneratedValue
	 */
	private $id;

	/**
	 * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Websites\Entities\Website")
	 * @ORM\JoinColumn(name="website_id", referencedColumnName="id")
	 */
	protected $website;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 */
	protected $slug;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	protected ?string $heading;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 */
	protected $shortDescription;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 */
	protected $longDescription;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 */
	protected $title;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 */
	protected $description;

	/**
	 * @ORM\Column(type="string", length=2048, nullable=true)
	 */
	protected $keywords;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private $createdAt;

	public function __construct(Website $website, string $slug)
	{
		$this->website = $website;
		$this->slug = $slug;
		$this->createdAt = new DateTime();
	}

	public function getId(): int
	{
		return $this->id;
	}

	public function setId(int $id): void
	{
		$this->id = $id;
	}

	public function getWebsite(): Website
	{
		return $this->website;
	}

	public function setWebsite(Website $website): void
	{
		$this->website = $website;
	}

	public function getSlug(): string
	{
		return $this->slug;
	}

	public function setSlug(string $slug): void
	{
		$this->slug = $slug;
	}

	public function getHeading(): ?string
	{
		return $this->heading ?: null;
	}

	public function setHeading(?string $heading): void
	{
		$this->heading = $heading ?: null;
	}

	public function getTitle()
	{
		return $this->title;
	}

	public function setTitle($title): void
	{
		$this->title = $title;
	}

	public function getDescription()
	{
		return $this->description;
	}

	public function setDescription($description): void
	{
		$this->description = $description;
	}

	/**
	 * @return DateTime
	 */
	public function getCreatedAt(): DateTime
	{
		return $this->createdAt;
	}

	/**
	 * @return mixed
	 */
	public function getShortDescription()
	{
		return $this->shortDescription;
	}

	/**
	 * @param mixed $shortDescription
	 */
	public function setShortDescription($shortDescription): void
	{
		$this->shortDescription = $shortDescription;
	}

	/**
	 * @return mixed
	 */
	public function getLongDescription()
	{
		return $this->longDescription;
	}

	/**
	 * @param mixed $longDescription
	 */
	public function setLongDescription($longDescription): void
	{
		$this->longDescription = $longDescription;
	}

	/**
	 * @return mixed
	 */
	public function getKeywords()
	{
		return $this->keywords;
	}

	/**
	 * @param mixed $keywords
	 */
	public function setKeywords($keywords): void
	{
		$this->keywords = $keywords;
	}

	public function toDocument(): string
	{
		$data = [
			'slug' => $this->getSlug(),
			'heading' => $this->getHeading(),
			'shortDescription' => $this->getShortDescription(),
			'longDescription' => $this->getLongDescription(),
			'title' => $this->getTitle(),
			'description' => $this->getDescription(),
			'keywords' => $this->getKeywords(),
		];

		$document = [];
		foreach ($data as $key => $value) {
			$document[] = $key . ":\n" . $value;
		}

		$document = implode("\n\n***\n\n", $document);

		return $document;
	}
}
