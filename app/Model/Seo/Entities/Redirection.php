<?php

namespace <PERSON><PERSON><PERSON>\Model\Seo\Entities;

use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="Kaufino\Model\Seo\Repositories\RedirectionRepository")
 * @ORM\Table(name="kaufino_seo_redirection")
 */
class Redirection
{
	/**
	 * @var int
	 * @ORM\Column(type="integer", nullable=FALSE)
	 * @ORM\Id
	 * @ORM\GeneratedValue
	 */
	private $id;

	/**
	 * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Websites\Entities\Website")
	 * @ORM\JoinColumn(name="website_id", referencedColumnName="id")
	 */
	protected $website;

	/**
	 * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Localization\Entities\Localization")
	 * @ORM\JoinColumn(name="localization_id", referencedColumnName="id")
	 */
	protected $localization;

	/**
	 * @ORM\Column(type="string")
	 */
	private $oldUrl;

	/**
	 * @ORM\Column(type="string")
	 */
	private $newUrl;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private $createdAt;

	public function getOldUrl()
	{
		return $this->oldUrl;
	}

	public function getLocalization()
	{
		return $this->localization;
	}

	public function getNewUrl()
	{
		return $this->newUrl;
	}
}