<?php

namespace <PERSON><PERSON><PERSON>\Model\Seo\Repositories;

use Doctrine\ORM\QueryBuilder;
use <PERSON><PERSON><PERSON>\Model\EntityRepository;
use <PERSON><PERSON><PERSON>\Model\Localization\Entities\Localization;
use <PERSON><PERSON><PERSON>\Model\Websites\Entities\Website;

class RedirectionRepository extends EntityRepository
{
	public function getPageExtensions(?Website $website = null, ?Localization $localization = null): QueryBuilder
	{
		$qb = $this->createQueryBuilder('r');

		if ($website) {
			$qb->andWhere('r.website = :website')
				->setParameter('website', $website);
		}

		if ($localization) {
			$qb->andWhere('r.localization = :localization')
				->setParameter('localization', $localization);
		}

		return $qb;
	}
}
