<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Azure;

use DateTime;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use <PERSON><PERSON><PERSON>\Model\Configuration;
use Nette\SmartObject;
use Nette\Utils\Image;
use Nette\Utils\Json;
use Ka<PERSON>ino\Model\Localization\Entities\Localization;
use Nette\Utils\JsonException;
use Psr\Http\Message\ResponseInterface;
use Tracy\Debugger;

class AzureClient
{
	use SmartObject;

	/** @var Configuration */
	private $configuration;

	public function __construct(Configuration $configuration)
	{
		$this->configuration = $configuration;
	}

	public function processOCR(string $imageUrl, Localization $localization = null)
	{
		$client = new Client();

        $model = 'latest';
        $version = 'v3.2';

        if ($localization->getLocale() === 'hr') {
            $version = 'v4.0';
        }

		$response = $client->post('https://westeurope.api.cognitive.microsoft.com/vision/' . $version . '/ocr?language=' . $this->getAzureLocale($localization) . '&detectOrientation=true&model-version=' . $model, [
			\GuzzleHttp\RequestOptions::JSON => ['url' => $imageUrl],
			'headers' => [
				'Ocp-Apim-Subscription-Key' => $this->configuration->getAzureApiToken(),
			],
		]);

		$json = (string) $response->getBody()->getContents();

		$ocrData = Json::decode($json, Json::FORCE_ARRAY);
		$imageContents = @file_get_contents($imageUrl);

		if ($imageContents === false) {
			return null;
		}

		$image = Image::fromString($imageContents);
		$imgWidth = $image->getWidth();
		$imgHeight = $image->getHeight();

		Debugger::dump($imgWidth);
		Debugger::dump($imgHeight);

		foreach ($ocrData['regions'] as $rKey => $region) {
			$ratios = $this->relativize($region['boundingBox'], $imgWidth, $imgHeight);
			$ocrData['regions'][$rKey]['boundingBox'] = implode(',', $ratios);

			Debugger::dump($ratios);

			foreach ($region['lines'] as $lKey => $line) {
				$ratios = $this->relativize($line['boundingBox'], $imgWidth, $imgHeight);
				$ocrData['regions'][$rKey]['lines'][$lKey]['boundingBox'] = implode(',', $ratios);

				foreach ($line['words'] as $wKey => $word) {
					$ratios = $this->relativize($word['boundingBox'], $imgWidth, $imgHeight);
					$ocrData['regions'][$rKey]['lines'][$lKey]['words'][$wKey]['boundingBox'] = implode(',', $ratios);
				}
			}
		}

		return Json::encode($ocrData);
	}

	private function getAzureLocale(?Localization $localization): string
	{
		if (!$localization) {
			return 'cs';
		}

		$locales = [
			'cz' => 'cs',
			'sk' => 'cs',
			'ro' => 'ro',
			'pl' => 'pl',
			'hu' => 'hu',
			'hr' => 'hr',
			'it' => 'it',
			'nl' => 'nl',
			'fr' => 'fr',
            'za' => 'en',
            'rs' => 'sr-Latn',
            'gr' => 'el',
            'de' => 'de',
		];

		if (!isset($locales[$localization->getRegion()])) {
			throw new \Exception('Unknown region for azure locale. Please, update locales array.');
		}

		return $locales[$localization->getRegion()];
	}

	public function detectProducts(string $imageUrl)
	{
		$client = new Client();

		$response = $client->post('https://westeurope.api.cognitive.microsoft.com/customvision/v3.4-preview/training/projects/69fc6580-5f92-4187-a30a-55d6870774c4/quicktest/url?iterationId=394f77bd-f2cd-4d3c-98f4-ae4bfe4363b9&store=true', [
			\GuzzleHttp\RequestOptions::JSON => ['url' => $imageUrl],
			'headers' => [
				'Training-Key' => $this->configuration->getAzureApiToken(),
			],
		]);

		Debugger::dump('request send');

		Debugger::dump($response);

		//Debugger::dump(Json::decode((string)$response->getBody()->getContents()));

		return (string)$response->getBody()->getContents();
	}

	/**
	 * @param $boundingBox
	 * @param int $imgWidth
	 * @param int $imgHeight
	 * @return false|string[]
	 */
	public function relativize($boundingBox, int $imgWidth, int $imgHeight)
	{
		list ($x, $y, $width, $height) = explode(',', $boundingBox);

		return [round($x / $imgWidth, 8), round($y / $imgHeight, 8), round($width / $imgWidth, 8), round($height / $imgHeight, 8)];
	}
}
