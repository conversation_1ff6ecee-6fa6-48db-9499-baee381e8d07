<?php

namespace <PERSON><PERSON><PERSON>\Model\Leaflets;

class Annotation
{
	private $x;

	private $y;

	private $width;

	private $height;

	private $tag;

	private $comment;

	public function __construct(float $x, float $y, float $width, float $height, ?string $tag, ?string $comment)
	{
		$this->x = $x;
		$this->y = $y;
		$this->width = $width;
		$this->height = $height;
		$this->tag = $tag;
		$this->comment = $comment;
	}

	/**
	 * @return float
	 */
	public function getX(): float
	{
		return $this->x;
	}

	/**
	 * @return float
	 */
	public function getY(): float
	{
		return $this->y;
	}

	/**
	 * @return float
	 */
	public function getWidth(): float
	{
		return $this->width;
	}

	/**
	 * @return float
	 */
	public function getHeight(): float
	{
		return $this->height;
	}

	/**
	 * @return string|null
	 */
	public function getTag(): ?string
	{
		return $this->tag;
	}

	/**
	 * @return string|null
	 */
	public function getComment(): ?string
	{
		return $this->comment;
	}

	/**
	 * @return string|null
	 */
	public function toArray(): array
	{
		return [
			'x' => $this->getX(),
			'y' => $this->getY(),
			'width' => $this->getWidth(),
			'height' => $this->getHeight(),
			'tag' => $this->getTag(),
			'comment' => $this->getComment(),
		];
	}

	public static function fromArray(array $data): Annotation
	{
		return new self($data['x'], $data['y'], $data['width'], $data['height'], $data['tag'], $data['comment']);
	}
}
