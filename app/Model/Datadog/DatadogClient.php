<?php

namespace <PERSON><PERSON><PERSON>\Model\Datadog;

use GuzzleHttp\Client;
use GuzzleHttp\RequestOptions;
use Nette\Http\Url;
use tipli\Model\Configuration;
use Tracy\Debugger;

class DatadogClient
{
    private const API_URL_LOGS = 'https://http-intake.logs.datadoghq.com/api/v2/logs';

    public function __construct(string $apiKey, string $appKey)
    {
        $this->apiKey = $apiKey;
        $this->appKey = $appKey;
    }

    public function sendLog(string $source, string $host, string $message, array $attributes = []): void
    {
        $attributes = array_merge(
            $attributes,
            [
                'service' => 'KLM',
                'host' => $host,
                'ddsource' => $source,
                'message' => $message,
            ]
        );

        $this->sendCurl(self::API_URL_LOGS, $attributes);
    }


    private function sendCurl(string $endpoint, array $dataArr): void
    {
        $client = new Client();

        try {
            $client->post($this->constructApiUrl($endpoint), [
                RequestOptions::JSON => $dataArr,
                RequestOptions::HEADERS => [
                    'DD-API-KEY' => $this->apiKey,
                ],
            ]);
        } catch (\Exception $e) {
            Debugger::log('Unexpected error: ' . $e->getMessage(), 'datadog-tracking-error');
        }
    }


    private function constructApiUrl(string $endpoint): string
    {
        $url = new Url($endpoint);

        $url->setQueryParameter('api_key', $this->apiKey);
        $url->setQueryParameter('application_key', $this->appKey);

        return $url->getAbsoluteUrl();
    }
}