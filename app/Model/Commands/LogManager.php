<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Commands;

use <PERSON><PERSON><PERSON>\Model\EntityManager;
use <PERSON><PERSON>ino\Model\Commands\Entities\Log;

class LogManager
{
	/** @var EntityManager */
	private $em;

	public function __construct(EntityManager $em)
	{
		$this->em = $em;
	}

	public function createLog($command, $note = null): Log
	{
		$log = new Log($command, $note);

		return $this->saveLog($log);
	}

	public function finishLog(Log $log, $note = null): Log
	{
		$log->finish($note);

		return $this->saveLog($log);
	}

	public function saveLog(Log $log): Log
	{
		$this->em->persist($log);
		$this->em->flush();

		return $log;
	}
}
