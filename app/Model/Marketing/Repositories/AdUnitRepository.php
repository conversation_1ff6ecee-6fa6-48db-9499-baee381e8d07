<?php

namespace <PERSON><PERSON><PERSON>\Model\Marketing\Repositories;

use Doctrine\ORM\QueryBuilder;
use <PERSON><PERSON><PERSON>\Model\EntityRepository;
use <PERSON><PERSON><PERSON>\Model\Localization\Entities\Localization;

class AdUnitRepository extends EntityRepository
{
    public function getAdUnits(): QueryBuilder
    {
        return $this->createQueryBuilder('au');
    }

    public function findAdUnitsByChannel(Localization $localization, string $channel)
    {
        return $this->getAdUnits()
            ->andWhere('au.channel = :channel')
            ->andWhere('au.active = 1')
            ->andWhere('au.localization = :localization')
            ->setParameter('localization', $localization)
            ->setParameter('channel', $channel)
            ->andWhere('au.elementId IS NOT NULL')
            ->getQuery()
            ->getResult();
    }
}
