<?php

namespace <PERSON><PERSON><PERSON>\Model\Log\Subscribers;

use <PERSON><PERSON><PERSON>\Model\Configuration;
use <PERSON><PERSON><PERSON>\Model\Github\GithubClient;
use <PERSON><PERSON><PERSON>\Model\Seo\Events\PageExtensionNewVersionCreatedEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

final class VersionSubscriber implements EventSubscriberInterface
{
	/** @var GithubClient */
	private $githubClient;

	/** @var Configuration */
	private $configuration;

	public function __construct(
		GithubClient $githubClient,
		Configuration $configuration
	) {
		$this->githubClient = $githubClient;
		$this->configuration = $configuration;
	}

	public static function getSubscribedEvents(): array
	{
		return [
			PageExtensionNewVersionCreatedEvent::class => 'trackPageExtensionNewVersion',
		];
	}

	public function trackPageExtensionNewVersion(PageExtensionNewVersionCreatedEvent $pageExtensionNewVersionCreatedEvent)
	{
		$pageExtension = $pageExtensionNewVersionCreatedEvent->getPageExtension();
		$user = $pageExtensionNewVersionCreatedEvent->getUser();

		if ($this->configuration->isDevelopmentMode()) { // @todo @Jirka tady je potreba dodelat detekci testovaciho rezimu
			return;
		}

		$path = implode(
			DIRECTORY_SEPARATOR,
			[
				$pageExtension->getWebsite()->getLocalization()->getLocale(),
				'pageExtension',
				$pageExtension->getId() . '-' . $pageExtension->getSlug() . '.txt',
			]
		);

		$this->githubClient->updateFile(
			GithubClient::REPOSITORY_KAUFINO_CONTENT,
			$path,
			$pageExtension->toDocument(),
			GithubClient::DEFAULT_BRANCH,
			$user->getEmail() . ' (' . $user->getId() . ')'
		);
	}
}
