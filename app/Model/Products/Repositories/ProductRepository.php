<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Products\Repositories;

use Doctrine\ORM\QueryBuilder;
use <PERSON><PERSON>ino\Model\EntityRepository;
use <PERSON><PERSON>ino\Model\Localization\Entities\Localization;

class ProductRepository extends EntityRepository
{
	public function getProducts(Localization $localization = null): QueryBuilder
	{
		$qb = $this->createQueryBuilder('p');

		if ($localization) {
			$qb->andWhere('p.localization = :localization')->setParameter('localization', $localization);
		} else {
			$qb->leftJoin('p.localization', 'l');
		}

		return $qb;
	}

	public function findProductBySlug(Localization $localization, string $slug)
	{
		$qb = $this->getProducts($localization)
			->andWhere('p.slug = :slug')
			->setParameter('slug', $slug);

		return $qb->getQuery()->getOneOrNullResult();
	}

	public function findProductByName(Localization $localization, string $name)
	{
		$qb = $this->getProducts($localization)
			->andWhere('p.name = :name')
			->setParameter('name', $name);

		return $qb->getQuery()->getOneOrNullResult();
	}

	public function findProductsByFulltext($keyword, Localization $localization)
	{
		return $this->getProducts($localization)
			->andWhere('p.name LIKE :keywordS OR p.name LIKE :keywordE OR p.name LIKE :keywordSE')
			->setParameter('keywordS', '' . $keyword . ' %')
			->setParameter('keywordE', '% ' . $keyword . '')
			->setParameter('keywordSE', '%' . $keyword . '%')
			->getQuery();
	}
}
