<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\ApiModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Marketing\MarketingFacade;
use Nette;
use <PERSON><PERSON><PERSON>\Presenters\BasePresenter;
use <PERSON><PERSON><PERSON>\Model\Shops\ShopFacade;
use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;

class HomepagePresenter extends BasePresenter
{
	/** @var ShopFacade */
	private $shopFacade;

	/** @var LeafletFacade */
	private $leafletFacade;

	/** @var MarketingFacade */
	private $marketingFacade;

	public function __construct(ShopFacade $shopFacade, LeafletFacade $leafletFacade, MarketingFacade $marketingFacade)
	{
		parent::__construct();

		$this->shopFacade = $shopFacade;
		$this->leafletFacade = $leafletFacade;
		$this->marketingFacade = $marketingFacade;
	}

	public function actionDefault()
	{
		$this->sendResponse(
			new Nette\Application\Responses\JsonResponse(['status' => 'OKOKOK'])
		);
	}

	public function actionShops()
	{
		$shops = $this->shopFacade->findLeafletShops($this->localization, false, null, $this->website->getModule());

		$results = [];
		foreach ($shops as $shop) {
			if (!$shop->getLogoUrl()) {
				continue;
			}

			$results[] = [
				'id' => $shop->getId(),
				'name' => $shop->getName(),
				'logoUrl' => $shop->getLogoUrl(),
			];
		}

		$this->sendResponse(
			new Nette\Application\Responses\JsonResponse($results)
		);
	}

	public function actionLeaflets()
	{
		$leaflets = $this->leafletFacade->findLeaflets();

		$results = [];
		foreach ($leaflets as $leaflet) {
			$firstPage = $leaflet->getFirstPage();
			$results[] = [
				'id' => $leaflet->getId(),
				'shopId' => $leaflet->getShop()->getId(),
				'name' => $leaflet->getName(),
				'imageUrl' => $firstPage->getImageUrl() === null ? null : ($this->imageFilter)($firstPage->getImageUrl(), 536, 536, 'exactTop'),
				'validTill' => $leaflet->getValidTill()->format('Y-m-d H:i:s'),
			];
		}

		$this->sendResponse(
			new Nette\Application\Responses\JsonResponse($results)
		);
	}

	public function actionLeaflet()
	{
		$leaflets = $this->leafletFacade->findLeaflets();

		$result = [];
		foreach ($leaflets as $leaflet) {
			$firstPage = $leaflet->getFirstPage();
			$result[] = [
				'id' => $leaflet->getId(),
				'shopId' => $leaflet->getShop()->getId(),
				'name' => $leaflet->getName(),
				'imageUrl' => $firstPage->getImageUrl() === null ? null : ($this->imageFilter)($firstPage->getImageUrl(), 536, 536, 'exactTop'),
				'validTill' => $leaflet->getValidTill()->format('Y-m-d H:i:s'),
			];

			foreach ($leaflet->getPages() as $page) {
				$result['pages'][] = [
					'id' => $page->getId(),
					'imageUrl' => $page->getImageUrl() === null ? null : ($this->imageFilter)($page->getImageUrl(), 870, null, 'fit'),
				];
			}

			break;
		}

		$this->sendResponse(
			new Nette\Application\Responses\JsonResponse($result)
		);
	}

	public function actionAddEmail()
	{
		$status = Nette\Http\IResponse::S200_OK;
		$message = 'ok';

		$body = $this->getHttpRequest()->getRawBody();

		if (!$body) {
			$status = Nette\Http\IResponse::S400_BAD_REQUEST;
			$message = 'request body is empty';

			$this->sendResponse(
				new Nette\Application\Responses\JsonResponse(['status' => $status, 'message' => $message])
			);
		}

		$body = Nette\Utils\Json::decode($body);

		if (Nette\Utils\Validators::isEmail($body->email) === false) {
			$status = Nette\Http\IResponse::S400_BAD_REQUEST;
			$message = 'invalid email address';

			$this->sendResponse(
				new Nette\Application\Responses\JsonResponse(['status' => $status, 'message' => $message])
			);
		}

		if ($this->marketingFacade->findEmail($body->email) === null) {
			$shop = null;

			if ($shopId = $body->shopId) {
				$shop = $this->shopFacade->findShop($shopId);
			}

			$this->marketingFacade->createEmail($body->email, $shop);
		}

		$this->sendResponse(
			new Nette\Application\Responses\JsonResponse(['status' => $status, 'message' => $message])
		);
	}

	public function actionClearResponseCache(string $t = null)
	{
		if ($this->getParameter('token') !== 'MlaPXRji59') {
			$this->sendJson(['error' => 1]);
		}

		if ($t === null) {
			$this->sendJson(['error' => 2]);
		}

		$this->responseCacheManager->clearCacheByTag($t, false);

		$this->sendJson(['status' => 'ok']);
	}
}
