navbar:
	shops: "Butiker"
	leaflets: "Broschyrer"
	search:
		placeholder: "<PERSON>ök butiker"
		submit: "Sök"

	moreShops: "Andra butiker"
	home: "Hem"

footer:
	copyright: "Letado Alla rättigheter reserverade."	
	shops: "Butiker"
	category: "Kategorier"	
	aboutLetado: "Om Letado"
	cookies: "Cookies"
	leaflets: "Broschyrer"
	aboutUs: "Om oss"
	nextCountries: "Andra länder"

search:
	title: "Sökresultat \"%query%\""
	noResults: "Hur mycket vi än letar kan vi inte hitta något.."

homepage:
	title: "Senaste broschyrer och varor på rea"
	text: "De senaste broschyrerna med ett brett utbud av varor på rea från stora återförsäljare"
	allLeaflets: "Alla broschyrer"
	shops: "Butiker"
	allShops: "Alla butiker"	

leaflets:
	title: "Broschyrer"
	text: "De senaste broschyrerna. Vi lägger till broschyrer för dig varje dag så att du alltid kan hitta specialprodukter."

leaflet:
	metaTitle: "Senaste %brand% broschyr giltig från %validSince%"
	metaTitlePageSuffix: "sida %page%"
	metaDesc: "Senaste broschyren från %brand% gäller från &nbsp;%validSinceDay% %validSince%."
	leaflet: "%brand%"	
	desc: "Senaste reklambladet från %leafletBrandLink% gäller från %validSince% till %validTill%. På sidan %leafletPageCount% hittar du alla aktuella rabatter. På Letado-sidan hittar du alltid aktuell information om alla broschyrer som erbjuds från dina favoritbutiker."
	longDesc1: "Dra nytta av specialerbjudanden från %leafletBrandLink%, som du hittar i den aktuella kampanjbroschyren från %validSince% till %validTill%. Allt blir dyrare och dyrare idag - bilar, flyg, semester, resor, elektronik, vitvaror, men också kläder och mycket mer. Men du behöver inte ta ett konsumtionslån eller annat lån för dina vanliga månatliga utgifter. På Letada strävar vi efter att ge dig rabatter från alla de mest populära butikerna så snart som möjligt. Så du kan dra nytta av de senaste kampanjerna eller rabatterna och spara pengar från din hushållsbudget."
	longDesc2: "Hos oss behöver du inte anlita en finansiell rådgivare för att hjälpa dig med din ekonomi eftersom vi kan göra det åt dig. De pengar som blir över kan du sedan använda till saker som utlandssemestrar, resor till lokala hotell och pensionat eller som en ekonomisk buffert för nästa avbetalning på ditt bolån."
	longDesc3: "Det är en härlig känsla att vara ekonomiskt oberoende och ha ett överskott av pengar. Det innebär också att du har råd med bra kvalitetsförsäkringar, oavsett om det är livförsäkring, hemförsäkring eller obligatorisk försäkring och haveriskydd. Detta skyddar din ekonomi från oväntade influenser som kan ha en betydande negativ inverkan på den. Försäkringar skyddar därför stabiliteten i din ekonomi."	
	longDesc4: "Vi på Letado kommer att fortsätta göra allt vi kan för att hjälpa dig att spara så mycket pengar som möjligt på dina vardagliga inköp så att du har råd att köpa din drömbil, favoritkläder, elektronik eller betala för en kvalitetsförsäkring. Vi hoppas att detta %leafletBrandLink% flygblad, giltigt från %validSince% till %validTill%, hjälper dig åtminstone lite och tar dig närmare dina drömmar!"
	smallTitle: "%brand% giltigt från"	
	recommendedLeaflets: "Populära broschyrer"
	similarLeaflets: "Andra broschyrer %brand%"
	backToLeaflets: "Tillbaka till listan över alla broschyrer"	
	allBrandLeaflets: "Alla broschyrer %brand%"
	goToShop: "Gå till butik"

shops:
	title: "Butiker"
	text: "Ett urval av de mest populära återförsäljarna vars nya reklamblad vi ger dig varje dag."

shop:
	leaflets: "broschyrer"
	text: "Den senaste %brand% broschyren med fynd."
	button: "Gå till butik %brand%"	
	noLeaflets: "Vi letar efter ditt senaste flygblad... Vänligen försök igen senare."
	otherShops: "Andra butiker."
	defaultTitleSuffic: '%shopName% - senaste flygbladet, varor på rea'
	otherLeaflets: "Andra broschyrer %brand%"
	type:
		shopTitle: "{$shopName|upper} broschyr {if $currentLeafletFromDate} från {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}{/if} + nästa veckas broschyr om åtgärder"
		eshopTitle: "%brand% rabatt"
		eshop: "Kolla in de senaste %brand% kampanjerna i deras katalog full av inspiration och fynd. De senaste %brand% rabatterna är alltid tillgängliga, så att du aldrig missar rabatterade varor."
	metaTitles:
	     withFutureLeaflet: "{$shopName|upper} flygblad nästa vecka från {$nextLeafletFromDate|dayGenitive} {$nextLeafletFromDate|date:'j.n.Y'} + aktuell broschyr"
	     withCurrentLeaflet: "{$shopName|upper} broschyr nästa vecka + aktuell broschyr från {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}"
	     withoutCurrentAndFutureLeaflet: "{$shopName|upper} broschyr nästa vecka + aktuell kampanjbroschyr online"
	metaDescriptions:
	    withFutureLeaflet: "{$shopName|upper} flygblad nästa vecka ✅ Bläddra i det speciella {$shopName|upper} NÄSTA VECKANS BREV från {$nextLeafletFromDate|dayGenitive} {$nextLeafletFromDate|date:'j.n.Y'}. Den aktuella PDF-filen av veckans {$shopName|upper} flygblad finns också tillgänglig online."
	    withCurrentLeaflet: "{$shopName|upper} flyer nästa vecka ✅ Bläddra i den speciella {$shopName|upper} FLYER för nästa vecka. Den aktuella PDF-filen av veckans {$shopName|upper}-flyer finns också tillgänglig online från {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}."
	    withoutCurrentAndFutureLeaflet: "{$shopName|upper} flyer nästa vecka ✅ Bläddra i den speciella {$shopName|upper} FLYER för nästa vecka. Det aktuella PDF-flyerbladet {$shopName|upper} med veckans kampanjer finns också tillgängligt online."

tag:
	text: "Erbjudande om de senaste broschyrerna från kategorin %tag%."
	noLeaflets: "Vi söker för dig den senaste flyer ... Vänligen försök igen senare."
	otherShops: "Andra butiker"	

about:
	title: "Om oss"
	text: "Vårt mål är att spara tid och pengar åt användarna. Varje dag ger vi dig aktuella reklamblad från de mest populära återförsäljarna och sparar dig tid när du letar efter specialerbjudanden på produkter."
	address: "Business Animals s.r.o. <br> Na Poříčí 1067/25 <br> 110 00 Praha - Nové Město <br><br> IČ: 02734699"

city:
	city:
		title: "Flyers %city%"
		text: "Current promotional leaflets %city%. In the sale leaflets in %city% you will find not only goods on sale but also many other discounts. Discounts can be found in %stores% and other stores."
		text2: "Promotional leaflets and current discounts in %city%. In the leaflets of popular shops in %city% you will find not only goods on sale but also many other discounts and offers at the best prices. Browse the flyers of the biggest stores in %city%, such as %stores%."
		h2: "Flyers, promotions and discounts from shops in %city%"
		leafletStores:
			title: "leaflet %brand%"
			store: "leaflet %brand% %city%"
			storeWithCity: "leaflet %brand% %city%"
		otherShops: "Stores in the city %city%"
		nearestCity: "Other cities with leaflets in the area"
		nearestCityWithShop: "Other cities with leaflets %shopName% in the area"
		categoriesInText: "%category% leaflets"
		citiesInText: "leaflets %city%"
		generatedText:
			1: "The city %city% with a population of %population% people offers countless shops, for which we bring you new flyers every week. Interesting promotions and discounts are prepared not only for the city %city%, but also for other nearby cities such as %cities%."
			2: "A list of the current flyers of the biggest shops in %city% can be found here:"
			3: "There are also promotional flyers for stores %stores% and many more. You can find their promotions for month %month% in the %shopsLink% section."
			leaflet: "Flyer %brand% current %validSince% - %validTill%"
			and: "and"
			or: "or"
	store:
		h1: %brand% %address%
		h2: "Stores %brand% in %city%"
		title: "%brand% %address%, leaflet and opening hours 🕔"
		description: "Compare the offers in the leaflets, find out the exact address and opening hours, or read about the range of products waiting for you at the store %brand% %address%."
		open: "Open"
		closed: "Closed"
		text: "Special offers and discounts on a wide range of products are regularly offered by the %brand% %address% store, where you can use the popular %brand% leaflet when making a purchase."
		text2WithoutStores: "Customers can conveniently view this online, as well as promotions available at other branches."
		text2: "Customers can conveniently view this online, as well as the promotions available at %stores% branches."
		or: nebo
		others: "and others"
		textBottom: "The %brand% %address% shop offers customers not only a wide range of goods but also low prices, which are regularly advertised in the %brand% leaflet. The %fullAddress% branch is a popular place for those looking for great deals. Thanks to the fact that the %brand% leaflet is available online, shoppers always have the latest discounts at hand. If %brand% %address% doesn't offer everything a shopper needs, they can also take advantage of other nearby stores such as:"
		textBottom2: "Find out the exact address, contact details, or opening hours of your favorite stores all in one place. There is also information on which branches are located in your area and where you can take advantage of other good deals, which are also announced in the promotional leaflets of selected stores."
		sections:
			leaflets: Egyéb szórólapok a kategóriából
			shops: Egyéb üzletek a környéken
			stores: Egyéb %brand% üzletek a környéken
	shop:
		title: "Leaflets %brand% %city%"
		h2: "Flyers, promotions, and discounts in the store %brand% %city%"
		text: "Promotional leaflets %brand% %city% and their current discounts and promotions. In the %brand% %city% leaflet you will find a wide range of products at the best prices. However, %brand% is not the only store in %city%. Other popular stores include %stores%."
		leafletStores:
			title: "%brand% leaflet %city%"
			store: "%brand% %city%"
		cityLink: "leaflets %city%"
		shopLink: "leaflets %shop%"
		otherShops: "Other shops in the city %city%"
		shopLeaflet: "Leaflets %brand%"
		citiesInText: "%brand% leaflet %city%"
		offers: "Offers from flyers %brand% %city%"
		generatedText:
			1: "The %brand% store in the city %city% offers promotional leaflets that we regularly update for you every week."
			2: "If you are looking for a new %brand% leaflet, you can check it out by clicking on this link: <a href=\"%actualLeafletUrl%\">Current %brand% %city% leaflet</a>. The validity of the leaflet is from %validSince% to %validTill%. Each leaflet contains exciting special offers and discounts, seasonal promotions or club prices, and a wide range of merchandise."
			3: "However, store %brand% is not only located in the city %city%. You can also find leaflets for the store %brand% in other nearby stores %stores%. All the promotional flyers are available in the section <a href=\"%leafletsUrl%\">Letáky.</a>"
			4: "If you're looking for other stores, popular ones include %stores%."
