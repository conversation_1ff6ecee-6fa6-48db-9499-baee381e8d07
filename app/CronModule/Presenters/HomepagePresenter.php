<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\CronModule\Presenters;

use <PERSON><PERSON><PERSON>\Commands\ProcessCities;
use <PERSON><PERSON><PERSON>\Commands\ProcessCitiesFromFile;
use <PERSON><PERSON><PERSON>\Commands\ProcessCoupons;
use <PERSON><PERSON><PERSON>\Commands\ProcessLeafletPages;
use <PERSON><PERSON><PERSON>\Commands\ProcessProducts;
use Ka<PERSON>ino\Commands\ProcessReviews;
use Ka<PERSON><PERSON>\Commands\ProcessShops;
use Ka<PERSON>ino\Commands\ProcessStores;
use Ka<PERSON><PERSON>\Commands\ProcessTags;
use Ka<PERSON><PERSON>\Commands\ProcessTopLeaflets;
use Ka<PERSON><PERSON>\Commands\RemoveOfferImages;
use Nette;
use Ka<PERSON><PERSON>\Commands\ProcessLeaflets;
//use <PERSON><PERSON><PERSON>\Commands\ProcessLeafletsPages;
use <PERSON><PERSON><PERSON>\Presenters\BasePresenter;

class HomepagePresenter extends BasePresenter
{
	use TSecuredEndpoint;

	/** @var ProcessLeaflets @inject */
	public $processLeaflets;

	/** @var ProcessLeafletPages @inject */
	public $processLeafletPages;

	/** @var ProcessShops @inject */
	public $processShops;

	/** @var ProcessTopLeaflets @inject */
	public $processTopLeaflets;

	/** @var ProcessCoupons @inject */
	public $processCoupons;

	/** @var ProcessTags @inject */
	public $processTags;

	/** @var ProcessCities @inject */
	public $processCities;

	/** @var ProcessProducts @inject */
	public $processProducts;

	/** @var ProcessReviews @inject */
	public $processReviews;

    /** @var ProcessStores @inject */
    public $processStores;

    /** @var ProcessCitiesFromFile @inject */
    public $processCitiesFromFile;

    /** @var RemoveOfferImages @inject */
    public $removeOfferImages;

	public function actionDefault()
	{
		$this->sendResponse(
			new Nette\Application\Responses\JsonResponse(['status' => 'OK'])
		);
	}

    public function actionRemoveOfferImages()
    {
        $this->removeOfferImages->start();

        $this->terminate();
    }

	public function actionProcessLeaflets(?int $localizationId = null)
	{
		$this->processLeaflets->start($localizationId);

		$this->terminate();
	}

	public function actionProcessLeafletPages()
	{
		$this->processLeafletPages->start();

		$this->terminate();
	}

	public function actionProcessShops()
	{
		$this->processShops->start();

		$this->terminate();
	}

	public function actionProcessTopLeaflets()
	{
		$this->processTopLeaflets->start();

		$this->terminate();
	}

	public function actionProcessCoupons()
	{
		$this->processCoupons->start($this->getParameter('localizationId'));

		$this->terminate();
	}

	public function actionProcessProducts()
	{
		$this->processProducts->start();

		$this->terminate();
	}

	public function actionProcessTags()
	{
		$this->processTags->start();

		$this->terminate();
	}

	public function actionProcessCities()
	{
		$this->processCities->start();

		$this->terminate();
	}

	public function actionProcessReviews()
	{
		$this->processReviews->start();

		$this->terminate();
	}

    public function actionProcessStores()
    {
        $this->processStores->start();

        $this->terminate();
    }

    public function actionProcessCitiesFromFile()
    {
        $this->processCitiesFromFile->start();

        $this->terminate();
    }
}
