<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\OfertoComModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Offers\OfferFacade;
use <PERSON><PERSON><PERSON>\Model\Shops\ShopFacade;

final class SearchPresenter extends BasePresenter
{
	/** @var ShopFacade @inject */
	public $shopFacade;

	/** @var OfferFacade @inject */
	public $leafletFacade;

	public function actionSearch(?string $q = null): void
	{
		$this->template->query = $q;
		$this->template->shops = $this->shopFacade->findCouponShopsByFulltext($this->localization, $q, true, 10, $this->website->getModule());
		// $this->template->offers = $this->leafletFacade->findOffersByFulltext($q, $this->localization, $this->website->getType());
	}
}
