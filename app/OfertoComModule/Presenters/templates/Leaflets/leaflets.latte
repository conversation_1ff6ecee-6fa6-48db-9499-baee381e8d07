{block scripts}
    {include parent}
{/block}

{block description}{_ofertocom.leaflets.metaDesc}{/block}

{block content}
<div class="container">
    <div class="mb-6">
        <h1 n:block="title" class="k__title ta-center mt-5 mb-4">{_ofertocom.leaflets.title}</h1>
        <p class="k__text ta-center mw-700 mb-0">{_ofertocom.leaflets.text}</p>						    
    </div>

    <div class="k-leaflets__wrapper k-leaflets__wrapper--xs-mx">
        {foreach $leaflets as $leaflet}
            <div class="k-leaflets__item mb-5">                
                <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" class="k-leaflets__link {if $leaflet->isExpired()}expired{/if} mb-3">
                    <picture>
                        <source data-srcset="{$leaflet->getFirstPage()->getImageUrl() |image:230,288,'exactTop','webp'}" type="image/webp">
                        <img src="{$basePath}/images/placeholder-230x288.png" data-src="{$leaflet->getFirstPage()->getImageUrl() |image:230,288,'exactTop'}" width="230" height="288" alt="{$leaflet->getName()}" class="k-leaflets__image lazyload">
                    </picture>                        
                </a>
                <div class="k-leaflets__title mt-0 mb-0">
                    <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" class="color-black">
                        {$leaflet->getName()}
                    </a>
                </div>                
                <p class="k-leaflets__date mt-0 mb-0">{$leaflet->getValidSince()|localDate} – {$leaflet->getValidTill()|localDate:'long'}</p>
            </div>            
        {/foreach}
    </div>
</div>
