{if isset($city) &&  $shop->isStore() && $shop->hasCity($city)}
<a n:href="City:shop $city, $shop" class="k-shop__item" title="{_kaufino.shop.storeLeaflet, [brand => $shop->getName()]}">
{else}
<a n:href="Shop:shop $shop" class="k-shop__item" title="{_kaufino.shop.storeLeaflet, [brand => $shop->getName()]}">
{/if}
    <span class="k-shop__image-wrapper">
        <picture>                        
            <source 
                data-srcset="
                    {$shop->getLogoUrl() |image:120,120,'fit','webp'} 120w,
                    {$shop->getLogoUrl() |image:240,240,'fit','webp'} 240w
                " 
                type="image/webp"
            >                                    
            <img 
                src="{$basePath}/images/placeholder-120x120.png" 
                data-srcset="
                    {$shop->getLogoUrl() |image:120,120,'fit','png'} 120w,
                    {$shop->getLogoUrl() |image:240,240,'fit','png'} 240w
                "
                data-sizes="120px"
                width="120" 
                height="120" 
                alt="{$shop->getName()}" 
                class="lazyload"
            >
        </picture>                    
    </span>
    <small class="k-shop__title">{$shop->getName()}</small>

    {if $user->isLoggedIn()}
        {var $countOfCurrentAndFutureLeaflets = count($shop->getCurrentAndFutureLeaflets())}
        {var $countOfCurrentAndFutureNewsletter = count($shop->getCurrentAndFutureNewsletter())}
        {var $countOfValidCoupons = count($getCountOfCouponsByShop($shop))}
        {var $countOfValidProducts = count($getCountOfProductsByShop($shop))}

        <span class="k-shop__bubble green">
            L: {$countOfCurrentAndFutureLeaflets} / N: {$countOfCurrentAndFutureNewsletter} / K: {$countOfValidCoupons} / P: {$countOfValidProducts}
        </span>

        {var $countOfExpiredLeaflets = count($shop->getExpiredLeaflets())}
        {var $countOfExpiredNewsletters = count($shop->getExpiredNewsletters())}
        {var $countOfExpiredCoupons = count($getCountOfExpiredCouponsByShop($shop))}
        {var $countOfExpiredProducts = count($getCountOfExpiredProductsByShop($shop))}

        <span class="k-shop__bubble green" style="margin-top: 22px">
            L: {$countOfExpiredLeaflets} / N: {$countOfExpiredNewsletters} / K: {$countOfExpiredCoupons} / P: {$countOfExpiredProducts}
        </span>
    {/if}
</a>