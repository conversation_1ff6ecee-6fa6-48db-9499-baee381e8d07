<div class="k-offers__item">
    <div class="k-offers__inner">
        <a n:href="Exit:offer $product" class="k-offers__image-wrapper">
            <picture>
                <source 
                    data-srcset="
                        {$product->getImageUrl()|image:150,150,'fit'} 150w,
                        {$product->getImageUrl()|image:300,300,'fit'} 300w
                    " 
                    type="image/webp"
                >            
                <img 
                    src="{$basePath}/images/pixel.png"                             
                    data-srcset="
                        {$product->getImageUrl()|image:150,150,'fit'} 150w,
                        {$product->getImageUrl()|image:300,300,'fit'} 300w
                    "
                    data-sizes="auto"
                    width="150" 
                    height="150" 
                    alt="{$product->getName()}" 
                    class="k-offers__image lazyload"
                >
            </picture>      

            <span class="product-tag__wrapper">                
                <span n:if="$product->getPercentageDiscount()" class="product-tag__item danger">Sleva -{$product->getPercentageDiscount()}%</span>                
                <span n:if="$product->isInStock()" class="product-tag__item success">Skladem</span>
            </span>                           
        </a>
        

        <div class="k-offers__content">
            <small class="k-offers__small">
                {if !isset($hideShop)}
                    <a n:href="Shop:shop $product->getShop()">
                        {$product->getShop()->getName()}
                    </a>
                {/if}                
            </small>            
            <p class="k-offers__title">                            
                <a n:href="Exit:offer $product">{$product->getName()}</a>
            </p>            
            <div class="k-offers__price">                                        
                <div class="k-offers__right">                    
                    <small n:if="$product->getCommonPrice() > 0">{$product->getCommonPrice()|price:$product->getLocalization()}</small>
                    <strong>{$product->getCurrentPrice()|price:$product->getLocalization()}</strong>
                </div>
            </div>
        </div>
    </div>
</div>