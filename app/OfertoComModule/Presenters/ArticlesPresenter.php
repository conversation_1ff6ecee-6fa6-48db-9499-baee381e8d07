<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\OfertoComModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Articles\ArticleFacade;

final class ArticlesPresenter extends BasePresenter
{
	/** @var ArticleFacade @inject */
	public $articleFacade;

	public function actionArticles(): void
	{
		$this->template->articles = $this->articleFacade->findArticles($this->website, 20);
	}
}
