{capture $cityLink}<a href="{link City:city $city}">{_kaufino.city.shop.cityLink, [city => $city->getName()]}</a>{/capture}
{capture $shopLink}<a href="{link Shop:shop $shop}">{_kaufino.city.shop.shopLink, [shop => $shop->getName()]}</a>{/capture}


{var $uniqueStores = []}
{var $i = 0}
{foreach $shops as $_shop}
    {continueIf $_shop == $shop}
    {var $uniqueStores[$_shop->getId()] = $_shop}
    {var $i = $i + 1}
    {breakIf $i > 2}
{/foreach}

{var $countOfStores = count($uniqueStores)}
{var $stores = []}

{capture $stores}
    {if $countOfStores}
        {foreach $uniqueStores as $store}
            {if $iterator->isLast() && $countOfStores > 1} {_kaufino.city.city.generatedText.and} {/if}
            <a n:href="City:shop $city, $store">{_kaufino.city.shop.leafletStores.store, [brand => $store->getName(), city => $city->getName()]}</a>{if $iterator->getCounter() < $countOfStores-1 && $countOfStores > 2}, {/if}
        {/foreach}
    {/if}
{/capture}

{block head}
    {include parent}
    <script n:syntax="double">        
        window.dataLayer.push({
            'content_group' : 'CityShop',
            'country' : {{$localization->getRegion()}}
        });
    </script>
{/block}

{block title}
    {if $pageExtension && $pageExtension->getTitle()}
        {$pageExtension->getTitle()}
    {else}
        {_kaufino.city.shop.metaTitle, [city => $city->getName(), brand => $shop->getName()]}
    {/if}    
{/block}

{block description}
    {if $pageExtension && $pageExtension->getDescription()}
        {$pageExtension->getDescription()}
    {else}
{_kaufino.city.shop.metaDescription, [city => $city->getName(), brand => $shop->getName(), cityLink => $cityLink, stores =>  ($stores |spaceless|stripHtml|trim), shopLink => $shopLink]|noescape}
    {/if}
{/block}

{block breadcrumb}
    <div class="k-breadcrumb__container">
        <p class="k-breadcrumb">            
            <a n:href="Leaflets:leaflets" class="link">{_kaufino.navbar.leaflets}</a> |
            <a n:href="City:city $city" class="link">{$city->getName()}</a> |
            <span class="color-grey">{$shop->getName()}</span>
        </p>
    </div>
{/block}

{block content}

<div class="leaflet k-lf-layout k-lf-layout--fixed-container">
    <div class="container d-block">
        <div class="leaflet__content w100">
            <div class="w100">                
                <div class="k-profile-header k-profile-header--sm-center">                    
                    <a href="{link Shop:shop $shop}" title="{_kaufino.city.shop.shopLeaflet, [brand => $shop->getName()]}" class="k-profile-header__logo-wrapper k-profile-header__logo-wrapper--smaller">
                        <picture>
                            <source 
                                srcset="
                                    {$shop->getLogoUrl() |image:80,80,'fit','webp'} 1x,
                                    {$shop->getLogoUrl() |image:160,160,'fit','webp'} 2x
                                " 
                                type="image/webp"
                            >                            
                            <img 
                                src="{$basePath}/images/placeholder-80x70.png" 
                                srcset="
                                    {$shop->getLogoUrl() |image:80,80} 1x,
                                    {$shop->getLogoUrl() |image:160,160} 2x
                                " 
                                width="80" 
                                height="80" 
                                alt="{$shop->getName()}" 
                                class="k-profile-header__logo"
                            >
                        </picture>							
                    </a>
                    
                    <div class="k-profile-header__content">
                        <h1 class="k-profile-header__title">
                            {if $pageExtension && $pageExtension->getHeading()}
                                {$pageExtension->getHeading()}
                            {else}
                                {_kaufino.city.shop.title, [city => $city->getName(), brand => $shop->getName()]}
                            {/if}                            
                        </h1>
                                                
                    </div>                                       
                </div>   

                {if count($leaflets) > 0}
                    <div class="k-leaflets__wrapper">
                        {foreach $leaflets as $leaflet}                            
                            {include '../components/leaflet.latte', leaflet => $leaflet}
                        {/foreach}
                    </div>
                {else}
                    <div class="alert alert-info mx-3">{_kaufino.tag.noLeaflets}</div>
                {/if}                

                {if count($similarLeaflets) > 0}
                    <h2 class="fz-xl fw-regular mb-5 px-3 px-lg-0">{_kaufino.city.store.sections.leaflets}</h2>

                    <div class="k-leaflets__wrapper k-leaflets__wrapper--5">
                        {foreach $similarLeaflets as $leaflet}
                            {breakIf $iterator->getCounter() > 10}
                            {include '../components/leaflet.latte', leaflet => $leaflet, cssClass => $iterator->counter > 18 ? 'hidden' : ''}
                        {/foreach}
                    </div>
                {/if}

                <div n:if="count($shops) > 1" class="">
                    <h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_kaufino.city.shop.otherShops, [city => $city->getName()]}</h2>
                    <div class="k-shop">
                        {foreach $shops as $_shop}
                            {continueIf $_shop == $shop}
                            {include '../components/shop-logo.latte', shop => $_shop, cssClass => $iterator->counter > 6 ? 'hidden' : ''}
                        {/foreach}
                    </div>

                    <p n:if="count($shops) > 5" class="d-flex mb-5">
                        <button class="link ml-auto k-show-more-button js-show-shop">{_'kaufino.showMore.shops'} »</button>
                    </p>
                </div>

                <div n:if="count($cityStores) > 0">
                    <h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_'kaufino.city.shop.storesTitle', ['brand' => $shop->getName(), 'city' => $city->getName()]}</h2>

                    <div class="k-tag">
                        {foreach $cityStores as $store}
                            <span class="k-tag__inner {$iterator->counter > 12 ? 'hidden'}">
                                <a n:href="City:store $city, $store->getShop(), $store" class="k-tag__item">{$shop->getName()} {$store->getFullStreet()}</a>
                            </span>
                        {/foreach}
                    </div>

                    <p n:if="count($cityStores) > 11" class="d-flex mb-5">
                        <button class="link ml-auto k-show-more-button js-show-tag">{_'kaufino.city.city.storesMoreButton'}</button>
                    </p>
                </div>

                <div n:if="count($nearestCities)">
                    <h2 class="fz-xl fw-regular mb-5 px-3 px-lg-0">
                        {_kaufino.city.city.nearestCityWithShop, [shopName =>$shop->getName()]}
                    </h2>

                    <p class="k-tag mb-5">
                        {foreach $nearestCities as $nearestCity}
                            <span class="k-tag__inner {$iterator->counter > 12 ? 'hidden'}">
                                <a n:href="City:shop $nearestCity, $shop" class="k-tag__item">{$shop->getName()} {$nearestCity->getName()}</a>
                            </span>

                            {breakIf $iterator->getCounter() > 12}
                        {/foreach}
                    </p>
                </div>

                <div n:if="count($nearestCities)">
                    <h2 class="fz-xl fw-regular mb-5 px-3 px-lg-0">
                        {_kaufino.city.city.nearestCity}
                    </h2>

                    <p class="k-tag mb-4">
                        {foreach $nearestCities as $nearestCity}
                            {breakIf $iterator->getCounter() > 24}

                            <span class="k-tag__inner {$iterator->counter > 12 ? 'hidden'}">
                                <a n:href="City:city $nearestCity" class="k-tag__item">{$nearestCity->getName()}</a>
                            </span>
                        {/foreach}
                    </p>
                    <p n:if="count($nearestCities) > 11" class="d-flex mb-5">
                        <button class="link ml-auto k-show-more-button js-show-tag js-show-all-btn">{_'kaufino.showMore.cities'} »</button>
                        <a n:href="Cities:cities" class="link ml-auto hidden k-show-more-button js-all-btn">{_'kaufino.showMore.allCities'} »</a>
                    </p>
                </div>

                <p class="k-profile-header__text ml-0">     
                    {if $pageExtension && $pageExtension->getShortDescription()}
                        {$pageExtension->getShortDescription()}
                    {else}
                        {_kaufino.city.shop.text, [city => $city->getName(), cityLink => $cityLink, brand => $shop->getName(), shopLink => $shopLink, stores => ($stores |trim)]|noescape|spaceless|replace: ' .', '.'}
                    {/if}
                </p>                        

                <div class="k-content ml-0">
                    {if $pageExtension && $pageExtension->getLongDescription()}                    
                        {$pageExtension->getLongDescription()|content|noescape}                    
                    {else}
                        <h2>{_kaufino.city.shop.h2, [brand => $shop->getName(), city => $city->getName()]}</h2>
                    {/if}

                    {var $currentLeaflet = count($leaflets) ? $leaflets[0] : null}
                    {if $currentLeaflet}
                        {capture $currentLeafletValidSince}{$currentLeaflet->getValidSince()|localDate:'long'}{/capture}
                        {capture $currentLeafletValidTill}{$currentLeaflet->getValidTill()|localDate:'long'}{/capture}
                        {capture $currentLeafletUrl}{link Leaflet:leaflet $currentLeaflet->getShop(), $currentLeaflet}{/capture}
                    {/if}

                    {capture $citiesInText |spaceless|trim}
                        {foreach $nearestCities as $_city}
                            {skipIf $_city == $city}
                            {skipIf $shop->hasCity($_city) === false}

                            {breakIf $iterator->getCounter() >= 4}

                            <a n:href="City:shop $_city, $shop" title="{_kaufino.city.shop.citiesInText, [brand => $shop->getName(), city => $city->getName()]}">{$shop->getName()} {$_city->getName()}</a>{if $iterator->getCounter() < 3}, {/if}
                        {/foreach}
                    {/capture}

                    {capture $storesInText |spaceless|trim}
                        {var $uniqueStores = []}
                        {var $i = 0}
                        {foreach $shops as $_shop}
                            {continueIf $_shop == $shop}
                            {continueIf !$_shop->hasCity($city)}
                            {var $uniqueStores[$_shop->getId()] = $_shop}
                            {var $i = $i + 1}
                            {breakIf $i > 3}
                        {/foreach}

                        {var $countOfStores = count($uniqueStores)}
                        {foreach $uniqueStores as $store}
                            {if $iterator->isLast() && $countOfStores > 1} a {/if}
                            <a n:href="Shop:shop $store" title="{_kaufino.city.shop.leafletStores.title, [brand => $store->getName(), city => $city->getName()]}">{$store->getName()}</a>{if $iterator->getCounter() < $countOfStores-1 && $countOfStores > 2}, {/if}
                        {/foreach}
                    {/capture}

                    {capture $leafletsUrl |spaceless|trim}{link Leaflets:leaflets}{/capture}

                    {dump $citiesInText}

                    <p>{_kaufino.city.shop.generatedText.1, [brand => $shop->getName(), city => $city->getName()]|noescape}</p>
                    
                    <p n:if="$currentLeaflet">{_kaufino.city.shop.generatedText.2, [brand => $currentLeaflet->getShop()->getName(), city => $city->getName(), validSince => $currentLeafletValidSince, validTill => $currentLeafletValidTill, actualLeafletUrl => $currentLeafletUrl]|noescape}</p>
                    
                    <p>{_kaufino.city.shop.generatedText.3, [city => $city->getName(), brand => $shop->getName(), stores => $citiesInText, leafletsUrl => $leafletsUrl]|noescape}</p>
                    
                    <p>{_kaufino.city.shop.generatedText.4, [brand => $shop->getName(),city => $city->getName(), domain => $shop->getDomain(), cities => $citiesInText, stores => $storesInText]|noescape}</p>
                </div>
            </div>
        </div>

    </div>

    <div class="float-wrapper__stop"></div>
</div>
