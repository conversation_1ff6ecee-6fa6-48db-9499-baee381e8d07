<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\KaufinoSubdomainModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Leaflets\Entities\Leaflet;
use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Offers\Entities\Offer;
use <PERSON><PERSON><PERSON>\Model\Offers\OfferFacade;
use Ka<PERSON>ino\Model\Shops\Entities\Shop;
use Ka<PERSON>ino\Model\Shops\ShopFacade;
use Ka<PERSON>ino\Model\Tags\Entities\Tag;

final class OffersPresenter extends BasePresenter
{
	/** @var OfferFacade @inject */
	public $offerFacade;

	public function actionOffers(): void
	{
        $this->error('Not found', 404);
        $offerTags = $this->tagFacade->findTags($this->localization, Tag::TYPE_OFFERS, 100);

		$this->template->offersTags = $offerTags;

        $this->template->offers = $this->offerFacade->findTopOffersByTags($this->localization, $offerTags);
	}
}
