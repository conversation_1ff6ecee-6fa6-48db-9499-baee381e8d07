{var $currentLeaflet = $shop->getCurrentLeaflet()}
{var $nextLeaflet = $shop->getNextLeaflet()}

{var $parameters = [
'currentLeafletFromDate' => $currentLeaflet ? $currentLeaflet->getValidSince() : null,
'currentLeafletToDate' => $currentLeaflet ? $currentLeaflet->getValidTill() : null,
'nextLeafletFromDate' => $nextLeaflet ? $nextLeaflet->getValidSince() : null,
'shopName' => $shop->getName(),
]}

{block title}
    {if $pageExtension && $pageExtension->getTitle()}
        {$seoGenerator->renderInSandbox($pageExtension->getTitle(), $parameters)}
    {else}
        {$metaTitle}
    {/if}
{/block}

{block description}{if $pageExtension && $pageExtension->getDescription()}{$seoGenerator->renderInSandbox($pageExtension->getDescription(), $parameters)}{else}{$metaDescription}{/if}{/block}

{block head}
    {include parent}          
    
    <!-- oferto.com / sticky -->
    {include "../components/sticky.latte"}                                        
{/block}

{block scripts}
    {include parent}

    <script n:if="$faqContentBlocks" type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "FAQPage",
            "mainEntity": [
                {foreach $faqContentBlocks as $faq} {
                    "@type": "Question",
                    "name": {$faq->getHeading()},
                    "acceptedAnswer": {
                        "@type": "Answer",
                        "text": {strip_tags($faq->getContent())}
                    }
                }{sep},{/sep}
        {/foreach}
            ]
        }
    </script>
{/block}

{block content}

{var $actualLeaflets = 4}
{var $actualShops = 30}
{var $actualActiveLeaflets = 42}

<div class="leaflet k-lf-layout k-lf-layout--fixed-container o-leaflet-brand">
    <div class="container">	
        <div class="leaflet__content">
            <div class="w100">

                <div class="k-profile-header k-profile-header--sm-center">                    
                    <a n:href="Shop:shop $shop" class="k-profile-header__logo-wrapper k-profile-header__logo-wrapper--smaller">
                        <picture>
                            <source data-srcset="{$shop->getLogoUrl() |image:160,140,'fit','webp'}" type="image/webp">
                            <img src="{$basePath}/images/placeholder-80x70.png" data-src="{$shop->getLogoUrl() |image:160,140}" width="160" height="140" alt="{$shop->getName()}" class="lazyload k-profile-header__logo">
                        </picture>							
                    </a>
                    
                    <div class="k-profile-header__content">
                        <h1 class="k-profile-header__title">
                            {if $pageExtension && $pageExtension->getHeading()}
                                {var $heading = $getHeadingFromPageExtension($pageExtension)}
                                {$heading}
                            {else}
                                {$heading1}
                            {/if}                                                        
                        </h1>                                                                    
                    </div>                                       
                </div>                      

                {* Letáky *}
                {if count($leaflets) > 0}
                    <div class="k-leaflets__wrapper mt-3">                        
                        {if false}
                            {if $website->hasAdSense()}        
                                <div class="k-leaflets__item k-leaflets__item--first mb-5">              
                                    <!-- oferto.com / mobile_rectangle1 -->
                                    {include "../components/mobile_rectangle1.latte"}
                                </div> 
                            {/if}  
                        {/if}  

                        {foreach $leaflets as $leaflet}
                            {breakIf $leaflet->isExpired() && $iterator->getCounter() > 5}
                            
                            <div class="k-leaflets__item mb-5">
                                <div class="k-leaflets__item mb-5">
                                    <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" class="k-leaflets__link {if $leaflet->isExpired()}expired{/if} mb-3">
                                        {dump $leaflet->getId()}
                                        <picture n:if="$leaflet->getFirstPage()">
                                            <source data-srcset="{$leaflet->getFirstPage()->getImageUrl() |image:230,288,'exactTop','webp'}" type="image/webp">
                                            <img src="{$basePath}/images/placeholder-230x288.png" data-src="{$leaflet->getFirstPage()->getImageUrl() |image:230,288,'exactTop'}" width="230" height="288" alt="{$leaflet->getName()}" class="k-leaflets__image lazyload">
                                        </picture>
                                    </a>
                                    <div class="k-leaflets__title mt-0 mb-0">
                                        <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" class="color-black">
                                            {if $leaflet->isChecked() === false}
                                                {_kaufino.leaflet.titleUnChecked, [brand => $leaflet->getName()]|noescape}
                                            {else}
                                                {$leaflet->getName()}
                                            {/if}
                                        </a>
                                    </div>
                                    <p class="k-leaflets__date mt-0 mb-0">{$leaflet->getValidSince()|localDate} – {$leaflet->getValidTill()|localDate:'long'}</p>
                                    <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" class="k-leaflets__button mt-3">{_"$websiteType.shop.showLeaflet"}</a>
                                </div>
                            </div>
                            

                            {if $iterator->getCounter() == 2}
                                <div n:if="$website->hasAdSense()" class="k-leaflets__item k-leaflets__item--mobile mb-5">
                                    <!-- oferto.com / mobile_rectangle2 -->
                                    {include "../components/mobile_rectangle2.latte"}
                                </div>
                            {/if}
                        {/foreach}                        
                    </div>

                    {if $expiredLeaflets}
                        <p class="d-flex">
                            <a n:href="Archive:archive, $shop" class="link ml-auto">{_"$websiteType.leaflets.expiredMetaTitle", [brand => $shop->getName()]} »</a>
                        </p>
                    {/if}
                {elseif (!$shop->isEshop())}
                    <div class="alert alert-info mx-3">{_"$websiteType.shop.noLeaflets"}</div>
                {/if}                                                          
                
                <!-- oferto.com / pr_native1 -->
                {include "../components/pr_native1.latte"}                

                <!-- oferto.com / pr_native3 -->
                {include "../components/pr_native3.latte"}                

                {*
                <div class="mt-4 mb-5">                                                         
                    <p class="k__text mw-900 mb-0 ml-0">{_"$websiteType.shop.bottomText", [brand => $shop->getName(), actualLeaflets => $actualLeaflets]|noescape}</p>
                </div>                 
                *}

                {if $contentBlocksAllowed}
                    {foreach $contentBlocks as $contentBlock}
                        {continueIf $contentBlock->getType() === 'legacy'}

                        <div class="k-content k__text mw-900 mb-5 ml-0" n:if="$contentBlock->getContent()">
                            <h2 n:if="$contentBlock->getheading()">{$contentBlock->getHeading()}</h2>

                            {$contentBlock->getContent() |content|noescape}
                        </div>
                    {/foreach}
                {else}
                    <p class="k-profile-header__text mw-900 ml-0">
                        {if $pageExtension && $pageExtension->getShortDescription()}
                            {$pageExtension->getShortDescription()}
                        {else}
                            {_"$websiteType.shop.text", [brand => $shop->getName()]|noescape}
                        {/if}
                    </p>

                    {if $pageExtension && $pageExtension->getLongDescription()}
                        <div class="k-content k__text mw-900 mb-5 ml-0">
                            {$pageExtension->getLongDescription()|content|noescape}
                        </div>
                    {/if}
                {/if}

                <div n:if="count($similarShops)-1 > 0" class="">
                    <h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_"$websiteType.shop.otherShops.title"}</h2>
                    <p class="k__text mw-900 mb-5 ml-0">{_"$websiteType.shop.otherShops.text", [brand => $shop->getName(), actualShops => $actualShops, actualActiveLeaflets => $actualActiveLeaflets]|noescape}</p>

                    <div class="k-shop">    
                        {foreach $similarShops as $similarShop}
                            {continueIf $similarShop->getId() == $shop->getId()}
                            <a n:href="Shop:shop $similarShop" class="k-shop__item">
                                <span class="k-shop__image-wrapper">
                                    <picture>
                                        <source data-srcset="{$similarShop->getLogoUrl() |image:80,70,'fit','webp'}" type="image/webp">
                                        <img src="{$basePath}/images/placeholder-80x70.png" data-src="{$similarShop->getLogoUrl() |image:80,70}" width="80" height="70" alt="{$similarShop->getName()}" class="lazyload">
                                    </picture>                                    
                                </span>
                                <small class="k-shop__title">{$similarShop->getName()}</small>
                            </a>
                        {/foreach}
                    </div>
                </div>                              
            </div>
        </div>                      
                
    </div>	    

	<div class="float-wrapper__stop"></div>	
</div>


<script type="application/ld+json" n:if="count($leaflets)">
    {
        "@context": "http://schema.org",
        "itemListElement": [
            {foreach $leaflets as $leaflet}
                {
                    "endDate": {$leaflet->getValidTill()->format('Y-m-d')},
                    "startDate": {$leaflet->getValidSince()->format('Y-m-d')},
                    "location": {
                        "address": {
                            "name": {$leaflet->getName()},
                            "@type": "PostalAddress"
                        },
                        "url": {link //:Oferto:Shop:shop $leaflet->getShop()},
                        "image": {$shop->getLogoUrl() |image:160,140,'fit','webp'},
                        "name": {$leaflet->getShop()->getName()},
                        "@type": "Place"
                    },
                    "performer": {
                        "name": {$leaflet->getShop()->getName()},
                        "@type": "Organization"
                    },
                    "image": {$leaflet->getFirstPage() ? $leaflet->getFirstPage()->getImageUrl() |image:60,84,'exactTop','webp' : ""},
                    "name": {$leaflet->getName()},
                    "url": {link //:Oferto:Leaflet:leaflet, $leaflet->getShop(), $leaflet},
                    "description": "",
                    "eventAttendanceMode": "https://schema.org/OfflineEventAttendanceMode",
                    "eventStatus": "https://schema.org/EventScheduled",
                    "organizer": {
                        "@type": "Organization",
                        "name": {$leaflet->getShop()->getName()},
                        "url": {link //:Oferto:Shop:shop $leaflet->getShop()}
                    },
                    "@type": "SaleEvent"
                }{sep},{/sep}
    {/foreach}
        ],
        "@type": "OfferCatalog"
    }
</script>