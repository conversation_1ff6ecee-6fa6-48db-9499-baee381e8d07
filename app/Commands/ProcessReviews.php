<?php

namespace <PERSON><PERSON><PERSON>\Commands;

use <PERSON><PERSON><PERSON>\Model\Shops\ReviewFacade;
use <PERSON><PERSON><PERSON>\Model\Shops\ShopFacade;

class ProcessReviews extends Job
{
	private ShopFacade $shopFacade;
	private ReviewFacade $reviewFacade;

	public function __construct(ShopFacade $shopFacade, ReviewFacade $reviewFacade)
	{
		parent::__construct();

		$this->shopFacade = $shopFacade;
		$this->reviewFacade = $reviewFacade;
	}

	protected function configure(): void
	{
		$this->setName('kaufino:process-reviews');
	}

	public function start(): void
	{
		$log = $this->onStart();

		$this->processReviews();

		$this->onFinish($log);
	}

	private function processReviews()
	{
		$shopsToAddReview = $this->shopFacade->findShopsToProcessReviews();

		foreach ($shopsToAddReview as $shop) {
			if (rand(1, 3) !== 3) {
				continue;
			}

			$this->reviewFacade->createReview($shop, rand(4, 5));
		}
	}
}
