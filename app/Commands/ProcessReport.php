<?php

namespace <PERSON><PERSON><PERSON>\Commands;

use <PERSON><PERSON>ino\Model\Articles\ArticleFacade;
use <PERSON><PERSON><PERSON>\Model\Geo\GeoFacade;
use <PERSON><PERSON>ino\Model\Offers\OfferFacade;
use <PERSON><PERSON>ino\Model\Shops\ShopFacade;
use <PERSON><PERSON><PERSON>\Model\Websites\Entities\Website;
use <PERSON><PERSON><PERSON>\Model\Websites\WebsiteFacade;

class ProcessReport extends Job
{
    private WebsiteFacade $websiteFacade;
    private ShopFacade $shopFacade;
    private OfferFacade $offerFacade;
    private GeoFacade $geoFacade;
    private ArticleFacade $articleFacade;

    private const WEBSITE_TYPES_TO_PROCESS = [Website::MODULE_KAUFINO, Website::MODULE_LETADO, Website::MODULE_OFERTO];

    public function __construct(WebsiteFacade $websiteFacade, ShopFacade $shopFacade, OfferFacade $offerFacade, GeoFacade $geoFacade, ArticleFacade $articleFacade)
    {
        parent::__construct();

        $this->websiteFacade = $websiteFacade;
        $this->shopFacade = $shopFacade;
        $this->offerFacade = $offerFacade;
        $this->geoFacade = $geoFacade;
        $this->articleFacade = $articleFacade;
    }

    protected function configure(): void
    {
        $this->setName('kaufino:process-report:run');
    }

    public function start(): void
    {
        $log = $this->onStart();

        $this->processReports();

        $this->onFinish($log);
    }

    private function processReports()
    {
        $websitesToProcess = $this->websiteFacade->getWebsites()
            ->andWhere('w.active = true')
            ->orderBy('w.localization', 'ASC')
            ->andWhere('w.module IN (:types)')
            ->setParameter('types', self::WEBSITE_TYPES_TO_PROCESS)
            ->getQuery()
            ->getResult()
        ;

        /** @var Website $website */
        foreach ($websitesToProcess as $website) {
            $shops = $this->shopFacade->findShopsByWebsiteType($website->getLocalization(), $website->getModule());

            $countOfShops = count($shops);
            $countOfOffers = count($this->offerFacade->findOffersByShops($shops, null));
            $countOfStores = count($this->geoFacade->findStores($website->getLocalization(), null));
            $countOfArticles = count($this->articleFacade->findArticles($website));

            if ($website->isLetado() || $website->isOfertoCom()) {
                $countOfActiveCities = 0;
                $countOfShopsInCities = 0;
                $countOfActiveShopsInCities = 0;
                $countOfActiveStores = 0;
            } else {
                $cities = $this->geoFacade->findCitiesByLocalization($website->getLocalization());
                $countOfActiveCities = count($this->geoFacade->findCities($website->getLocalization(), null, $website->getModule()));
                $countOfShopsInCities = $this->shopFacade->getCountOfShopsInCities($cities);
                $countOfActiveShopsInCities = $this->shopFacade->getCountOfActiveShopsInCities($cities);
                $countOfActiveStores = count($this->geoFacade->findStores($website->getLocalization(), null, $website->getModule()));
            }

            $website->setCountOfShops($countOfShops);
            $website->setCountOfOffers($countOfOffers);
            $website->setCountOfActiveCities($countOfActiveCities);
            $website->setCountOfShopsInCities($countOfShopsInCities);
            $website->setCountOfActiveShopsInCities($countOfActiveShopsInCities);
            $website->setCountOfStores($countOfStores);
            $website->setCountOfActiveStores($countOfActiveStores);
            $website->setCountOfArticles($countOfArticles);

            $this->websiteFacade->save($website);
        }
    }
}
