<?php

namespace <PERSON><PERSON><PERSON>\KaufinoModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Seo\Entities\Redirection;
use <PERSON><PERSON><PERSON>\OfertoComModule\Presenters\BasePresenter;

class RedirectionPresenter extends BasePresenter
{
	public function actionRedirection(Redirection $redirection)
	{
		$this->redirectUrl($this->getHttpRequest()->getUrl()->getHostUrl() . '/' . $redirection->getLocalization()->getRegion() . '/' . $redirection->getNewUrl(), 301);
	}
}
