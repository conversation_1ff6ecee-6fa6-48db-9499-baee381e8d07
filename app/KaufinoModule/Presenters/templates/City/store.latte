{block head}
    {include parent}

    <!-- Leaflet -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>

    <script n:syntax="double">        
        window.dataLayer.push({
            'content_group' : 'CityStore',
            'country' : {{$localization->getRegion()}}
        });
    </script>
{/block}

{block title}
    {if $pageExtension && $pageExtension->getTitle()}
        {$pageExtension->getTitle()}
    {else}
        {_kaufino.city.store.title, [city => $city->getName(), brand => $shop->getName(), address => $store->getFullStreet()]}
    {/if}
{/block}

{block description}
    {if $pageExtension && $pageExtension->getDescription()}
        {$pageExtension->getDescription()}
    {else}
        {_kaufino.city.store.description, [brand => $shop->getName(), address => $store->getFullStreet(), city => $city->getName()]}
    {/if}
{/block}

{block robots}{if $city->isNoIndex()}noindex, nofollow{else}index,follow{/if}{/block}

{block breadcrumb}
    <div class="k-breadcrumb__container">
        <p class="k-breadcrumb">
            <a n:href="Shops:shops" class="link">{_kaufino.navbar.shops}</a> |
            <a n:href="City:city $city" class="link">{$city->getName()}</a> |
            {if $city->isActiveBrandsKaufino() && $shop->hasActiveCities()}
                <a n:href="City:shop $city, $shop" class="link">{$shop->getName()} {$city->getName()}</a>  |
            {else}
                <a n:href="Shop:shop $shop" class="link">{$shop->getName()}</a>  |
            {/if}
            <span class="color-grey">{$store->getFullStreet()}</span>
        </p>
    </div>
{/block}

{block scripts}
    {include parent}

    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "Store",
            "name": {_"kaufino.city.store.h1", [city => $city->getName(), brand => $shop->getName(), address => $store->getFullStreet()]},
        "url": {link //this},
        "address": {
            "@type": "PostalAddress",
            "streetAddress": {$store->getFullStreet()},
            "addressLocality": {$store->getCity()->getName()},
            "addressRegion": {$store->getCity()->getDistrict()},
            "postalCode": {$store->getZipCode()},
            "addressCountry": {$store->getLocalization()->getRegion() |upper}
        },
        "openingHoursSpecification": {$store->getOpeningHoursSchemaSpecification()},
        "geo": {
            "@type": "GeoCoordinates",
            "latitude": {$store->getLat()},
            "longitude": {$store->getLng()}
        }
    }
    </script>
{/block}

{define cities}
    <div n:if="count($cities) > 0" class="nearest-store-branch">
        <h2 class="fz-xl fw-regular mb-5 px-3 px-lg-0">{_kaufino.city.store.sections.stores, [brand => $shop->getName()]}</h2>

        <p class="k-tag k-tag--4 mb-5">
            {foreach $cities as $cityItem}
                <span class="k-tag__inner {$iterator->counter > 4 ? 'hidden' : ''}">
                    <a n:href="City:shop $cityItem, $shop" class="k-tag__item">{$shop->getName()} {$cityItem->getName()}</a>
                </span>
            {/foreach}
        </p>

        <p n:if="count($cities) > 3" class="d-flex mt-3 mb-5">
            <button class="link ml-auto k-show-more-button js-show-tag js-show-all-btn">{_'kaufino.showMore.cities'} »</button>
            <a n:href="Cities:cities" class="link ml-auto hidden k-show-more-button js-all-btn">{_'kaufino.showMore.allCities'} »</a>
        </p>        
    </div>
{/define}

{block content}
{capture $otherStores |spaceless|trim}
    {foreach $nearestStores as $nearesStore}
        <a n:href="City:store $nearesStore->getCity(), $shop, $nearesStore">{_'kaufino.city.store.store', [fullAddress => $nearesStore->getFullAddress()] |replace: ' ', '&nbsp'|noescape}</a>

        {if $iterator->counter === 1}
            {if count($nearestStores) > 1}
                {_'kaufino.city.store.or'}
            {else}
                {_'kaufino.city.store.others'}
            {/if}
        {/if}

        {breakIf $iterator->counter > 1}
    {/foreach}
{/capture}

<div class="leaflet k-lf-layout k-lf-layout--fixed-container">
    <div class="container">
        <div class="storeContent w100">
            <div class="storeContent__main mb-5">
                <div class="k-profile-header k-profile-header--sm-center my-0">
                    <a href="{if $city->isActiveBrandsKaufino() && $shop->hasActiveCities()}{link City:shop $city, $shop}{else}{link Shop:shop $shop}{/if}" title="{_kaufino.city.shop.shopLeaflet, [brand => $shop->getName()]}" class="k-profile-header__logo-wrapper k-profile-header__logo-wrapper--smaller">
                        <picture>
                            <source
                                srcset="
                                    {$shop->getLogoUrl() |image:80,80,'fit','webp'} 1x,
                                    {$shop->getLogoUrl() |image:160,160,'fit','webp'} 2x
                                "
                                type="image/webp"
                            >
                            <img
                                src="{$shop->getLogoUrl() |image:80,80}"
                                srcset="
                                    {$shop->getLogoUrl() |image:80,80} 1x,
                                    {$shop->getLogoUrl() |image:160,160} 2x
                                "
                                width="80"
                                height="80"
                                alt="{$shop->getName()}"
                                class="k-profile-header__logo"
                            >
                        </picture>
                    </a>

                    <div class="k-profile-header__content">
                        <h1 class="k-profile-header__title">
                            {if $pageExtension && $pageExtension->getHeading()}
                                {$pageExtension->getHeading()}
                            {else}
                                {_kaufino.city.store.h1, [city => $city->getName(), brand => $shop->getName(), address => $store->getFullStreet()]}
                            {/if}
                        </h1>                        
                    </div>
                </div>

                <h2>{_'kaufino.city.store.openHoursAndContact'}</h2>

                <div class="storeInfo mt-5">
                    <div class="storeInfo__row storeInfo__storeHours closed">
                        <div class="storeInfo__container-hours" n:if="$openingHours = $store->getOpeningHours()">
                            <div class="storeInfo__icon color--green">
                                <span class="icon " data-svg="/assets/icons/time.svg?v=1700491612828"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="none" d="M0 0h24v24H0z"></path><path d="M16.3 3c.*******.5 1.3-.2.5-.8.7-1.3.5-1.1-.5-2.3-.8-3.5-.8-4.4 0-8 3.6-8 8s3.6 8 8 8 8-3.6 8-8c0-1.7-.5-3.3-1.5-4.6-.3-.4-.2-1.1.2-1.4.4-.3 1.1-.2 1.4.2C21.3 7.9 22 9.9 22 12c0 5.5-4.5 10-10 10S2 17.5 2 12 6.5 2 12 2c1.5 0 3 .3 4.3 1zM13 12h2c.6 0 1 .4 1 1s-.4 1-1 1h-3c-.6 0-1-.4-1-1V8c0-.6.4-1 1-1s1 .4 1 1v4z"></path></svg></span>
                            </div>
                                <div class="storeInfo__hours">
                                    <div class="storeInfo__hours color--mid_grey" id="js-storeInfo">
                                        <ul class="storeHours">
                                            <li class="storeHours__item storeHours__header">
                                                <strong class="" n:if="$store->isOpen()">{_'kaufino.city.store.open'}</strong>
                                                <strong class="" n:if="$store->isOpen() === false">{_'kaufino.city.store.closed'}</strong>
                                            </li>
                                            {foreach $store->getDays() as $day => $dayAsNumber}
                                                <li class="storeHours__item {$dayAsNumber === (int) date('N') ? 'storeHours__item--active': 'storeHours__item--inactive'}">
                                                    <span class="storeHours__itemDay">{_app.day.$dayAsNumber.nominative |firstUpper}</span>
                                                    {if isset($openingHours[$day])}
                                                        <span class="storeHours__itemTime">
                                                            {foreach $openingHours[$day] as $hours}                                                        
                                                                {foreach $hours as $hour}{$hour}{sep} / {/sep}{/foreach}                                                    
                                                                {sep}, {/sep}
                                                            {/foreach}
                                                        </span>
                                                    {else}
                                                        {_'kaufino.city.store.closed' |lower}
                                                    {/if}
                                                </li>
                                            {/foreach}
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        <div class="storeInfo__additional">
                            <div class="storeInfo__additional-address">
                                <span>
                                    <img src="{$basePath}/images/icons/place_black_24dp.svg" alt="address">
                                    <span>{$store->getAddress()}</span>
                                </span>
                            </div>
                            <div class="storeInfo__additional-phone" n:if="$store->getPhoneNumber()">
                                <a href="tel:{$store->getPhoneNumber()}">
                                    <img src="{$basePath}/images/icons/phone_black_24dp.svg" alt="phone">
                                    <span>{$store->getPhoneNumber()}</span>
                                </a>
                            </div>
                            <div class="storeInfo__additional-email" n:if="$store->getEmail()">
                                <a href="mailto:{$store->getEmail()}">
                                    <img src="{$basePath}/images/icons/email_black_24dp.svg" alt="email">
                                    <span>                                        
                                        {$store->getEmail()}                                                                                
                                    </span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                {if count($leaflets) > 0}
                    <h2 class="fz-xl fw-regular mb-5 px-3 px-lg-0">{_kaufino.shop.title, [brand => $shop->getName()]|noescape}</h2>
                    <div class="k-leaflets__wrapper k-leaflets__wrapper--3">
                        {foreach $leaflets as $leaflet}
                            {if false && $iterator->counter == 1}
                                <div class="k-leaflets__item mb-3">
                                    <!-- Vypis mesta - Responsive - 2 -->
                                    <ins class="adsbygoogle adslot-1" style="display:block" data-ad-client="ca-pub-4233432057183172" data-ad-slot="2849716931" data-ad-format="square" data-full-width-responsive="true"></ins>

                                    <script>
                                        (adsbygoogle = window.adsbygoogle || []).push({});
                                    </script>
                                </div>
                            {/if}

                            {include '../components/leaflet.latte', leaflet => $leaflet, cssClass => $iterator->counter > 18 ? 'hidden' : ''}
                        {/foreach}
                    </div>

                    <p class="d-flex">                        
                        <a n:href="Shop:shop $shop" class="link ml-auto k-show-more-button js-show-leaflet">{_'kaufino.leaflet.allBrandLeaflets', [brand => $shop->getName()]} »</a>                        
                    </p>
                {/if}                

                {*if $shop->hasActiveCities()}
                    {include cities}
                {/if*}

                <div n:if="count($offersByTag) > 0">
                    <h2 class="fz-xl fw-regular mb-5 px-3 px-lg-0">{_kaufino.city.shop.offers, ['brand' => $shop->getName(), 'city' => $city->getName()]}</h2>

                    <div class="k-offers k-offers--4">
                        {foreach $offersByTag as $offer}
                            {continueIf !$offer->getLeafletPage()}

                            {include '../components/offer-tag.latte', offer => $offer}
                        {/foreach}
                    </div>

                    <div class="d-flex mt-3 mb-5">
                        <a n:href="Offers:offers#offers" class="link ml-auto k-show-more-button">
                            {_'kaufino.showMore.tags'} »
                        </a>
                    </div>
                </div>

                {*if count($similarLeaflets) > 0}
                    <h2 class="fz-xl fw-regular mb-5 px-3 px-lg-0">{_kaufino.city.store.sections.leaflets}</h2>

                    <div class="k-leaflets__wrapper k-leaflets__wrapper--3">
                        {foreach $similarLeaflets as $leaflet}
                            {breakIf $iterator->counter > 6}
                            {include '../components/leaflet.latte', leaflet => $leaflet, cssClass => $iterator->counter > 18 ? 'hidden' : ''}
                        {/foreach}
                    </div>
                {/if*}

                {*
                <p class="k-profile-header__text ml-0">
                    {_kaufino.city.store.text, [brand => $shop->getName(), 'address' => $store->getFullStreet(), 'city' => $city->getName()]}

                    {if count($nearestStores) >= 1}
                        {_'kaufino.city.store.text2', [stores => $otherStores] |noescape}
                    {else}
                        {_'kaufino.city.store.text2WithoutStores'}
                    {/if}
                </p>

                <h2>{_kaufino.city.store.h2bottom, ['brand' => $shop->getName(), 'city' => $city->getName(), 'street' => $store->getFullStreet()]}</h2>

                <div class="k-content ml-0">
                    <p>{_kaufino.city.store.textBottom, ['street' => $store->getFullStreet(), brand => $shop->getName(), 'address' => $store->getFullStreet(), 'city' => $city->getName(), 'fullAddress' => $store->getFullAddress()]}</p>

                    <ul n:if="$similarShops">
                        {foreach $similarShops as $similarShop}
                            <li>
                                <a n:href="City:shop $city, $similarShop">{$similarShop->getName()} {$city->getName()}</a>
                            </li>

                            {breakIf $iterator->counter > 2}
                        {/foreach}
                    </ul>

                    <p>
                        {_kaufino.city.store.textBottom2}
                    </p>

                    {if $pageExtension && $pageExtension->getLongDescription()}
                        <div>{$pageExtension->getLongDescription()}</div>
                    {/if}
                </div>
                *}
            </div>

            <div class="storeContent__storeList">
                <h2 class="fz-l fw-regular mt-0 mb-3 px-3 px-lg-0">
                    {_'kaufino.city.store.h2', [brand => $shop->getName(), city => $city->getName()]}
                </h2>
                <div id="map" class="storeContent__map" style="width: 100%; height: 396px;"></div>

                {* telefon: $store->getPhoneNumber() *}
                {* email: $store->getEmail() *}

                <script n:syntax="double">
                    function renderMap(at, address) {
                        const map = L.map('map').setView(at, 14);
                        let marker = L.marker(at)
                            .bindPopup(address)
                            .addTo(map);

                        const tiles = L.tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png', {
                            maxZoom: 19,
                            attribution: '&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>'
                        }).addTo(map);
                    }
                </script>
                <script>
                    renderMap(
                        [{$store->getLat()}, {$store->getLng()}],
                        {$store->getAddress()}
                    )
                </script>

                <ul class="storeList">
                    {foreach $nearestStores as $nearesStore}
                        <li class="storeList__item">
                            <a n:href="City:store $nearesStore->getCity(), $shop, $nearesStore" class="storeItem">
                                <div class="storeItem__wrapper">   
                                    <span class="storeItem__image">
                                        <img
                                            src="{$shop->getLogoUrl() |image:80,70}"
                                            srcset="
                                                {$shop->getLogoUrl() |image:160,140} 1x,
                                                {$shop->getLogoUrl() |image:320,280} 2x
                                            "
                                            width="80"
                                            height="70"
                                            alt="{$shop->getName()}"
                                            class=""
                                        >
                                    </span>

                                    <div>
                                        <span class="storeItem__content">
                                            {_'kaufino.city.store.store', [fullAddress => $nearesStore->getFullAddress()]}
                                        </span>
                                        <div class="storeItem__distance">
                                            {$distance($nearesStore)} km
                                        </div>
                                    </div>
                                </div>
                                
                                <span class="storeItem__icon color--primary">
                                    <span class="icon " data-svg="/assets/icons/arrow_right.svg?v=1700491612828"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="none" d="M0 0h24v24H0z"></path><path d="M8.8 7.7c-.4-.4-.4-1 0-1.4.4-.4 1.1-.4 1.5 0l4.6 4.3c.8.8.8 2 0 2.8l-4.6 4.3c-.4.4-1.1.4-1.5 0-.4-.4-.4-1 0-1.4l4.6-4.3-4.6-4.3z"></path></svg></span>
                                </span>
                            </a>
                        </li>
                    {/foreach}
                </ul>
            </div>
        </div>
    </div>
</div>

