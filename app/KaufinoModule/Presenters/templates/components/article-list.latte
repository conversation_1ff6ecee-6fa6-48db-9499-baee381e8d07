<div class="o-article-list">
    {foreach $articles as $article}                        
        <div class="o-article-list__item">
            <a n:href="Article:article $article" class="o-article-list__image-wrapper">                    
                <img n:if="$article->getImageUrl()" src="{$article->getImageUrl() |image:270,180,'exact'}" width="270" height="180" alt="{$article->getName()}" loading="lazy">                    
            </a>            
            {*<small class="d-block mt-3 mb-3">{$article->getPublishedAt()|localDate:'long'}</small>*}
            <h3 class="mt-3 mb-3">
                <a n:href="Article:article $article" class="td-hover-underline">
                    {$article->getName()}
                </a>
            </h3>                            
            <p>{$article->getShortDescription()|truncate:150}</p>            
        </div>            
    {/foreach}                    
</div>