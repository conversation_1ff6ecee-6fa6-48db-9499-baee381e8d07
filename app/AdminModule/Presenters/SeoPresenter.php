<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\AdminModule\Presenters;

use Doctrine\ORM\QueryBuilder;
use <PERSON><PERSON><PERSON>\AdminModule\Forms\IPageExtensionControlFactory;
use <PERSON><PERSON><PERSON>\AdminModule\Forms\PageExtensionControl;
use <PERSON><PERSON><PERSON>\Model\Seo\Entities\PageExtension;
use <PERSON><PERSON><PERSON>\Model\Seo\SeoFacade;
use Ka<PERSON>ino\Model\Users\UserFacade;
use Ka<PERSON>ino\Model\Websites\Entities\Website;
use <PERSON><PERSON><PERSON>\Model\Websites\WebsiteFacade;
use Ublaboo\DataGrid\Column\Action\Confirmation\StringConfirmation;

class SeoPresenter extends BasePresenter
{
	/** @var SeoFacade @inject */
	public $seoFacade;

    /** @var UserFacade @inject */
    public $userFacade;

	/** @var WebsiteFacade @inject */
	public $websiteFacade;

	/** @var IPageExtensionControlFactory @inject */
	public $pageExtensionControlFactory;

	public function actionPageExtension($id = null, $websiteId = null, $slug = null)
	{
		if ($id) {
			/** @var Tag $pageExtension */
			$pageExtension = $this->seoFacade->findPageExtension($id);

			if (empty($pageExtension)) {
				$this->error('Page extension not found.');
			}

			$this->template->pageExtension = $pageExtension;
		}
	}

	public function createComponentPageExtensionsGrid($name)
	{
		$pageExtensions = $this->seoFacade->getPageExtensions();

		$grid = $this->dataGridFactory->create()
			->getGrid($this, $name, $pageExtensions);

		$grid->setRememberState(true);
		$grid->setColumnsHideable();
		$grid->setStrictSessionFilterValues(false);

		$grid->setTemplateFile(__DIR__ . '/templates/Seo/grid/default.latte');

		$grid->addToolbarButton(':Admin:Seo:pageExtension')
			->setTitle('Add a new record')
			->setIcon('plus')
			->setClass('btn btn-xs btn-primary');

		$grid->addColumnText('website', 'Website')
			->setRenderer(static function (PageExtension $pageExtension) {
				return $pageExtension->getWebsite()->getName();
			})->setFilterSelect(['' => 'All'] + $this->websiteFacade->findPairs());

		$grid->addColumnText('slug', 'Slug');

		$grid->addFilterText('slug', 'Slug')
			->setCondition(static function (QueryBuilder $qb, $value) {
				$qb->andWhere('p.slug LIKE :slug')->setParameter('slug', '%' . $value . '%');
			});

		$grid->addColumnText('heading', 'Heading');

		$grid->addAction('pageExtension', '', 'pageExtension')->setClass('btn btn-xs btn-primary')->setIcon('pencil');
		$grid->addAction('openPageExtension', '', 'openPageExtension!')->setClass('btn btn-xs btn-primary')->setIcon('search');
		$grid->addAction('deletePageExtension', '', 'deletePageExtension')
			->setClass('btn btn-xs btn-danger')
			->setIcon('trash')
			->setConfirmation(
				new StringConfirmation('Do you really want to delete row %s?', 'slug') // Second parameter is optional
			);

		$grid->setItemsPerPageList([10, 50, 100, 200], false);
		$grid->setDefaultSort(['name' => 'ASC']);
	}

	public function handleOpenPageExtension($id)
	{
		/** @var PageExtension $pageExtension */
		$pageExtension = $this->seoFacade->findPageExtension($id);

		/** @var Website $website */
		$website = $pageExtension->getWebsite();

		$this->redirectUrl($website->getDomain() . '/' . $pageExtension->getSlug());
	}

	public function actionDeletePageExtension($id)
	{
		if ($id) {
			/** @var PageExtension $pageExtension */
			$pageExtension = $this->seoFacade->findPageExtension($id);

			if (empty($pageExtension)) {
				$this->error('Page extension not found.');
			}

			$this->seoFacade->deletePageExtension($pageExtension);

			$this->redirect(':Admin:Seo:default');
		}
	}

	protected function createComponentPageExtensionControl(): PageExtensionControl
	{
		$pageExtension = $this->getParameter('id') ? $this->seoFacade->findPageExtension($this->getParameter('id')) : null;
		$control = $this->pageExtensionControlFactory->create(
			$this->userFacade->find($this->getUser()->getId()),
			$pageExtension,
			$this->getParameter('websiteId'),
			$this->getParameter('slug')
		);

		$control->onSuccess[] = function ($entity, $continue) use ($pageExtension) {
			$this->flashMessage('Successfully saved.');
			if ($pageExtension && !$continue) {
				$this->redirect(':Admin:Seo:default');
			} else {
				$this->redirect(':Admin:Seo:pageExtension', $entity->getId());
			}
		};

		return $control;
	}
}
