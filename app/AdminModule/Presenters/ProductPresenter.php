<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\AdminModule\Presenters;

use Doctrine\ORM\QueryBuilder;
use InvalidArgumentException;
use <PERSON><PERSON><PERSON>\AdminModule\Forms\IProductControlFactory;
use <PERSON><PERSON><PERSON>\AdminModule\Forms\ProductControl;
use Ka<PERSON>ino\AdminModule\Forms\IProductLeafletPageControlFactory;
use Ka<PERSON>ino\AdminModule\Forms\ProductLeafletPageControl;
use <PERSON><PERSON>ino\Model\Localization\Entities\Localization;
use Ka<PERSON>ino\Model\Localization\LocalizationFacade;
use <PERSON><PERSON><PERSON>\Model\Offers\OfferFacade;
use <PERSON><PERSON><PERSON>\Model\Products\Entities\Product;
use Ka<PERSON>ino\Model\Products\ProductFacade;
use Nette\Utils\Json;
use Nette\Utils\Strings;

class ProductPresenter extends BasePresenter
{
	/** @var LocalizationFacade @inject */
	public $localizationFacade;

	/** @var ProductFacade @inject */
	public $productFacade;

	/** @var IProductControlFactory @inject */
	public $productControlFactory;

	/** @var OfferFacade @inject */
	public $offerFacade;

	public function actionProduct($id = null)
	{
		if ($id) {
			/** @var Product $product */
			$product = $this->productFacade->findProduct($id);

			if (empty($product)) {
				$this->error('Product not found.');
			}

			$this->template->product = $product;
		}
	}

	public function createComponentProductsGrid($name)
	{
		$products = $this->productFacade->getProducts(null, false);

		$grid = $this->dataGridFactory->create()
			->getGrid($this, $name, $products);

		$grid->setRememberState(true);
		$grid->setColumnsHideable();
		$grid->setStrictSessionFilterValues(false);

		$grid->setTemplateFile(__DIR__ . '/templates/Product/grid/default.latte');

		$grid->addToolbarButton(':Admin:Product:product')
			->setTitle('Add a new record')
			->setIcon('plus')
			->setClass('btn btn-xs btn-primary');

		$grid->addColumnText('localization', 'Localization')
			->setRenderer(static function (Product $product) {
				return $product->getLocalization()->getName();
			})->setFilterSelect(['' => 'All'] + $this->localizationFacade->findPairs());

		$grid->addColumnText('id', 'Id');

		$grid->addColumnText('name', 'Name')
			->setEditableCallback(function ($id, $value): void {
				$product = $this->productFacade->findProduct($id);
				$product->setName($value);
				$this->productFacade->saveProduct($product);
			});

		$grid->addColumnText('brand', 'Brand')
			->setEditableCallback(function ($id, $value): void {
				$product = $this->productFacade->findProduct($id);
				$product->setBrand($value);
				$this->productFacade->saveProduct($product);
			});

		$grid->addColumnText('manufacturer', 'Manufacturer')
			->setEditableCallback(function ($id, $value): void {
				$product = $this->productFacade->findProduct($id);
				$product->setManufacturer($value);
				$this->productFacade->saveProduct($product);
			});

		$grid->addColumnText('image', 'Image')->setFilterSelect(['' => 'All'] + ['withImage' => 'With image', 'withoutImage' => 'Without image'])
			->setCondition(static function (QueryBuilder $qb, $value) {
				if ($value == 'withImage') {
					$qb->andWhere('o.imageUrl IS NOT NULL');
				} elseif ($value == 'withoutImage') {
					$qb->andWhere('o.imageUrl IS NULL');
				}
			});

		//$grid->addColumnStatus('tag.name', 'Tag');


		$grid->addFilterText('name', 'Name')
			->setCondition(static function (QueryBuilder $qb, $value) {
				$qb->andWhere('o.name LIKE :name')->setParameter('name', '%' . $value . '%');
			});

		$grid->addAction('product', '', 'product')->setClass('btn btn-xs btn-primary')->setIcon('pencil');

		$grid->setItemsPerPageList([10, 50, 100, 200], false);
		$grid->setDefaultSort(['o.name' => 'ASC']);
	}

	protected function createComponentProductControl(): ProductControl
	{
		$product = $this->getParameter('id') ? $this->productFacade->findProduct($this->getParameter('id')) : null;
		$control = $this->productControlFactory->create($product);

		$control->onSuccess[] = function ($entity, $continue) use ($product) {
			$this->flashMessage('Successfully saved.');
			if ($product && !$continue) {
				$this->redirect(':Admin:Product:default');
			} else {
				$this->redirect(':Admin:Product:product', $entity->getId());
			}
		};

		return $control;
	}

	public function actionAssignProduct($offerId, $productId = null, $productName = null)
	{
		$data['message'] = null;

		try {
			if (!$offerId) {
				throw new InvalidArgumentException("Offer is missing.");
			}

			$offer = $this->offerFacade->findOffer($offerId);
			if (!$offer) {
				throw new InvalidArgumentException("Offer is not found with id: " . $offerId);
			}

			if ($productId) {
				$product = $this->productFacade->findProduct($productId);

				if (!$product) {
					throw new InvalidArgumentException("Product is not found with id: " . $productId);
				}
			} else {
				if ($productName) {
					$product = $this->productFacade->findProductByName($offer->getLocalization(), $productName);

					$slug = Strings::webalize($productName);
					if (!$product) {
						if ($this->productFacade->findProductBySlug($offer->getLocalization(), $slug)) {
							throw new InvalidArgumentException("Product slug is duplicate.");
						}

						$product = $this->productFacade->createProduct($offer->getLocalization(), $productName, $slug);
					}
				} else {
					throw new InvalidArgumentException("Missing product name");
				}
			}

			$offer->setProduct($product);

			$data = [
				'success' => true,
			];
		} catch (\Exception $e) {
			$data['success'] = 'false';
			$data['message'] = $e->getMessage();
		}

		$this->sendJson($data);
	}

	public function actionGetOfferToAssignProduct()
	{
		$cz = $this->localizationFacade->findLocalizationByRegion(Localization::REGION_CZECHIA);
		$offer = $this->offerFacade->findOfferToAssignProduct($cz);

		$data['offer'] = null;

		if ($offer) {
			$data['offer'] = [
				'id' => $offer->getId(),
				'name' => $offer->getName(),
				"price" => $offer->getCurrentPrice(),
				"imageUrl" => $offer->getImageUrl(),
			];
		}

		$this->sendJson($data);
	}

	public function actionAutocompleteProductsToAnnotateOffer($offerId = null, $query = null)
	{
		try {
			if (!$offerId) {
				throw new InvalidArgumentException("Offer is missing.");
			}

			$offer = $this->offerFacade->findOffer($offerId);
			if (!$offer) {
				throw new InvalidArgumentException("Offer is not found with id: " . $offerId);
			}

			$products = $this->productFacade->findProductsByFulltext($query, $offer->getLocalization(), 5);

			$data = [];
			foreach ($products as $product) {
				$data[] = [
					'id' => $product->getId(),
					'name' => $product->getName(),
					"imageUrl" => $product->getImageUrl(),
				];
			}

/*            $data[] = [
				'id' => 321,
				'name' => "Produkt 2",
				"imageUrl" => "http://localhost:8000/upload/offers/image/60cf2f8b2153c-680.jpg"
			];*/
		} catch (\Exception $e) {
			$data['success'] = 'false';
			$data['message'] = $e->getMessage();
		}

		$this->sendJson($data);
	}
}
