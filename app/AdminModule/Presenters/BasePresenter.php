<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\AdminModule\Presenters;

use <PERSON><PERSON><PERSON>\AdminModule\Components\IDataGridFactory;
use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Offers\OfferFacade;

abstract class BasePresenter extends \Kaufino\Presenters\BasePresenter
{
	/** @var IDataGridFactory @inject */
	public $dataGridFactory;

	/** @var LeafletFacade @inject */
	public $leafletFacade;

	/** @var OfferFacade @inject */
	public $offerFacade;

	public function startup()
	{
		parent::startup();

		if (!$this->getUser()->isLoggedIn() && $this->getPresenter()->getName() != "Admin:Sign") {
			$this->redirect('Sign:in');
		}

		$this->template->headerCountOfOffersRemaining = $this->offerFacade->getCountOfOffersToConfirm();
		$this->template->headerCountOfleafletPagesRemaining = $this->leafletFacade->getCountOfLeafletPagesToAnnotate();

        bdump($_SERVER);
	}
}
