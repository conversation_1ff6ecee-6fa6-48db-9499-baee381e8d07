{block content}

<div class="container-fluid no-padding">
    <div class="card card-accent-primary">
        <div class="card-body">
            <div class="btn-group" role="group" aria-label="Basic outlined example">
                {foreach $localizations as $localizationOption}
                    <a n:href="this, $localizationOption->getId()" class="btn {$localizationOption === $currentLocalization ? "btn-primary" : "btn-outline-primary"}">{$localizationOption->getName()}</a>
                {/foreach}
            </div>
            <hr>

            {foreach $shops as $shop}
                <h2>{$shop->getName()} <small>| limit: {$shop->getCountOfPagesToUseDetectObjects()}</small></h2>
                {foreach $getTrendingLeafletPages($shop) as $leafletPage}
                    <div class="leaflet-box {$leafletPage->isAnnotated() ? annotated : notAnnotated}">
                        <a n:href="LeafletPage:annotations, $leafletPage->getId()" target="_blank">
                            <img src="{$leafletPage->getImageUrl() |image:140,196,'exactTop'}" width="140" height="196" class="img-responsive lazyload">
                        </a>
                        <div class="leaflet-label">score: {$leafletPage->getScore()}</div>
                        <div n:if="$leafletPage->isAnnotated()" class="leaflet-label"><span class="fa fa-check"></span> annotated</div>
                        <div n:if="!$leafletPage->isAnnotated()" class="leaflet-label"><span class="fa fa-times"></span> not annotated</div>
                    </div>
                {/foreach}
                <div class="clearfix"></div>
                <hr>
            {/foreach}

        </div>
    </div>
</div>

<style>
    .leaflet-box {
        float:left;
        margin-right:15px;
        margin-bottom:15px;
    }
    .leaflet-box .leaflet-label {
        width:100%;
        font-weight:bold;
        font-size:12px;
        padding:1px;
    }
    .leaflet-box.notAnnotated {
        background:rgba(155,0,0,0.4);
    }
    .leaflet-box.annotated {
        background:rgba(0,155,0,0.4);
    }
</style>
