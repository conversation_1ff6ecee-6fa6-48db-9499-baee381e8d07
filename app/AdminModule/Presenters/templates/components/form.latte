{define form $formName}
	<form n:name=$formName>
	<ul class=error n:ifcontent>
		<li n:foreach="$form->ownErrors as $error">{$error}</li>
	</ul>

	<table>
	<tr n:foreach="$form->controls as $input"
		n:if="!$input->getOption(rendered) && $input->getOption(type) !== hidden"
		n:class="$input->required ? required">

		<th>{label $input /}</th>
		<td>{input $input} <span class=error n:ifcontent>{$input->error}</span></td>
	</tr>
	</table>
	</form>
{/define}


{* for Bootstrap v3 *}
{define bootstrap-form $formName}
	<form n:name=$formName class=form-horizontal>
	<ul class=error n:ifcontent>
		<li n:foreach="$form->ownErrors as $error">{$error}</li>
	</ul>

	<div n:foreach="$form->controls as $name => $input"
		n:if="!$input->getOption(rendered) && $input->getOption(type) !== hidden"
		n:class="form-group, $input->required ? required, $input->error ? has-error">

		<div class="col-sm-2 control-label">{label $input /}</div>

		<div class="col-sm-10">
			{if $input->getOption(type) in [text, select, textarea]}
				{input $input class => form-control}
			{elseif $input->getOption(type) === button}
				{input $input class => "btn btn-default"}
			{elseif $input->getOption(type) === checkbox}
				<div class="checkbox">{input $input}</div>
			{elseif $input->getOption(type) === radio}
				<div class="radio">{input $input}</div>
			{else}
				{input $input}
			{/if}

			<span class=help-block n:ifcontent>{$input->error ?: $input->getOption(description)}</span>
		</div>
	</div>
	</form>
{/define}
