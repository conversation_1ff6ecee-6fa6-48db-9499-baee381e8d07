<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\AdminModule\Forms;

use <PERSON><PERSON><PERSON>\Model\Offers\Entities\Offer;
use <PERSON><PERSON>ino\Model\Offers\OfferAutocomplete;
use <PERSON><PERSON><PERSON>\Model\Offers\OfferFacade;
use <PERSON><PERSON><PERSON>\Model\Tools;
use Ka<PERSON>ino\Model\Users\UserIdentity;
use Nette;
use Nette\Application\UI\Form;
use Nette\InvalidArgumentException;
use Nette\Utils\Strings;

class OfferLeafletPageControl extends Nette\Application\UI\Control
{
	public array $onSuccess = [];
    private ?Offer $offer;
    private OfferFacade $offerFacade;
    private UserIdentity $userIdentity;
    private OfferAutocomplete $offerAutocomplete;

    public function __construct(?Offer $offer, OfferFacade $offerFacade, UserIdentity $userIdentity, OfferAutocomplete $offerAutocomplete)
	{
        $this->offer = $offer;
        $this->offerFacade = $offerFacade;
        $this->userIdentity = $userIdentity;
        $this->offerAutocomplete = $offerAutocomplete;
    }

	public function createComponentForm(): Form
    {
		$form = new Form();

		$form->addText('name', 'Name:')
			->setHtmlAttribute('autofocus', '1');

		$form->addText('currentPrice', 'Price:');

		$form->addText('commonPrice', 'Common price:');

		$form->addTextArea('description', 'Description:');

		$form->addTextArea('ocrOutput', 'OCR:', null, 8);

		$form->addHidden('startTime', time());

		$form->addSubmit('submit', 'Confirm');
		$form->addSubmit('archive', 'Archive');

		if ($this->offer) {
			$form->setDefaults(
				[
					'name' => $this->offer->getName(),
					'commonPrice' => $this->offer->getCommonPrice(),
					'currentPrice' => $this->offer->getCurrentPrice(),
					'description' => $this->offer->getDescription(),
					'ocrOutput' => $this->offer->getOcrOutput(),
				]
			);
		}

		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function formSucceeded(Form $form, $values)
	{
		try {
			$offer = $this->offer;

			if ($form['submit']->isSubmittedBy()) {
				$name = trim($values->name);
				$currentPrice = Tools::stringToFloat($values->currentPrice);
				$commonPrice = Tools::stringToFloat($values->commonPrice);

				if (!$name) {
					throw new InvalidArgumentException('Name must be filled in.');
				}

				if ($name == 'Waiting for name') {
					throw new InvalidArgumentException('Waiting for name is invalid name.');
				}

				if (!$currentPrice) {
					throw new InvalidArgumentException('Current price must be filled in.');
				}

				if ($commonPrice > 0 && $currentPrice >= $commonPrice) {
					throw new InvalidArgumentException('Current price must be better than common price.');
				}

				$offer->setName($name);
				$offer->setSlug(Strings::webalize($offer->getName() . '-' . $offer->getId()));
				$offer->setCommonPrice($commonPrice);
				$offer->setCurrentPrice($currentPrice);
				$offer->setDescription($values->description);
				$offer->setTimeToCreate(time() - $values->startTime);
				$offer->confirm();
			} else {
				$offer->archive();
			}

			$offer->setAuthor($this->userIdentity->getIdentity());
			$this->offerFacade->saveOffer($offer);

			$this->onSuccess($offer);
		} catch (InvalidArgumentException $e) {
			$form->addError($e->getMessage());
		}
	}

	public function render()
	{
		$this->template->setFile(__DIR__ . '/control.latte');
		$this->template->render();
	}
}


interface IOfferLeafletPageControlFactory
{
	public function create(Offer $offer = null): OfferLeafletPageControl;
}
