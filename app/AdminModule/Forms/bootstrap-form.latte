{define bootstrap-head}
	<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css" integrity="sha384-1q8mTJOASx8j1Au+a5WDVnPi2lkFfwwEAa8hDDdjZlpLegxhjVME1fgjWPGmkzs7" crossorigin="anonymous">
{/define}

{define bootstrap-form $form, $labelSize, $controlSize}
	<form n:name=$form style="margin-top:15px;">
    <div class="form-group" n:if="$form->ownErrors">
        <div class="col-md-12">
            <ul class="errors">
                <li n:foreach="$form->ownErrors as $error" class="text-danger">{$error}</li>
            </ul>
        </div>
    </div>

    {var $labelSize = (isset($labelSize) ? $labelSize : 2)}
    {var $controlSize = (isset($controlSize) ? $controlSize : 10)}

    <div class="col-md-12">
        {if !empty($form->getGroups())}
            {foreach $form->getGroups() as $group}
                {if $group->getOption('label')}
                    <h4>{$group->getOption('label') |noescape}</h4>
                {/if}
                {include bootstrap-form-controls $group->getControls(), $labelSize, $controlSize}
                <div class="clearfix"></div>
            {/foreach}
        {else}
            {include bootstrap-form-controls $form->getControls(), $labelSize, $controlSize}
        {/if}
    </div>

	</form>
{/define}


{define bootstrap-form-controls $controls, $labelSize, $controlSize}
    <div n:foreach="$controls as $name => $input"
        n:if="!$input->getOption(rendered) && $input->getOption(type) !== hidden"
        n:class="form-group, $input->required ? required, $input->error ? has-error, row">

        {var $labelSize = (isset($labelSize) ? $labelSize : 2)}
        {var $controlSize = (isset($controlSize) ? $controlSize : 10)}

        <div class="col-sm-{$labelSize} control-label">{label $input}</div>

        <div class="col-sm-{$controlSize}">
            {if $input->getOption(type) in [text, select, textarea]}
                {input $input class => $input->getControlPrototype()->class . ' form-control', autocomplete => "off",}
            {elseif $input->getOption(type) === button}
                {input $input class => $input->getControlPrototype()->class . ' btn btn-primary'}
            {elseif $input->getOption(type) === checkbox}
                <div class="checkbox-wrapper">{input $input, class => "form-checkbox"}</div>
            {elseif $input->getOption(type) === radio}
                <div class="radio">{input $input}</div>
            {else}
                {input $input}
            {/if}

            <span class=has-error n:ifcontent>{$input->error}</span>
        </div>
        {if method_exists($input, "getCustomHtml") && $input->getCustomHtml() !== nulls}
            {$input->getCustomHtml() |noescape}
        {/if}
    </div>
{/define}
