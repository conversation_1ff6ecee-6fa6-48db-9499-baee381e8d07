{form form}
<div class="container">
    <div class="row justify-content-center">
        <div class="card border-light">
            <div class="card-body">
                <h5 class="card-title">
                    Edit user
                </h5>

                <div class="mb-3" n:if="$form->getErrors()">
                   <div class="alert-danger alert">
                      <span n:foreach="$form->getErrors() as $error">
                          {$error}{sep}<br>{/sep}
                      </span>
                   </div>
                </div>

                <div class="mb-3">
                    {label localization, class: "form-label"}
                    {input localization, class: "form-control"}
                </div>

                <div class="mb-3">
                    {label email, class: "form-label"}
                    {input email, class: "form-control"}
                </div>

                <div class="mb-3">
                    {label password, class: "form-label"}
                    {input password, class: "form-control"}
                    <small class="form-text text-muted" n:if="$user">Leave empty if you don't want to change the password. Minimum length is 6 characters, otherwise password will not be saved.</small>
                </div>

                <div class="mb-3">
                    {label name, class: "form-label"}
                    {input name, class: "form-control"}
                </div>

                <div class="mb-3">
                    {label surname, class: "form-label"}
                    {input surname, class: "form-control"}
                </div>

                <div class="mb-3 ">
                    {label image, class: "form-label"}

                    <div class="d-flex align-items-center gap-2">
                        <div n:if="$user && $user->getImageUrl()">
                            <img src="{$user->getImageUrl()}" alt="image" class="img-thumbnail" style="max-width: 64px; max-height: 48px">
                        </div>
                        <div>
                            {input image, class: "form-control"}
                        </div>
                    </div>
                </div>

                <div class="mb-3 form-check">
                    {input active:input, class: "form-check-input"}
                    <label class="form-check-label" for="frm-tagControl-form-active">
                        Active?
                    </label>
                </div>

                <div class="d-grid gap-2 d-md-flex">
                    {input submit, class: "btn btn-primary"}
                    {input submitAndContinue, class: "btn btn-primary"}
                </div>
            </div>
        </div>
    </div>
</div>
{/form}