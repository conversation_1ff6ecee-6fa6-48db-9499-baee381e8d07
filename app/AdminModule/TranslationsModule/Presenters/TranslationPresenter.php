<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\AdminModule\TranslationsModule\Presenters;

use <PERSON><PERSON><PERSON>\AdminModule\Presenters\BasePresenter;
use <PERSON><PERSON><PERSON>\Model\Github\GithubClient;
use <PERSON><PERSON><PERSON>\Model\Localization\Entities\Localization;
use <PERSON><PERSON><PERSON>\Model\Localization\LocalizationFacade;
use <PERSON><PERSON><PERSON>\Model\Translations\TranslationFacade;

class TranslationPresenter extends BasePresenter
{
	/** @var GithubClient @inject */
	public $githubClient;

	/** @var LocalizationFacade @inject */
	public $localizationFacade;

	/** @var TranslationFacade @inject */
	public $translationFacade;

	public function renderDefault()
	{
		$this->template->localizations = $this->localizationFacade->findLocalizations();

		$this->template->getDictionaryList = function (Localization $localization) {
			return $this->translationFacade->getDictionaryList($localization);
		};
	}
}
