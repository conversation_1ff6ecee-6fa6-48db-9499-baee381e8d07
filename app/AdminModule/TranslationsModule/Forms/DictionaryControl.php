<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\AdminModule\TranslationsModule\Forms;

use <PERSON><PERSON><PERSON>\Model\Github\GithubClient;
use Nette;
use Nette\Application\UI\Form;
use <PERSON><PERSON><PERSON>\Model\Configuration;
use <PERSON><PERSON><PERSON>\Model\Localization\Entities\Localization;
use <PERSON><PERSON><PERSON>\Model\Translations\TranslationFacade;
use Nette\InvalidArgumentException;

class DictionaryControl extends Nette\Application\UI\Control
{
	/** @var array  */
	public $onSuccess = [];

	/** @var string */
	private $dictionaryName;

	/** @var Localization */
	private $localization;

	/** @var string|null */
	private $branch;

	/** @var TranslationFacade */
	private $translationFacade;

	/** @var Configuration */
	private $configuration;

	public function __construct(string $dictionaryName, Localization $localization, ?string $branch, TranslationFacade $translationFacade, Configuration $configuration)
	{
		$this->dictionaryName = $dictionaryName;
		$this->localization = $localization;
		$this->branch = $branch;
		$this->translationFacade = $translationFacade;
		$this->configuration = $configuration;
	}

	public function createComponentForm(): Form
	{
		$form = new Form();

		$form->addSubmit('submit');

		$form->onSubmit[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function formSucceeded(Form $form)
	{
		try {
			$data = $form->getHttpData();

			if (isset($data['translation'])) {
				$translationData = $data['translation'];
			} else {
				throw new InvalidArgumentException('Nelze zpracovat. Kontaktujte správce.');
			}

			try {
				$comment = 'Dictionary was updated.';
				//$comment = 'úpravy provedeny uživatelem: ' . $this->user->getUserName() . ' (' . $this->user->getId() . ')';
				if ($this->configuration->isDevelopmentMode()) {
					$comment .= ' (localhost)';
				}

				$this->translationFacade->updateDictionary($this->localization, $this->dictionaryName, $translationData, GithubClient::DEFAULT_BRANCH, $comment);
			} catch (\Exception $e) {
				throw new InvalidArgumentException('Nelze zpracovat. Kontaktujte správce.');
			}

			$this->onSuccess();
		} catch (InvalidArgumentException $e) {
			$form->addError($e->getMessage());
		}
	}

	public function render()
	{
		$this->template->setFile(__DIR__ . '/control.latte');
		$this->template->render();
	}
}


interface IDictionaryControlFactory
{
	public function create(string $dictionaryName, Localization $localization, ?string $branch): DictionaryControl;
}
