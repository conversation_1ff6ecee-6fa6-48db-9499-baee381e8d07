version: "3.7"
services:
    database:
        image: mariadb:latest
        volumes:
            - ./.docker/database:/docker-entrypoint-initdb.d:cached,ro
        environment:
            MYSQL_ROOT_PASSWORD: root
            #            MYSQL_USER: root
            MYSQL_PASSWORD: root
            MYSQL_DATABASE: kaufino

    web:
        container_name: kaufinoWebserver
        build:
            context: .
            dockerfile: Dockerfile
        environment:
            NETTE_DEBUG: dd72daf3c3654258475a34a21
        #            PHP_IDE_CONFIG: serverName=localhost
        #            DOCKER_DEV: 'true'
        #            XDEBUG_CONFIG: "remote_host=************"
        ports:
            - "127.0.0.1:8087:80"
            - "127.0.0.1:445:443"
        extra_hosts:
            - "kaufino.dev:127.0.0.1"
        hostname: kaufino.dev
        domainname: dev
        volumes:
            - .:/project
            - ./.docker/apache/certs:/etc/apache2/ssl
            - ./.docker/apache/sites-enabled:/etc/apache2/sites-enabled
#            - ./.docker/php/xdebug.ini:/usr/local/etc/php/conf.d/xdebug.ini
        depends_on:
            - database
#
#    adminer:
#        image: adminer
#        ports:
#            - 8081:8080
#        depends_on:
#            - db
