<?php

namespace Tests\Acceptance;

use Tests\Support\AcceptanceTester;

class SitemapCest
{
    /**
     * @env kaufino
     * @env letado
     */
    public function testSitemap(AcceptanceTester $I): void
    {
        $regions = ['cz', 'sk', 'pl', 'hu', 'ro', 'hr', 'it'];

        foreach ($regions as $region) {
            $I->amOnPage('/' . $region . '/sitemap.xml');
            $I->seeResponseCodeIs(200);
        }
    }

}