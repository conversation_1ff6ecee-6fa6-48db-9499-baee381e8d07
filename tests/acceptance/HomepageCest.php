<?php

namespace Tests\Acceptance;

use Tests\Support\AcceptanceTester;

class HomepageCest
{
    public function testKaufinoHomepage(AcceptanceTester $I): void
    {
        $I->amOnPage('/cz/');
        $I->seeResponseCodeIs(200);
        $I->see('Akční letáky', 'h1');

        $I->amOnPage('/sk/');
        $I->seeResponseCodeIs(200);
        $I->see('Akciové letáky', 'h1');

        $I->amOnPage('/pl/');
        $I->seeResponseCodeIs(200);
        $I->see('Gazetki promocyjne', 'h1');

        $I->amOnPage('/ro/');
        $I->seeResponseCodeIs(200);
        $I->see('Cataloage', 'h1');

        $I->amOnPage('/hu/');
        $I->seeResponseCodeIs(200);
        $I->see('<PERSON>k<PERSON><PERSON>', 'h1');
    }

   /* public function testLetadoHomepage(AcceptanceTester $I): void
    {
        $I->amOnPage('/cz/');
        $I->seeResponseCodeIs(200);
        $I->see('Nejnovější letáky a zboží v akci', 'h1');

        $I->amOnPage('/sk/');
        $I->seeResponseCodeIs(200);
        $I->see('Najnovšie letáky a produkty v akcii', 'h1');

        $I->amOnPage('/pl/');
        $I->seeResponseCodeIs(200);
        $I->see('Najnowsze gazetki i produkty w promocji', 'h1');

        $I->amOnPage('/ro/');
        $I->seeResponseCodeIs(200);
        $I->see('Cele mai noi cataloage și produse în ofertă', 'h1');

        $I->amOnPage('/hu/');
        $I->seeResponseCodeIs(200);
        $I->see('Legújabb akciós újságok és akciós termékek', 'h1');
    }*/
}