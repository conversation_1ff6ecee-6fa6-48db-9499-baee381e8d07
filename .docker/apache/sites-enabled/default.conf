<VirtualHost *:80>
	ServerName kaufino.dev
	ServerAlias kaufino.dev

	## Vhost docroot
	DocumentRoot "/project/www"
	<Directory "/project/www">
		Options Indexes FollowSymLinks MultiViews
		AllowOverride all
		Require all granted
	</Directory>
</VirtualHost>


<VirtualHost *:443>
	ServerName kaufino.dev
	ServerAlias kaufino.dev
	DocumentRoot "/project/www"

	<Directory "/project/www">
		Options Indexes FollowSymLinks MultiViews
		AllowOverride all
		Require all granted
	</Directory>

	SSLEngine on
	SSLCertificateFile /etc/apache2/ssl/cert.pem
	SSLCertificateKeyFile /etc/apache2/ssl/cert-key.pem
</VirtualHost>
